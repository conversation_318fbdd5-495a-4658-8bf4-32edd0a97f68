import { <PERSON><PERSON>, Col, Row } from 'antd';

import { useNavigate } from 'react-router-dom';
import { ReactComponent as NotificationIcon } from '../../../../assets/icons/notification.svg';
import { ReactComponent as SettingsIcon } from '../../../../assets/icons/settings-outline.svg';
import { canAccess } from '../../../../common/canAccess';
import { useStoreState } from '../../../../store';
import { UserRole } from '../../../../store/user/types/User';
import Avatar from '../../shared/Avatar';
import { useNotifications } from '../../../notifications/NotificationsContext';
import NotificationsModal from '../../../notifications/NotificationsModal';
import { useMemo } from 'react';
import classNames from 'classnames';

const Actions: React.FC = () => {
    const { user } = useStoreState((state) => state.auth);
    const { unreadCount } = useStoreState((state) => state.notification);

    const unreadLabel = useMemo(() => {
        if (unreadCount < 10) {
            return unreadCount;
        } else {
            return '9+';
        }
    }, [unreadCount]);

    const { showModal, setShowModal } = useNotifications();

    const navigate = useNavigate();

    return (
        <Row className="actions" align="middle">
            <Col>
                <Row className="actions-icons">
                    <Col onClick={() => setShowModal(!showModal)}>
                        <Button type="link">
                            <NotificationIcon />
                            {!!unreadCount && (
                                <div
                                    className={classNames('unread-count', {
                                        'has-many': unreadCount >= 10,
                                    })}
                                >
                                    <span>{unreadLabel}</span>
                                </div>
                            )}
                        </Button>
                    </Col>
                    {canAccess([UserRole.SUPER_ADMIN]) && (
                        <Col>
                            <Button type="link" onClick={() => navigate('/settings')}>
                                <SettingsIcon />
                            </Button>
                        </Col>
                    )}
                </Row>
            </Col>
            {user && (
                <Col>
                    <Row
                        onClick={() => navigate('/account')}
                        className="actions-avatar"
                        align="middle"
                        justify="space-between"
                    >
                        <Col className="actions-avatar-info">
                            <div>
                                <b>{user.name}</b>
                            </div>
                            <div>{user.role}</div>
                        </Col>
                        <Col>
                            <Avatar size="small" photoUrl={user.profilePhoto} name={user.name} />
                        </Col>
                    </Row>
                </Col>
            )}
            {showModal && (
                <NotificationsModal show={showModal} onClose={() => setShowModal(false)} />
            )}
        </Row>
    );
};

export default Actions;
