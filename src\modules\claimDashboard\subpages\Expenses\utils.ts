import { MenuProps } from 'antd';
import { makeFilterParam } from '../../../../utils/paginate';
import {
    ExpenseFilterParam,
    ExpenseFitlerableFields,
    RawExpenseStatus,
} from '../../../../store/expense/types/Expense';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';
import { ExpenseCategory } from '../../../../types';
import { capitalizeString } from '../../../../utils/strings';
import {
    ExpenseAddDto,
    ExpenseDto,
    ExpenseFilterDto,
} from '../../../../store/expense/dto/ExpenseDto';
import { plainToInstance } from 'class-transformer';
import {
    ReviewersCommentsType,
    ReviewersCommentsTypeApproved,
    ReviewersCommentsTypeDeclined,
    ReviewersCommentsTypeInReview,
    ReviewersCommentsTypePartialApproved,
} from '../../../../common/ReviewersCommentsType';
import { fromRawStatusMap, toRawStatusMap } from '../../../../store/expense/mappers';

export enum ExpenseActions {
    REQUEST_RECEIPT = 'Request Receipt',
    EDIT = 'Edit',
    ARCHIVE = 'Archive',
    VIEW = 'View',
    RESTORE = 'Restore',
}

export enum ExpenseStatus {
    SUBMITTED = 'Submitted',
    IN_REVIEW = 'In Review',
    AUDITED = 'Audited',
}

export const actionMenuItems: MenuProps['items'] = [
    {
        label: ExpenseActions.VIEW,
        key: ExpenseActions.VIEW,
    },
    {
        label: ExpenseActions.REQUEST_RECEIPT,
        key: ExpenseActions.REQUEST_RECEIPT,
    },
    {
        label: ExpenseActions.EDIT,
        key: ExpenseActions.EDIT,
    },
    {
        label: ExpenseActions.ARCHIVE,
        key: ExpenseActions.ARCHIVE,
    },
];

export const archivedActionMenuItems: MenuProps['items'] = [
    {
        label: ExpenseActions.VIEW,
        key: ExpenseActions.VIEW,
    },
    {
        label: ExpenseActions.RESTORE,
        key: ExpenseActions.RESTORE,
    },
];

export const statusMenuItems: MenuProps['items'] = [
    {
        label: ExpenseStatus.SUBMITTED,
        key: RawExpenseStatus.SUBMITTED,
    },
    {
        label: ExpenseStatus.IN_REVIEW,
        key: RawExpenseStatus.IN_REVIEW,
    },
    {
        label: ExpenseStatus.AUDITED,
        key: RawExpenseStatus.AUDITED,
    },
];

export const makeExpenseFilterParam = makeFilterParam<ExpenseFitlerableFields>;

export const ExpenseStatusFilter = {
    ...RawExpenseStatus,
    ALL: 'ALL',
    ARCHIVED: 'ARCHIVED',
    RECENT: 'RECENT',
};

export type ExpenseStatusFilterType =
    (typeof ExpenseStatusFilter)[keyof typeof ExpenseStatusFilter];

export const getReadableExpenseCategory = (category: ExpenseCategory) => {
    return capitalizeString(category.replaceAll('_', ' '));
};

export const FilterExpenseCategoryOptions: SelectBoxOption[] = Object.values(ExpenseCategory).map(
    (category) => ({
        label: getReadableExpenseCategory(category),
        value: category as string,
    }),
);

export const AddExpenseCategoryOptions: SelectBoxOption[] = Object.values(ExpenseCategory).map(
    (category) => ({
        label: getReadableExpenseCategory(category),
        value: category as string,
    }),
);

export const mapFilterParams = (filterParms: ExpenseFilterParam[]): ExpenseFilterDto => {
    const category = filterParms.find((param) => param.field === 'category');
    const dateRange = filterParms.find((param) => param.field === 'date');
    const submittedRange = filterParms.find((param) => param.field === 'submitted_amount');
    const approvedRange = filterParms.find((param) => param.field === 'approved_amount');

    if (typeof dateRange?.value[0] === 'string' && typeof dateRange?.value[1] === 'string') {
        new Date(dateRange?.value[0]);
        new Date(dateRange?.value[1]);
    }

    return plainToInstance(ExpenseFilterDto, {
        category: category?.value,
        dateRange: dateRange?.value ? [dateRange.value[0], dateRange.value[1]] : undefined,
        submittedRange: submittedRange?.value || undefined,
        approvedRange: approvedRange?.value || undefined,
    });
};

export type UploadStatus = 'idle' | 'ready' | 'inProgress' | 'completed';

export enum AuditedType {
    APPROVED = 'approved',
    PARTIAL = 'partial',
    DECLINED = 'declined',
}

export interface ExpenseAddEntry {
    added?: boolean;
    data: ExpenseAddDto;
}

export const MAX_FILE_UPLOADS = 7;

export const isOtherField = (reviewerCommentType?: ReviewersCommentsType): boolean => {
    return (
        ReviewersCommentsType.IN_REVIEW_OTHER === reviewerCommentType ||
        ReviewersCommentsType.APPROVED_OTHER === reviewerCommentType ||
        ReviewersCommentsType.APPROVED_PARTIAL_OTHER === reviewerCommentType ||
        ReviewersCommentsType.NOT_APPROVED_OTHER === reviewerCommentType
    );
};

export const getReviewerCommentByStatus = (
    status?: RawExpenseStatus,
    expense?: ExpenseDto,
    reviewersCommentType?: ReviewersCommentsType,
    auditedType?: AuditedType,
) => {
    if (status === RawExpenseStatus.SUBMITTED) {
        return undefined;
    }

    if ((status && fromRawStatusMap[status]) === expense?.status && reviewersCommentType) {
        return ReviewersCommentsType[reviewersCommentType];
    }
    if (status === RawExpenseStatus.AUDITED) {
        if (auditedType === AuditedType.PARTIAL) {
            return ReviewersCommentsType.APPROVED_PARTIAL;
        }
        if (auditedType === AuditedType.DECLINED) {
            return ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE;
        }
        return ReviewersCommentsType.APPROVED_BREAKFAST;
    }

    if (status === RawExpenseStatus.IN_REVIEW) {
        return ReviewersCommentsType.IN_REVIEW_DUPLICATE;
    }

    return undefined;
};

export const getReviewerCommentsByStatusToSelectBox = (
    status: RawExpenseStatus,
    auditedType?: AuditedType,
): SelectBoxOption[] => {
    if (status === RawExpenseStatus.AUDITED) {
        if (auditedType === AuditedType.DECLINED) {
            return Object.keys(ReviewersCommentsTypeDeclined).map((item): SelectBoxOption => {
                return {
                    label: ReviewersCommentsTypeDeclined[item],
                    value: item,
                };
            });
        }

        if (auditedType === AuditedType.PARTIAL) {
            return Object.keys(ReviewersCommentsTypePartialApproved).map(
                (item): SelectBoxOption => {
                    return {
                        label: ReviewersCommentsTypePartialApproved[item],
                        value: item,
                    };
                },
            );
        }

        return Object.keys(ReviewersCommentsTypeApproved).map((item): SelectBoxOption => {
            return {
                label: ReviewersCommentsTypeApproved[item],
                value: item,
            };
        });
    }

    if (status === RawExpenseStatus.IN_REVIEW) {
        return Object.keys(ReviewersCommentsTypeInReview).map((item): SelectBoxOption => {
            return {
                label: ReviewersCommentsTypeInReview[item],
                value: item,
            };
        });
    }

    return Object.keys(ReviewersCommentsTypeDeclined).map((item): SelectBoxOption => {
        return {
            label: ReviewersCommentsTypeDeclined[item],
            value: item,
        };
    });
};

export const getReviewerCommentValueByStatus = (
    status: RawExpenseStatus,
    type?: ReviewersCommentsType,
    auditedType?: AuditedType,
): string => {
    if (!type) {
        return '';
    }
    console.log('<<<<<<<<<<<<type', type);
    console.log('<<<<<<<<<<<<auditedType', auditedType);
    console.log('<<<<<<<<<<<<status', status);
    if (status === RawExpenseStatus.AUDITED) {
        if (auditedType === AuditedType.PARTIAL) {
            return ReviewersCommentsTypePartialApproved[type];
        }
        if (auditedType === AuditedType.DECLINED) {
            return ReviewersCommentsTypeDeclined[type];
        }
        return ReviewersCommentsTypeApproved[type];
    }

    if (status === RawExpenseStatus.IN_REVIEW) {
        return ReviewersCommentsTypeInReview[type];
    }

    return '';
};

export const getInitialReviewersComments = (
    expense: ExpenseDto,
    status?: RawExpenseStatus,
): SelectBoxOption[] => {
    if (!status) {
        return [];
    }
    if (
        status !== toRawStatusMap[ExpenseStatus.AUDITED] ||
        (!expense.approvedAmount && !expense.declinedAmount)
    ) {
        return getReviewerCommentsByStatusToSelectBox(status);
    }
    if (
        expense.approvedAmount &&
        expense.submittedAmount &&
        expense.approvedAmount >= expense.submittedAmount
    ) {
        return getReviewerCommentsByStatusToSelectBox(status);
    }
    if (
        expense.declinedAmount &&
        expense.submittedAmount &&
        expense.declinedAmount >= expense.submittedAmount
    ) {
        return getReviewerCommentsByStatusToSelectBox(status, AuditedType.DECLINED);
    }
    return getReviewerCommentsByStatusToSelectBox(status, AuditedType.PARTIAL);
};

export const getAuditedTypeByReviewsCommentType = (
    commentType?: ReviewersCommentsType,
): AuditedType | undefined => {
    return commentType?.startsWith('APPROVED_PARTIAL')
        ? AuditedType.PARTIAL
        : commentType?.startsWith('NOT_APPROVED')
        ? AuditedType.DECLINED
        : undefined;
};
