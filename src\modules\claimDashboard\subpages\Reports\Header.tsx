import { Button } from 'antd';
import { useState } from 'react';
import { ReactComponent as IconAdd } from '../../../../assets/icons/add-sharp.svg';
import AddModal from './AddModal';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { useLocation } from 'react-router-dom';
import { canAccess } from '../../../../common/canAccess';
import { UserRole } from '../../../../store/user/types/User';
import { isOpen } from '../../../../common/claim';
import ReportRequest from './ReportRequest';

interface Props {
    claim: ClaimDto;
    onAddSuccess: VoidFunction;
}

const Header: React.FC<Props> = ({ claim, onAddSuccess }) => {
    const location = useLocation();

    const isNewReport = location.pathname.includes('/new');
    const isReportRequest = location.pathname.includes('/request');
    const isNewReportRequest = location.pathname.includes('/new-request');

    const [showAddModal, setShowAddModal] = useState(isNewReport);
    // const [showReportRequest, setShowReportRequest] = useState(isReportRequest);
    const [showNewReportRequest, setShowNewReportRequest] = useState(isNewReportRequest);

    return (
        <div className="claim-reports-header margin-bottom-24">
            <div>
                <div className="title">Reports</div>
                <div className="info">Track reports in the policyholder&apos;s claim.</div>
            </div>
            {claim.archivedAt ||
            !canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) ||
            (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN])) ||
            isReportRequest ? null : (
                <Button
                    className="btn-primary flex-space"
                    size="large"
                    onClick={() => setShowAddModal(true)}
                >
                    <IconAdd className="margin-right-16" />
                    Create Report
                </Button>
            )}
            {claim.archivedAt ||
            !canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) ||
            (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN])) ||
            !isReportRequest ? null : (
                <Button
                    className="btn-primary flex-space"
                    size="large"
                    onClick={() => setShowNewReportRequest(true)}
                >
                    <IconAdd className="margin-right-16" />
                    Report Request
                </Button>
            )}
            {showAddModal && (
                <AddModal
                    claim={claim}
                    show={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    onSuccess={() => {
                        setShowAddModal(false);
                        onAddSuccess();
                    }}
                />
            )}
            {showNewReportRequest && (
                <ReportRequest
                    claim={claim}
                    show={showNewReportRequest}
                    onClose={() => setShowNewReportRequest(false)}
                    onSuccess={() => {
                        setShowNewReportRequest(false);
                        onAddSuccess();
                    }}
                />
            )}
        </div>
    );
};
export default Header;
