.auth-form {
    @include flex(flex, column, wrap, flex-start, center);
    background-color: $color-background-secondary;
    gap: $margin;
    width: 430px;
    padding: $padding;
    text-align: center;

    &-actions {
        width: 100%;
    }

    &-code-verification-text {
        font-size: 16px;
        font-weight: $font-weight-light;
        text-align: center;
    }

    &-forgot-password {
        align-self: flex-start;
        font-weight: $font-weight-semi-bold;
        font-size: $font-size;
        line-height: 130%;
        padding: 0;
    }

    &-password {
        margin-bottom: 16px !important;
    }

    &-contact {
        height: 18px;
        position: absolute;
        right: 0;
        top: 16px;
        cursor: pointer;
    }

    &-resend-code-link {
        font-weight: $font-weight-semi-bold;
        font-size: 14px;
        line-height: 130%;
    }

    &-terms-and-conditions {
        @include flex(flex, row, nowrap, flex-start, center);
        align-self: flex-start;
        display: flex !important;
        flex: 1;
        width: 100%;
    }
}
