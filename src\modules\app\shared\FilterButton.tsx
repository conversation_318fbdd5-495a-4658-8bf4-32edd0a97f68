import { Button } from 'antd';
import classNames from 'classnames';
import { ReactComponent as IconFilter } from '../../../assets/icons/icon-filter.svg';

interface Props {
    onClear: VoidFunction;
    onClick: VoidFunction;
    filterCount: number;
    className?: string;
}

const FilterButton: React.FC<Props> = ({ onClear, onClick, filterCount, className }) => {
    return (
        <div className={classNames('btn-filter-container', className)}>
            <Button
                className={classNames('btn-secondary btn-filter margin-right-16', {
                    active: filterCount,
                    outline: !filterCount,
                })}
                onClick={() => onClick()}
            >
                {filterCount ? <div className="filter-count">{filterCount}</div> : <IconFilter />}
                <span>Filter</span>
            </Button>
            {!!filterCount && (
                <div className="btn-clear" onClick={() => onClear()}>
                    Clear
                </div>
            )}
        </div>
    );
};
export default FilterButton;
