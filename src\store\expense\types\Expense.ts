import { FilterParamsOperator } from '../../../types';

export interface RawExpense {
    id: string;
    category: string;
    location: string;
    vendor: string;
    date: string;
    submitted_amount: string | null;
    approved_amount: string | null;
    declined_amount: string | null;
    status: string;
    image_path: string;
    created_at: string;
    archived_at: string;
    claim_id: string;
    notes: string | null;
    reviewer_comment: string | null;
    created_by?: RawExpenseCreatedBy;
}

export type ExpenseFitlerableFields =
    | 'date'
    | 'vendor'
    | 'submitted_amount'
    | 'approved_amount'
    | 'location'
    | 'status'
    | 'category'
    | 'notes'
    | 'claim_id'
    | 'archived_at'
    | 'is_created_by_admin'
    | 'last_activity_at'
    | 'created_by_id';

export enum ExpenseOrderBy {
    ID = 'uuid',
    STATUS = 'status',
    CATEGORY = 'category',
    LOCATION = 'location',
    DATE = 'date',
    CLAIM_ID = 'claim_id',
    VENDOR = 'vendor',
    CREATED_AT = 'created_at',
    LAST_ACTIVITY_AT = 'last_activity_at',
}
export interface ExpenseFilterParam {
    field: ExpenseFitlerableFields;
    operator: FilterParamsOperator;
    value: number | string | string[] | number[] | Date[];
}

export const DEFAULT_EXPENSE_PER_PAGE = 10;

export enum RawExpenseStatus {
    SUBMITTED = 'submitted',
    IN_REVIEW = 'in_review',
    AUDITED = 'audited',
}

export interface RawExpenseCreatedBy {
    full_name?: string;
    name?: string;
    email: string;
    profile_photo: string | null;
}

export interface ExpenseCreatedBy {
    name: string;
    email: string;
    profilePhoto?: string;
}
