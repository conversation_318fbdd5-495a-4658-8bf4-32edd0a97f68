import AuthModel from '../../auth/types/AuthModel';
import ChatModel from '../../chat/types/ChatModel';
import UserModel from '../../user/types/UserModel';
import ClaimModel from '../../claim/types/ClaimModel';
import ProfileModel from '../../profile/types/ProfileModel';
import DocumentsModel from '../../documents/types/DocumentsModel';
import PolicyHolderModel from '../../policyHolder/types/PolicyHolderModel';
import ExpenseModel from '../../expense/types/ExpenseModel';
import ReportModel from '../../report/type/ReportModel';
import NotificationModel from '../../notifications/types/NotificationModel';
import InvoiceModel from '../../invoice/type/InvoiceModel';

export default interface AppModel {
    auth: AuthModel;
    claim: ClaimModel;
    chat: ChatModel;
    expense: ExpenseModel;
    profile: ProfileModel;
    documents: DocumentsModel;
    policyholder: PolicyHolderModel;
    report: ReportModel;
    invoice: InvoiceModel;
    user: UserModel;
    notification: NotificationModel;
}
