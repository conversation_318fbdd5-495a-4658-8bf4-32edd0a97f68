import { ResourcePagination } from '../store/app/types';
import { FilterParam, FilterParamsOperator } from '../types';

export const DEFAULT_PER_PAGE = 8;

export const getPaginationPages = (count: number, perPage = DEFAULT_PER_PAGE): number => {
    return Math.ceil(count / perPage);
};

export enum PaginationDirection {
    ASC = 'asc',
    DESC = 'desc',
}

export const mapPagination = (rawPagination: any): ResourcePagination => ({
    perPage: rawPagination.per_page,
    currentPage: rawPagination.current_page,
    total: rawPagination.total,
});

export const makeFilterParam = <T = any>(
    field: T,
    operator: FilterParamsOperator,
    value?: string | string[] | number | number[] | Date[],
): FilterParam<T> => ({
    field,
    operator,
    value,
});
