import classNames from 'classnames';
import React, { useState } from 'react';
import RcDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Controller, useFormContext } from 'react-hook-form';
import { DATE_ONLY } from '../../../../utils/dateFormat';

interface Props {
    className?: string;
    dateFormat?: string;
    disabled?: boolean;
    label?: string;
    maxDate?: Date;
    minDate?: Date;
    name: string;
    onCalendarOpen?: (value: boolean) => void;
    placeholder?: string;
    showTimeSelect?: boolean;
    size?: 'small' | 'regular' | 'big';
    mask?: string;
    customInput?: React.ReactNode;
    onChange?: (date: string) => void;
}

const DatePicker: React.FC<Props> = ({
    className,
    dateFormat = DATE_ONLY,
    disabled,
    label,
    maxDate,
    minDate,
    name,
    onCalendarOpen,
    placeholder,
    showTimeSelect,
    size = 'regular',
    customInput,
}: Props) => {
    const { control, getValues, setValue } = useFormContext();
    const [open, setOpen] = useState<boolean>(false);
    const handleDateChange = (date: Date | null) => {
        if (date) {
            setValue(name, date);
        }
    };
    return (
        <Controller
            render={({ field, fieldState }) => {
                return (
                    <div
                        className={classNames(
                            {
                                'form-group': true,
                                datepicker: true,
                            },
                            className,
                            size,
                        )}
                    >
                        {label && <label>{label}</label>}

                        <div className="form-field">
                            <RcDatePicker
                                className={classNames(
                                    {
                                        open: open,
                                    },
                                    size,
                                )}
                                useWeekdaysShort={true}
                                id={name}
                                {...field}
                                disabled={disabled}
                                placeholderText={placeholder}
                                selected={field.value}
                                value={getValues(name)}
                                maxDate={maxDate}
                                minDate={minDate}
                                showTimeSelect={showTimeSelect}
                                showYearDropdown
                                yearDropdownItemNumber={40}
                                scrollableYearDropdown
                                dateFormat={dateFormat}
                                onCalendarOpen={() => {
                                    setOpen(true);
                                    !!onCalendarOpen && onCalendarOpen(true);
                                }}
                                onCalendarClose={() => {
                                    setOpen(false);
                                    !!onCalendarOpen && onCalendarOpen(false);
                                }}
                                shouldCloseOnSelect={true}
                                // customInputRef="inputRef"
                                customInput={customInput}
                                onChange={handleDateChange}
                            />
                        </div>

                        {!!fieldState.error && (
                            <div
                                className={classNames(
                                    {
                                        note: true,
                                    },
                                    [fieldState.error.type],
                                )}
                            >
                                {fieldState.error.message}
                            </div>
                        )}
                    </div>
                );
            }}
            name={name}
            control={control}
        />
    );
};

export default DatePicker;
