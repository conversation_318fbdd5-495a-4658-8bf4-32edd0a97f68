@import 'abstracts/variables', 'abstracts/mixins', 'layout/center', 'layout/grid', 'layout/header',
    'layout/main', 'layout/navigation', 'layout/sider', 'layout/dashboard', 'layout/auth',
    'components/avatar', 'components/button', 'components/contextmenu', 'components/dialog',
    'components/dropzone', 'components/modal', 'components/filter-button',
    'components/form/date-picker', 'components/form/form', 'components/form/code_verification',
    'components/form/password_conditions', 'components/form/range', 'components/form/select_box',
    'components/notifications', 'components/people_modal', 'components/table',
    'components/transform', 'components/search', 'components/spinner', 'components/user_search',
    'pages/login', 'pages/account', 'pages/claim_library', 'pages/claim_dashboard',
    'pages/reports_dashboard', 'pages/claim_expenses', 'pages/claim_information',
    'pages/claim_reports', 'pages/settings', 'vendor/toastify.scss', 'chat/chat', 'global/normalize',
    'global/font', 'global/heading', 'global/helpers', 'global/base', 'global/ant', 'global/color',
    'shared/pagination', 'shared/pill', 'shared/progress--bar';
