.dialog {
    @include flex(flex, column, nowrap, flex-start, stretch);
    background-color: white;
    max-width: 400px;
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.3s ease;
    padding: 40px;

    &.open {
        opacity: 1;
        transform: scale(1);
    }

    hr {
        margin-bottom: 24px !important;
        margin-top: 24px !important;
    }

    &-title {
        font-size: 24px;
        font-weight: $font-weight-bold;
        line-height: 28px;
        text-align: center;
    }

    &-subtitle {
        color: var(--gray-2, #4f4f4f);
        font-size: 14px;
        font-weight: $font-weight-light;
        line-height: 20px;
        text-align: center;
    }

    &-overlay {
        @include flex(flex, row, nowrap, center, center);
        background: rgba(0, 0, 0, 0.9);
        opacity: 0;
        transition: all 0.3s ease;
        pointer-events: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 20;

        &.open {
            opacity: 1;
            pointer-events: auto;
        }
    }
}
