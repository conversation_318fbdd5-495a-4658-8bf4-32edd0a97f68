import { Button, Space } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { ReactComponent as IconEdit } from '../../assets/icons/pencil-sharp.svg';
import { useStoreActions, useStoreState } from '../../store';
import { ClaimFilterableField, ClaimsOrderBy } from '../../store/claim/types/Claim';
import UserDto from '../../store/user/dto/UserDto';
import { PaginationDirection, makeFilterParam } from '../../utils/paginate';
import Avatar from '../app/shared/Avatar';
import Dialog from '../app/shared/Dialog';
import Modal from '../app/shared/Modal';
import Pagination from '../app/shared/Pagination';
import ConnectedClaimsList from './ConnectedClaimsList';
import EditUserModal from './EditUserModal';
import { formatDateStringWithBrowserTimeZone } from '../../utils/dateFormat';
import { UserRole } from '../../store/user/types/User';
import { capitalizeString } from '../../utils/strings';

interface Props {
    onEditSuccess: VoidFunction;
    handleDelete: VoidFunction;
    onClose: VoidFunction;
    show: boolean;
    user: UserDto;
    deleteDialogDisabled?: boolean;
}

const UserDetailsModal: React.FC<Props> = ({
    user,
    onClose,
    handleDelete,
    onEditSuccess,
    show,
    deleteDialogDisabled = false,
}) => {
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);

    const { user: authUser } = useStoreState((state) => state.auth);

    const { list: claims, pagination } = useStoreState((state) => state.claim);
    const {
        get: geClaimsList,
        loadPagination,
        unload,
    } = useStoreActions((actions) => actions.claim);

    const getClaims = useCallback(
        (
            page = 1,
            limit = 6,
            order_by = ClaimsOrderBy.ID,
            direction = PaginationDirection.DESC,
        ) => {
            if (!user) return;

            geClaimsList({
                page,
                limit,
                order_by,
                direction,
                params: [
                    makeFilterParam<ClaimFilterableField>(
                        user.role === UserRole.ADJUSTER ? 'user_id' : 'admin_id',
                        '=',
                        user.id,
                    ),
                    makeFilterParam<ClaimFilterableField>('archived_at', 'is null'),
                ],
            });
        },
        [geClaimsList, user],
    );

    useEffect(() => {
        getClaims(pagination?.currentPage);
    }, [pagination?.currentPage, geClaimsList, getClaims]);

    useEffect(() => {
        return () => {
            unload();
        };
    }, [unload]);

    const handlePaginationChange = (currentPage: number) => {
        if (!pagination) {
            return;
        }
        loadPagination({ ...pagination, currentPage });
    };

    const ModalHeader = () => (
        <div className="flex-space header">
            <div className="flex-start">
                <Avatar
                    className="margin-right-24"
                    size="medium"
                    name={user.name}
                    photoUrl={user.profilePhoto}
                />
                <div className="flex-col">
                    <div className="header-name margin-bottom-8">{user.name}</div>
                    <div className="header-role">{user.role}</div>
                </div>
            </div>
            <Button
                onClick={() => setShowEditModal(true)}
                size="large"
                className="btn-secondary outline"
            >
                <Space>
                    <span>Edit</span>
                    <IconEdit />
                </Space>
            </Button>
        </div>
    );

    return (
        <>
            <Modal
                className="settings-page-modal-details"
                onClose={onClose}
                show={show}
                title={<ModalHeader />}
            >
                <div className="body">
                    <div className="group">
                        <div className="label">Phone Number:</div>
                        <div className="value">{user.phone}</div>
                    </div>
                    <div className="group">
                        <div className="label">Email:</div>
                        <div className="value">{user.email}</div>
                    </div>
                    <div className="group">
                        <div className="label">Method of Contact</div>
                        <div className="value">{capitalizeString(user.contactMethod || '')}</div>
                    </div>
                    {user.role === UserRole.ADJUSTER && (
                        <>
                            <div className="group">
                                <div className="label">Insurance Company</div>
                                <div className="value">{capitalizeString(user.company || '')}</div>
                            </div>
                            <div className="group">
                                <div className="label">Title</div>
                                <div className="value">{capitalizeString(user.jobTitle || '')}</div>
                            </div>
                            <div className="group">
                                <div className="label">Time Zone Located In</div>
                                <div className="value">{capitalizeString(user.timezone || '')}</div>
                            </div>
                        </>
                    )}
                    <hr />
                    <div>
                        <ConnectedClaimsList list={claims} />
                        {pagination && (
                            <Pagination {...pagination} onChange={handlePaginationChange} simple />
                        )}
                    </div>
                    <hr />
                    {user.id !== authUser?.id && (
                        <>
                            <Button
                                type="link"
                                className="btn-text"
                                block
                                onClick={() => setShowDeleteDialog(true)}
                            >
                                <b>Delete Profile</b>
                            </Button>
                            <hr />
                        </>
                    )}
                    <div className="timestamps">
                        {user.activationSentAt && (
                            <div className="timestamp">
                                <span>Welcome Email Sent:</span>
                                <span>
                                    {formatDateStringWithBrowserTimeZone(user.activationSentAt)}{' '}
                                    (PST)
                                </span>
                            </div>
                        )}
                        {user.emailVerifiedAt && (
                            <div className="timestamp">
                                <span>Terms Accepted:</span>
                                <span>
                                    {formatDateStringWithBrowserTimeZone(user.emailVerifiedAt)}{' '}
                                    (PST)
                                </span>
                            </div>
                        )}
                    </div>
                </div>

                {showEditModal && (
                    <EditUserModal
                        user={user}
                        onSuccess={() => onEditSuccess()}
                        onClose={() => setShowEditModal(false)}
                        show={showEditModal}
                    />
                )}
            </Modal>
            <Dialog
                onCancel={() => setShowDeleteDialog(false)}
                onOk={handleDelete}
                show={showDeleteDialog}
                disabled={deleteDialogDisabled}
            />
        </>
    );
};
export default UserDetailsModal;
