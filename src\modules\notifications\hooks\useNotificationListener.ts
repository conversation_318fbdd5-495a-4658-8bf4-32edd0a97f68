import { useEffect } from 'react';
import { useStoreActions } from '../../../store';
import { NOTIFICATION_INTERVAL_PERIOD } from '../utills';

const useNotificationListener = (isActive) => {
    const { checkUnread } = useStoreActions((actions) => actions.notification);

    useEffect(() => {
        const interval = isActive
            ? setInterval(() => checkUnread(), NOTIFICATION_INTERVAL_PERIOD)
            : undefined;

        if (!isActive) {
            clearInterval(interval);
            return;
        }

        checkUnread();

        return () => {
            clearInterval(interval);
        };
    }, [checkUnread, isActive]);
};
export default useNotificationListener;
