import { Button } from 'antd';
import { endOfDay, endOfToday, startOfDay } from 'date-fns';
import { useForm } from 'react-hook-form';
import { ExpenseRangesDto } from '../../../../store/claim/dto/ClaimDto';
import { ExpenseFilterDto } from '../../../../store/expense/dto/ExpenseDto';
import { ExpenseFilterParam } from '../../../../store/expense/types/Expense';
import Modal from '../../../app/shared/Modal';
import DatePickerRange from '../../../app/shared/form/DatePickerRange';
import Form from '../../../app/shared/form/Form';
import MultiSelectBox from '../../../app/shared/form/MultiSelectBox';
import NumberRangePicker from '../../../app/shared/form/NumberRangePicker';
import {
    ExpenseStatusFilter,
    ExpenseStatusFilterType,
    FilterExpenseCategoryOptions,
    mapFilterParams,
} from './utils';
import React from 'react';

interface Props {
    // claim: ClaimDto;
    statusFilter: ExpenseStatusFilterType;
    show: boolean;
    onClose: VoidFunction;
    onFilterChange: (filterParams: ExpenseFilterParam[]) => void;
    filterParams: ExpenseFilterParam[];
    expenseRanges?: ExpenseRangesDto;
}

const FilterModal: React.FC<Props> = ({
    show,
    onClose,
    statusFilter,
    onFilterChange,
    filterParams,
    expenseRanges,
}) => {
    const mappedParams = mapFilterParams(filterParams);

    const methods = useForm<ExpenseFilterDto>({
        defaultValues: {
            category: mappedParams.category,
            submittedRange: mappedParams.submittedRange || undefined,
            approvedRange: mappedParams.approvedRange || undefined,
            dateRange: mappedParams.dateRange || undefined,
        },
    });

    const handleSubmit = (fields) => {
        const params: ExpenseFilterParam[] = [];
        fields.submittedRange &&
            fields.submittedRange?.[0] !== fields.submittedRange?.[1] &&
            params.push({
                field: 'submitted_amount',
                operator: 'between',
                value: fields.submittedRange,
            });

        if (
            statusFilter === ExpenseStatusFilter.AUDITED &&
            fields.approvedRange?.[0] !== fields.approvedRange?.[1]
        ) {
            fields.approvedRange &&
                params.push({
                    field: 'approved_amount',
                    operator: 'between',
                    value: fields.approvedRange,
                });
        }

        if (fields.category) {
            params.push({
                field: 'category',
                operator: 'in',
                value: fields.category,
            });
        }

        if (fields.dateRange && fields.dateRange.every((date) => !!date)) {
            params.push({
                field: 'date',
                operator: 'between',
                value: [startOfDay(fields.dateRange[0]), endOfDay(fields.dateRange[1])],
            });
        }

        onFilterChange(params);
        onClose();
    };

    const onClearFilters = () => {
        methods.reset({
            submittedRange: [undefined, undefined],
            approvedRange: [undefined, undefined],
            dateRange: [undefined, undefined],
        });
        onFilterChange([]);
    };

    return (
        <Modal
            show={show}
            onClose={onClose}
            title="Filter"
            subtitle={`When you've selected your desired filters, click "Apply Filters" to update your expense list. The list will show only those expenses matching your selected criteria.`}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(handleSubmit)}>
                <MultiSelectBox
                    label="Category"
                    name="category"
                    options={FilterExpenseCategoryOptions}
                    placeholder="Select"
                />
                <DatePickerRange
                    name="dateRange"
                    label="Date Range"
                    // min={startOfDay(new Date(claim.lossDate))}
                    max={endOfToday()}
                />
                {expenseRanges?.submitted.min !== expenseRanges?.submitted.max && (
                    <NumberRangePicker
                        label="Submitted Amount"
                        name="submittedRange"
                        min={expenseRanges?.submitted.min || 0}
                        max={expenseRanges?.submitted.max || 10000}
                        defaultValue={[
                            expenseRanges?.submitted.min || 0,
                            expenseRanges?.submitted.max || 10000,
                        ]}
                    />
                )}
                {expenseRanges?.approved.min !== expenseRanges?.approved.max &&
                    statusFilter === ExpenseStatusFilter.AUDITED && (
                        <NumberRangePicker
                            label="Approved Amount"
                            name="approvedRange"
                            min={expenseRanges?.approved.min || 0}
                            max={expenseRanges?.approved.max || 10000}
                            defaultValue={[
                                expenseRanges?.approved.min || 0,
                                expenseRanges?.approved.max || 10000,
                            ]}
                        />
                    )}
                <div className="flex-start">
                    <Button className="btn-primary" size="large" htmlType="submit">
                        Apply Filters
                    </Button>
                    <Button type="link" size="large" onClick={() => onClearFilters()}>
                        Clear
                    </Button>
                </div>
            </Form>
        </Modal>
    );
};
export default FilterModal;
