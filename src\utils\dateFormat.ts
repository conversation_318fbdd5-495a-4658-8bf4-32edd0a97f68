import { enUS } from 'date-fns/locale';
import { format as formatTz, utcToZonedTime, zonedTimeToUtc } from 'date-fns-tz';
import { format, parseISO } from 'date-fns';

export const TIME_ONLY = 'h:mm a';
export const DATE_ONLY = 'MM/dd/yyyy';
export const DAY_TIME_THIS_WEEK = 'EEE hh:mm a';
export const CHAT_DATE_TIME = 'MMM d, yyyy, h:mm a';
export const API_DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';
export const DATE_TIME_FORMAT = 'yyyy-MM-ddTHH:mm:ss';
export const API_DATE_FORMAT = 'yyyy-MM-dd';
export const DATE_TIME = 'MM/dd/yyyy h:mm a';
export const DATE_AT_TIME = "MM / dd / yyyy 'at' h:mm a";
export const DATE_AT_TIME_PLACEHOLDER = 'MM / DD / YYYY -  hh:mm __';
export const DATE_AT_TIME_MASK = '99 / 99 / 9999 at 99:99 aa';
export const DATE_FORMAT = 'MM / dd / yyyy';
export const TIME_FORMAT = 'hh:mm a';
export const DATE_TIME_DISPLAY_FORMAT = `${DATE_FORMAT} 'at' ${TIME_FORMAT}`;
export const MONTH_DAY = 'MMM d';
export const UTC_TIMEZONE = 'Etc/UTC';

export const timeZoneMapping = {
    EST: 'America/New_York',
    MST: 'America/Denver',
    PST: 'America/Los_Angeles',
    CST: 'America/Chicago',
    HAST: 'Pacific/Honolulu',
    AKST: 'America/Anchorage',
    UTC: 'UTC',
};

export const formatDateUTCString = (date: string, formatType: string = DATE_TIME) => {
    return format(utcToZonedTime(date, UTC_TIMEZONE), formatType, { locale: enUS });
};
export const formatDateString = (date: string, formatType: string = DATE_TIME) => {
    const parsedDate = new Date(date);
    return format(parsedDate, formatType, { locale: enUS });
};

export const convertUtcToLocal = (utcDateString): Date => {
    const utcDate = new Date(utcDateString);
    // Convert the UTC date to the local timezone by subtracting the timezone offset
    return new Date(utcDate.getTime() - utcDate.getTimezoneOffset() * 60000);
};

export const createUTCDate = (utcDateString: string): Date => {
    const dateParts = utcDateString.split(/[-T:Z.]/).map((part) => parseInt(part));
    return new Date(
        Date.UTC(
            dateParts[0],
            dateParts[1] - 1,
            dateParts[2],
            dateParts[3],
            dateParts[4],
            dateParts[5],
        ),
    );
};

export const convertUtcToSpecifiedTimeZone = (
    utcDateString: string,
    timeZoneAbbreviation: string,
): Date => {
    const targetTimeZone = timeZoneMapping[timeZoneAbbreviation];
    if (!targetTimeZone) {
        throw new Error(`Invalid time zone abbreviation: ${timeZoneAbbreviation}`);
    }
    // Manually parse the UTC date string to avoid local timezone offsets
    // move this into separate function
    const utcDate = createUTCDate(utcDateString);
    return utcToZonedTime(utcDate, targetTimeZone);
};

export const convertLocalDateToUTC = (dateInput: string, timeZoneAbbreviation: string): Date => {
    const targetTimeZone = timeZoneMapping[timeZoneAbbreviation];
    if (!targetTimeZone) {
        throw new Error(`Invalid time zone abbreviation: ${timeZoneAbbreviation}`);
    }

    return zonedTimeToUtc(dateInput, targetTimeZone);
};
export const getDate = (date: string) => utcToZonedTime(date, UTC_TIMEZONE);

export const isValidDateString = (input: string) => {
    return !isNaN(Date.parse(input));
};

export const formatDateObjectWithTimeZone = (date: Date, format: string, timeZone: string) => {
    // Convert the date to the given timezone
    const zonedDate = utcToZonedTime(date, timeZone);

    // Return the date as a string formatted for the given timezone
    // The format includes the time zone offset and abbreviation for readability
    return formatTz(zonedDate, format, { timeZone });
};

export const formatDateStringWithBrowserTimeZone = (
    dateString: string,
    format: string = DATE_AT_TIME,
): string => {
    // Adjust the dateString to truncate fractional seconds to milliseconds
    const adjustedDateString = dateString.replace(/(\.\d{3})\d*Z$/, '$1Z');

    // Parse the adjusted date string into a Date object
    const date = parseISO(adjustedDateString);

    if (isNaN(date.getTime())) {
        throw new Error('Invalid date string');
    }

    // Get the browser's timezone
    const browserTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    return formatDateObjectWithTimeZone(date, format, browserTimeZone);
};

export const formatToTimezone = (
    date: Date,
    timeZone: string,
    format: string = API_DATE_TIME_FORMAT,
) => {
    // Resolve the full timezone name from the abbreviation
    const timezone = timeZoneMapping[timeZone];
    return formatDateObjectWithTimeZone(date, format, timezone);
};
