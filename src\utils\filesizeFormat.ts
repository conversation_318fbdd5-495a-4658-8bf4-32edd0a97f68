export function formatFileSize(bytes: number, sizeScale = 'KB'): string {
    const sizeScales: string[] = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const base = 1024;

    if (bytes === 0) {
        return `0${sizeScale}`;
    }

    const i = Math.floor(Math.log(bytes) / Math.log(base));
    const formattedSize = (bytes / Math.pow(base, i)).toFixed(2);
    return `${formattedSize}${sizeScales[i]}`;
}
