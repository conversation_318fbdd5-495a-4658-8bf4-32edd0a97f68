import { MenuProps, Space } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import UserDto from '../../store/user/dto/UserDto';
import { ACTION_MENU_DIVIDER, Resource } from '../../types';
import Avatar from '../app/shared/Avatar';
import ContextMenu from '../app/shared/ContextMenu';
import DataGrid from '../app/shared/DataGrid';
import { UserActions, UserFilter } from './utils';
import { useStoreState } from '../../store';

interface Props {
    list: Resource<UserDto[]>;
    userFilter: UserFilter;
    handleActionClick: (item: MenuItemType | undefined, user: UserDto) => void;
}

const UserList: React.FC<Props> = ({ list, userFilter, handleActionClick }) => {
    const { user: authUser } = useStoreState((state) => state.auth);

    const [actionItems] = useState<MenuProps['items']>([
        {
            label: 'View Profile',
            key: UserActions.VIEW_USER,
        },
        {
            label: 'Edit Profile',
            key: UserActions.EDIT,
        },
        {
            type: 'divider',
        },
        {
            label: 'Delete Profile',
            key: UserActions.DELETE,
        },
    ]);

    const columns: ColumnsType<UserDto> = useMemo(() => {
        if (userFilter === 'Admins') {
            return [
                {
                    key: 'id',
                    title: 'Full Name',
                    render: (user: UserDto) => (
                        <Space
                            className="cursor"
                            onClick={() => handleActionClick({ key: UserActions.VIEW_USER }, user)}
                        >
                            <Avatar photoUrl={user.profilePhoto} name={user.name} />
                            <b>{user.name}</b>
                        </Space>
                    ),
                },
                {
                    key: 'role',
                    title: 'Role',
                    render: ({ role }) => <span>{role}</span>,
                },
                { key: 'email', title: 'Email', render: ({ email }) => <span>{email}</span> },
                {
                    key: 'phone',
                    title: 'Phone Number',
                    render: ({ phone }) => <span>{phone}</span>,
                },
                {
                    key: 'actions',
                    title: 'Actions',
                    width: 60,
                    render: (user: UserDto) => {
                        let items = actionItems;

                        if (user.id === authUser?.id) {
                            items = items?.filter(
                                (item) =>
                                    item?.key !== ACTION_MENU_DIVIDER &&
                                    item?.key !== UserActions.DELETE,
                            );
                        }

                        return (
                            <div className="flex-center">
                                <ContextMenu
                                    onItemClick={(item) => handleActionClick(item, user)}
                                    items={items}
                                />
                            </div>
                        );
                    },
                },
            ] as ColumnsType<UserDto>;
        } else {
            return [
                {
                    key: 'id',
                    title: 'Full Name',
                    render: (user: UserDto) => (
                        <Space
                            className="cursor"
                            onClick={() => handleActionClick({ key: UserActions.VIEW_USER }, user)}
                        >
                            <Avatar photoUrl={user.profilePhoto} name={user.name} />
                            <b>{user.name}</b>
                        </Space>
                    ),
                },
                {
                    key: 'company',
                    title: 'Insurance Company',
                    render: ({ company }) => <span>{company}</span>,
                },
                {
                    key: 'jobTitle',
                    title: 'Title',
                    render: ({ jobTitle }) => <span>{jobTitle}</span>,
                },
                { key: 'email', title: 'Email', render: ({ email }) => <span>{email}</span> },
                {
                    key: 'phone',
                    title: 'Phone Number',
                    render: ({ phone }) => <span>{phone}</span>,
                },
                {
                    key: 'actions',
                    title: 'Actions',
                    width: 60,
                    render: (user: UserDto) => {
                        let items = actionItems;

                        if (user.id === authUser?.id) {
                            items = items?.filter(
                                (item) =>
                                    item?.key !== ACTION_MENU_DIVIDER &&
                                    item?.key !== UserActions.DELETE,
                            );
                        }

                        return (
                            <div className="flex-center">
                                <ContextMenu
                                    onItemClick={(item) => handleActionClick(item, user)}
                                    items={items}
                                />
                            </div>
                        );
                    },
                },
            ] as ColumnsType<UserDto>;
        }
    }, [userFilter, handleActionClick, actionItems, authUser?.id]);

    return <DataGrid list={list} columns={columns} />;
};
export default UserList;
