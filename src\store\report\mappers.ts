import { plainToInstance } from 'class-transformer';
import { ReportDto } from './dto/ReportDto';
import { RawReport, RawReportType } from './type/Report';

export const mapReport = ({
    claim_id,
    start_date,
    end_date,
    expenses_count,
    type,
    in_review_count,
    audited_count,
    cost_savings,
    recommended_reimbursement,
    approved_count,
    submitted_count,
    declined_count,
    expenses_category,
    html,
    metadata,
    ...rest
}: RawReport): ReportDto =>
    plainToInstance(ReportDto, {
        ...rest,
        claimId: claim_id,
        startDate: start_date,
        endDate: end_date,
        expensesCount: expenses_count,
        type: fromRawReportTypeMap[type],
        inReview: in_review_count,
        audited: audited_count,
        costSavings: cost_savings,
        recommendedReimbursement: recommended_reimbursement,
        submitted: submitted_count,
        approved: approved_count,
        declined: declined_count,
        categories: expenses_category,
        html: html,
        metadata: metadata,
    } as ReportDto);

export const mapReports = (raws: RawReport[]): ReportDto[] => raws.map((raw) => mapReport(raw));

export const fromRawReportTypeMap = {
    [RawReportType.AUTOMATIC]: 'Automatic',
    [RawReportType.MANUAL]: 'Manual',
};
