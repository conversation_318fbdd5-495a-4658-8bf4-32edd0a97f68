.datepicker {
    margin-bottom: 24px !important;

    .icon {
        font-size: 20px;
        top: 33px !important;
        z-index: 1;

        &-after {
            + .react-datepicker-wrapper {
                input {
                    padding-right: 56px;
                }
            }
        }
    }

    &-input {
        font-weight: $font-weight-semi-bold;
    }

    .react-datepicker {
        @include flex(flex, row, nowrap, flex-start, stretch);
        border: 1px solid transparent;
        border-radius: 0;
        text-transform: capitalize;
        width: 100%;

        @include breakpoint(sm) {
            padding: 16px 0;
        }

        &-popper {
            position: static !important;
            transform: none !important;
        }

        &__triangle {
            &:after,
            &:before {
                content: none !important;
            }
        }

        &-wrapper {
            line-height: 1;
            width: 100%;
        }

        &__year {
            &-read-view {
                &--selected-year {
                    color: transparent;
                }
                &--down-arrow {
                    display: none;
                }
            }

            &-dropdown-container {
                left: 30px;
                position: absolute;
                right: 0;
                top: 0;
                width: calc(100% - 60px);
            }
        }

        &__header {
            background-color: transparent;
            border-bottom: none;
            margin-bottom: 15px;
            padding: 0;

            .react-datepicker {
                &__day-names {
                    @include flex(flex, row, nowrap, space-between, flex-start);
                }

                &__day-name {
                    color: #d5d5d6;
                    font-size: 12px;
                    margin: 0;
                    text-transform: uppercase;
                }
            }
        }

        &__navigation {
            &-icon {
                &:before {
                    border-color: #020202;
                    border-width: 2px 2px 0 0;
                }
            }
        }

        &__current-month {
            font-weight: $font-weight-semi-bold;
            margin-bottom: 22px;
        }

        &__month {
            margin: 0;

            &-container {
                width: 100%;
            }
        }

        &__week {
            @include flex(flex, row, nowrap, space-between, flex-start);
        }

        &__day {
            border-radius: 50%;
            margin: 0;
            line-height: 36px;
            transition: all 0.1s ease;
            width: 36px;

            &--outside-month {
                color: #d5d5d6;
            }

            &:hover {
                border-radius: 50%;
            }

            &--selected {
                background-color: $color-secondary;
                //border-radius: 50%;
                color: $color-text;

                &:hover {
                    background-color: $color-secondary;
                }
            }

            &--keyboard-selected {
                background-color: transparent;
                border-color: #000;
                outline: none;
                //border-radius: 0;

                &:hover {
                    background-color: #f0f0f0;
                }
            }

            &--in {
                &-range {
                    background-color: $color-secondary;
                    color: #fff;
                }

                &-selecting {
                    &-range {
                        background-color: $color-secondary;
                        color: #fff;
                    }
                }
            }
        }

        &__input-container {
            line-height: 1;

            input {
                padding: 17px 18px;
                border: none;
                background-color: $color-input-background;
                width: 100%;

                &:disabled {
                    background-color: $color-background-secondary;
                    border-color: $color-secondary;
                }

                // &.open {
                //     border: 2px solid $color-primary;
                // }
            }
        }

        &-popper {
            z-index: 6;
        }

        &__time {
            &-container {
                border-left: none;
                margin-left: 40px;

                @include breakpoint(sm) {
                    margin-left: 0;
                }
            }

            &-list {
                height: 240px !important;

                &-item {
                    &--selected {
                        background-color: $color-secondary !important;
                    }
                }
            }
        }
    }

    &-range {
        @include flex(flex, column, nowrap, center, flex-start);
        cursor: pointer;
        width: 100%;

        &-inner {
            margin-bottom: 0 !important;
        }

        .react-datepicker {
            padding-left: 0;
            padding-right: 0;

            &__tab-loop {
                width: 100%;
            }

            &-wrapper {
                display: none;
            }

            &__day {
                border-radius: 24px;
                color: $color-text;
                flex: 1;
                line-height: 45px;
                margin: 0 2px 5px;

                &-name {
                    color: $color-text !important;
                    flex: 1;
                    line-height: 45px;
                    margin: 0 2px 5px;
                }

                &:hover {
                    border-radius: 24px;
                }

                &--disabled {
                    color: transparentize($color-text, 0.8) !important;
                }

                &--in {
                    &-range {
                        background-color: #f0faf8;
                        color: $color-secondary;
                        font-weight: $font-weight-semi-bold;

                        &:hover {
                            background-color: #f0faf8;
                            color: $color-secondary;
                        }
                    }

                    &-selecting {
                        &-range {
                            background-color: #f0faf8 !important;
                            color: $color-secondary;
                            font-weight: $font-weight-semi-bold;

                            &:hover {
                                background-color: #f0faf8 !important;
                                color: $color-secondary;
                            }
                        }
                    }
                }

                &--selecting {
                    &-range {
                        &-end,
                        &-start {
                            background-color: $color-secondary !important;
                            color: #fff !important;

                            &:hover {
                                background-color: $color-secondary !important;
                            }
                        }
                    }
                }

                &--range {
                    &-end,
                    &-start {
                        background-color: $color-secondary;
                        color: #fff !important;

                        &:hover {
                            background-color: $color-secondary;
                        }
                    }
                }

                &--outside-month {
                    color: transparentize($color-text, 0.4);
                }
            }
        }

        &-head {
            @include flex(flex, row, nowrap, flex-start, center);
            gap: 8px;
            font-weight: $font-weight-bold;
        }

        &-inner {
            @include flex(flex, column, nowrap, center, center);
            width: 100%;
        }

        &-content {
            @include flex(flex, column, nowrap, center, center);
            width: 100%;

            .separator {
                font-size: 12px;
                margin-right: 5px;
            }
        }

        .icon {
            color: $color-tertiary;
            cursor: pointer;
            font-size: 16px;
            margin-right: 4px;
        }
    }

    .text-field-container {
        margin-bottom: 0 !important ;
    }
}
