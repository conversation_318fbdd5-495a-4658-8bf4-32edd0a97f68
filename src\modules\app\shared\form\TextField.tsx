import { Input } from 'antd';
import classNames from 'classnames';
import { ReactNode } from 'react';
import { Controller, useFormContext } from 'react-hook-form';

interface Props {
    name: string;
    label?: ReactNode;
    value?: string;
    max?: number;
    min?: number;
    step?: number;
    placeholder?: string;
    disabled?: boolean;
    type?: 'text' | 'password' | 'number' | 'email' | 'textarea';
    readonly?: boolean;
    className?: string;
    required?: boolean;
    contentBefore?: ReactNode;
    contentAfter?: ReactNode;
    onChange?: (string) => void;
}

const TextField: React.FC<Props> = ({
    name,
    label,
    value,
    max,
    min,
    step,
    placeholder,
    type,
    disabled,
    readonly,
    required,
    className,
    contentBefore,
    contentAfter,
    onChange,
}) => {
    const { control, getValues, setValue } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState, formState }) => (
                <>
                    <div
                        className={classNames(
                            {
                                'text-field-container': true,
                            },
                            className,
                        )}
                    >
                        {label && <label htmlFor={name}>{label}</label>}
                        <div className="text-field-container-inner">
                            {type !== 'textarea' && (
                                <>
                                    {!!contentBefore && (
                                        <span className="icon icon-before margin-right-8">
                                            {contentBefore}
                                        </span>
                                    )}
                                    <Input
                                        autoComplete="off"
                                        {...field}
                                        disabled={disabled || formState.isSubmitting}
                                        type={type}
                                        min={min}
                                        max={max}
                                        step={step}
                                        placeholder={placeholder}
                                        readOnly={readonly}
                                        value={value || getValues(name)}
                                        onChange={(v) => {
                                            setValue(name, v.target.value);
                                            !!onChange && onChange(v.target.value);
                                        }}
                                        required={required}
                                    />
                                    {!!contentAfter && (
                                        <span className="icon icon-before">{contentAfter}</span>
                                    )}
                                </>
                            )}
                            {type === 'textarea' && (
                                <textarea
                                    autoComplete="off"
                                    {...field}
                                    className={classNames(
                                        {
                                            error: !!fieldState.error,
                                            disabled: disabled || formState.isSubmitting,
                                        },
                                        [fieldState.error?.type],
                                    )}
                                    placeholder={placeholder}
                                    disabled={disabled || formState.isSubmitting}
                                    value={value || getValues(name)}
                                    onChange={(v) => {
                                        setValue(name, v.target.value);
                                    }}
                                    readOnly={readonly}
                                />
                            )}
                        </div>

                        {!!fieldState.error && (
                            <div
                                className={classNames(
                                    {
                                        note: true,
                                    },
                                    [fieldState.error.type],
                                )}
                            >
                                {fieldState.error.message}
                            </div>
                        )}
                    </div>
                </>
            )}
        />
    );
};

export default TextField;
