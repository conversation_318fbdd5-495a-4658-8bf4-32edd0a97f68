import { plainToInstance } from 'class-transformer';
import { InvoiceDto } from './dto/InvoiceDto';
import { RawInvoice, RawInvoiceType } from './type/Invoice';

export const mapInvoice = ({
    claim_id,
    start_date,
    end_date,
    expenses_count,
    type,
    in_review_count,
    audited_count,
    cost_savings,
    recommended_reimbursement,
    approved_count,
    submitted_count,
    declined_count,
    expenses_category,
    html,
    metadata,
    ...rest
}: RawInvoice): InvoiceDto =>
    plainToInstance(InvoiceDto, {
        ...rest,
        claimId: claim_id,
        startDate: start_date,
        endDate: end_date,
        expensesCount: expenses_count,
        type: fromRawInvoiceTypeMap[type],
        inReview: in_review_count,
        audited: audited_count,
        costSavings: cost_savings,
        recommendedReimbursement: recommended_reimbursement,
        submitted: submitted_count,
        approved: approved_count,
        declined: declined_count,
        categories: expenses_category,
        html: html,
        metadata: metadata,
    } as InvoiceDto);

export const mapInvoices = (raws: RawInvoice[]): InvoiceDto[] => raws.map((raw) => mapInvoice(raw));

export const fromRawInvoiceTypeMap = {
    [RawInvoiceType.AUTOMATIC]: 'Automatic',
    [RawInvoiceType.MANUAL]: 'Manual',
};
