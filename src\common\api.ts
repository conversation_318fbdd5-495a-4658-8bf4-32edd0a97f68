import axios, { AxiosResponse } from 'axios';
import { store } from '../store';

declare global {}

export type ApiResponse<T> = AxiosResponse<T>;

//const env = (import.meta as any).env;

export const api = axios.create({
    baseURL: 'https://api.oopedev.com/admin',
});

export const publicApi = axios.create({
    baseURL: 'https://api.oopedev.com/admin',
});

// Request interceptor for API calls
api.interceptors.request.use(
    async (config) => {
        const expiresAt = store.getState().auth.expiresAt;
        config.headers['Accept'] = 'application/json';
        config.headers['Content-Type'] = 'application/json';

        if (!store.getState().auth._retry && expiresAt && expiresAt < new Date()) {
            await store.getActions().auth.refresh();
        }

        if (store.getState().auth.accessToken) {
            config.headers['Authorization'] = `Bearer ${store.getState().auth.accessToken}`;
        }

        return config;
    },
    (error) => {
        Promise.reject(error);
    },
);

// Response interceptor for API calls
api.interceptors.response.use(
    (response) => {
        return response;
    },
    async function (error) {
        // check if error reason is wrong access token. Status 401 expected only in case of invalid access token
        if (error.response && error.response.status === 401) {
            // already tried. clear user data
            store.getActions().auth.unload();
        }
        return Promise.reject(error);
    },
);
