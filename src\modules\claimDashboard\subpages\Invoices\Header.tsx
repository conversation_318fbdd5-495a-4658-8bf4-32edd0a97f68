import { Button } from 'antd';
import { useState } from 'react';
import { ReactComponent as IconAdd } from '../../../../assets/icons/add-sharp.svg';
import AddModal from './AddModal';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { useLocation } from 'react-router-dom';
import { canAccess } from '../../../../common/canAccess';
import { UserRole } from '../../../../store/user/types/User';
import { isOpen } from '../../../../common/claim';

interface Props {
    claim: ClaimDto;
    onAddSuccess: VoidFunction;
}

const Header: React.FC<Props> = ({ claim, onAddSuccess }) => {
    const location = useLocation();

    const isNewInvoice = location.pathname.includes('/new');

    const [showAddModal, setShowAddModal] = useState(isNewInvoice);

    return (
        <div className="claim-reports-header margin-bottom-24">
            <div>
                <div className="title">Invoices</div>
                <div className="info">Track invoices in the policyholder&apos;s claim.</div>
            </div>
            {claim.archivedAt ||
            !canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) ||
            (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN])) ? null : (
                <Button
                    className="btn-primary flex-space"
                    size="large"
                    onClick={() => setShowAddModal(true)}
                >
                    <IconAdd className="margin-right-16" />
                    Create Invoice
                </Button>
            )}
            {showAddModal && (
                <AddModal
                    claim={claim}
                    show={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    onSuccess={() => {
                        setShowAddModal(false);
                        onAddSuccess();
                    }}
                />
            )}
        </div>
    );
};
export default Header;
