import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../store';
import UserDto, { UserUpdateDto } from '../../store/user/dto/UserDto';
import { toRawRole } from '../../store/user/mappers';
import { RawUserRole } from '../../store/user/types/User';
import FlashMessages from '../app/FlashMessages';
import Modal from '../app/shared/Modal';
import Form from '../app/shared/form/Form';
import TextField from '../app/shared/form/TextField';
import { PreferedContactMethods, RoleOptions, Timezones } from './utils';
import SelectBox from '../app/shared/form/SelectBox';

interface Props {
    onSuccess: VoidFunction;
    onClose: VoidFunction;
    show: boolean;
    user: UserDto;
}

const EditUserModal: React.FC<Props> = ({ onClose, show, onSuccess, user }) => {
    const [isDisabled, setIsDisabled] = useState(false);

    const { update } = useStoreActions((actions) => actions.user);

    const methods = useForm<UserUpdateDto>({
        resolver: classValidatorResolver(UserUpdateDto),
        defaultValues: {
            role: toRawRole[user.role],
            name: user.name,
            phone: user.phone,
            email: user.email,
            timezone: user.timezone,
            job_title: user.jobTitle,
            company: user.company,
            preferred_contact_method: user.contactMethod,
        },
    });

    const onSubmit = async (fields) => {
        setIsDisabled(true);

        try {
            await update({ id: user.id, dto: fields });
            onSuccess();
        } catch (err: any) {
            FlashMessages.error('Failed to edit user');
            console.error(err.message);
        } finally {
            setIsDisabled(false);
        }
    };

    const role = methods.watch('role');

    return (
        <Modal
            title="Personal Information"
            subtitle='Click "Save Changes" to keep any changes made.'
            show={show}
            onClose={() => onClose()}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                {role !== RawUserRole.ADJUSTER && (
                    <SelectBox label="Type" name="role" options={RoleOptions} />
                )}
                <TextField disabled={isDisabled} name="name" label="Full Name" />
                <TextField disabled type="email" name="email" label="Email" />
                <TextField disabled={isDisabled} type="text" name="phone" label="Phone Number" />
                <SelectBox
                    options={PreferedContactMethods}
                    disabled={isDisabled}
                    name="preferred_contact_method"
                    label="Preferred Method of Contact"
                />
                {role === RawUserRole.ADJUSTER && (
                    <TextField disabled={isDisabled} name="company" label="Company" />
                )}
                {role === RawUserRole.ADJUSTER && (
                    <TextField disabled={isDisabled} name="job_title" label="Title" />
                )}
                <SelectBox
                    options={Timezones}
                    disabled={isDisabled}
                    name="timezone"
                    label="Time zone"
                />
                <Button
                    htmlType="submit"
                    size="large"
                    className="btn-primary"
                    loading={methods.formState.isSubmitting}
                >
                    Save
                </Button>
            </Form>
        </Modal>
    );
};
export default EditUserModal;
