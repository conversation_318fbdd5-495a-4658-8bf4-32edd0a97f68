import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Checkbox } from 'antd';
import classNames from 'classnames';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { ReactComponent as LogoImage } from '../../assets/images/logo.svg';
import withoutAuth from '../../hooks/withoutAuth';
import { useStoreActions } from '../../store';
import { SetupPasswordDto } from '../../store/auth/dto/SetupPasswordDto';
import AuthLayout from '../app/layouts/AuthLayout';
import Form from '../app/shared/form/Form';
import PasswordField from '../app/shared/form/PasswordField';
import PasswordConditionList from './PasswordContitionList';

const SetupPasswordPage: React.FC = () => {
    const { token } = useParams();
    const navigate = useNavigate();

    const [hasMetPasswordConditions, setHasMetPasswordConditions] = useState(false);

    const { activate } = useStoreActions((actions) => actions.auth);

    const methods = useForm<SetupPasswordDto>({
        resolver: classValidatorResolver(SetupPasswordDto),
        defaultValues: {
            password: '',
            password_confirmation: '',
            terms_accepted_at: false,
        },
    });

    const password = methods.watch('password');
    const password_confirmation = methods.watch('password_confirmation');
    const termsAcceptedAt = methods.watch('terms_accepted_at');

    const canSubmit = useMemo(
        () => termsAcceptedAt && hasMetPasswordConditions,
        [termsAcceptedAt, hasMetPasswordConditions],
    );

    const onSubmit = async (fields) => {
        try {
            if (!token) throw new Error('No token');
            await activate({
                token,
                dto: fields,
            });
            navigate('/auth/login');
        } catch (err: any) {
            console.error(err.message);
        }
    };

    return (
        <AuthLayout>
            <Form className="auth-form" methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <LogoImage className="auth-form-logo"></LogoImage>
                <div className="block">
                    <div className="heading h4 margin-bottom-32">Setup your password</div>
                    <PasswordField
                        name="password"
                        label="Password"
                        placeholder="Enter your password"
                    />
                    <PasswordField
                        name="password_confirmation"
                        label="Repeat Password"
                        placeholder="Confirm your password"
                        className="margin-bottom-24"
                    />
                    <PasswordConditionList
                        password={password}
                        repeatPassword={password_confirmation}
                        onConditionsChange={setHasMetPasswordConditions}
                    />
                </div>

                <Checkbox
                    className="auth-form-terms-and-conditions"
                    checked={!!termsAcceptedAt}
                    onChange={() => methods.setValue('terms_accepted_at', !termsAcceptedAt)}
                >
                    I agree to{' '}
                    <a
                        href="https://www.outofpocketexp.com/security/terms-and-conditions"
                        target="_blank"
                        rel="noreferrer"
                    >
                        Terms and Conditions
                    </a>
                </Checkbox>
                <Button
                    htmlType="submit"
                    type="primary"
                    size="large"
                    className={classNames('btn btn-primary', { disabled: !canSubmit })}
                    block
                    disabled={!canSubmit}
                    loading={methods.formState.isSubmitting}
                >
                    Continue
                </Button>
            </Form>
        </AuthLayout>
    );
};

export default withoutAuth(SetupPasswordPage);
