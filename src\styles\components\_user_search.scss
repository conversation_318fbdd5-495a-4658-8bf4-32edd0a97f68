.user-search {
    position: relative;
    width: 100%;

    &-inner {
        @include flex(flex, column, nowrap, flex-start, stretch);
        background-color: $color-input-background;
        height: 100%;
        overflow: auto;
    }

    &.is-open {
        @include flex(flex, column, nowrap, flex-start, flex-start);
        background-color: $color-gray-5;
        width: calc(100% - 80px);
        height: calc(100% - 48px);
        left: 40px;
        position: absolute;
        top: 24px;
        z-index: 10;

        .actions {
            @include flex(flex, column, nowrap, flex-start, stretch);
            background-color: $color-input-background;
            bottom: 0;
            font-size: 16px;
            font-weight: $font-weight-bold;
            left: 0;
            line-height: 20px;
            margin-top: auto;
            position: sticky;
            width: 100%;
            z-index: 2;

            &-arrow {
                @include flex(flex, row, nowrap, center, center);
                border-bottom: 1px solid #e1e7f1;
                padding: 8px 4px 16px 4px;
                min-height: 37px;

                svg {
                    height: 12px !important;
                    stroke-width: 2px;
                }
            }

            .add-new-btn {
                color: $color-text !important;
                font-size: 16px !important;
                padding: 16px 18px;

                svg {
                    color: $color-text;
                    height: 16px !important;
                    width: 16px !important;
                }
            }
        }

        .icon-back {
            cursor: pointer;
            margin-left: 12px;
            margin-top: 12px;
            overflow: visible;
            transform: rotate(180deg);
        }

        .text-field-container {
            background-color: $color-input-background;
            margin-bottom: 0 !important;
            padding: 12px;
            position: sticky;
            top: 0;
            z-index: 2;

            &-inner {
                @include flex(flex, row, nowrap, space-between, center);
                background-color: white;
                padding: 0 12px;
                input {
                    background-color: white !important;
                    border: none;
                    border-radius: 0;
                    box-sizing: border-box;
                    flex: 1;
                    font-size: 16px;
                    font-weight: $font-weight-light;
                    line-height: 20px;
                    width: 100%;
                }

                .icon {
                    width: 16px !important;
                    height: 16px !important;
                }
            }
        }

        .user-option {
            @include flex(flex, row, nowrap, flex-start, center);
            cursor: pointer;
            padding: 12px 18px;
            position: relative;
            transition: all 0.3s ease;

            &.selected {
                background-color: #c4eae3;

                .icon {
                    color: $color-text;
                }
            }

            &:hover {
                background-color: #c4eae3;
            }

            .icon {
                height: 16px;
                margin-right: 16px;
                width: 16px;
            }

            .user-info {
                @include flex(flex, column, nowrap, space-between, flex-start);
                gap: 4px;

                .email {
                    color: $color-secondary-darker;
                    line-height: 18px;
                }

                .name {
                    color: $color-text;
                    font-weight: $font-weight-semi-bold;
                    line-height: 18px;
                }
            }
        }
    }

    .button {
        @include flex(flex, row, nowrap, space-between, center);
        background-color: $color-input-background;
        cursor: pointer;
        gap: 8px;
        padding: 16px 18px;
        width: 100%;

        .value {
            flex: 1;
            font-size: 16px;
            font-weight: $font-weight-light;
            line-height: 20px;
        }

        .chevron {
            width: 16px !important;
            height: 16px !important;
        }
    }

    label {
        display: block;
        font-size: 16px;
        font-weight: $font-weight-semi-bold;
        line-height: 130%;
        margin-bottom: 8px;
    }
}
