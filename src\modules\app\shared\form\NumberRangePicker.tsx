import { <PERSON>lide<PERSON> } from 'antd';
import classNames from 'classnames';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { formatCurrency } from '../../../../utils/currencyFormat';

interface Props {
    className?: string;
    disabled?: boolean;
    label?: string;
    max?: number;
    min?: number;
    name: string;
    step?: number;
    isRange?: boolean;
    defaultValue?: number | [number, number];
}

const NumberRangePicker: React.FC<Props> = ({
    className,
    disabled,
    label,
    max = 9999,
    min = 0,
    name,
    step = 0.1,
    isRange = true,
    defaultValue,
}) => {
    const { control, watch } = useFormContext();

    const range = watch(name, defaultValue);

    range;

    return (
        <Controller
            render={({ field, fieldState, formState }) => {
                return (
                    <div
                        className={classNames(
                            {
                                'form-group': true,
                                range: true,
                            },
                            className,
                        )}
                    >
                        {label && (
                            <label className="form-group-label" htmlFor={name}>
                                {label}
                            </label>
                        )}

                        {isRange ? (
                            <div className="range-indicator margin-bottom-16">
                                <span>{formatCurrency(range[0] || min, 0)}</span>
                                <span>-</span>
                                <span>{formatCurrency(range[1] || max, 0)}</span>
                            </div>
                        ) : null}

                        <div className="range-container">
                            {!isRange && (
                                <Slider
                                    {...field}
                                    min={min}
                                    max={max}
                                    step={step}
                                    defaultValue={(defaultValue as number) || 0}
                                    disabled={formState.isSubmitting || disabled}
                                />
                            )}

                            {isRange && (
                                <Slider
                                    {...field}
                                    range
                                    min={min || 0}
                                    max={max || 100}
                                    step={step}
                                    defaultValue={(defaultValue as [number, number]) || [0, 100]}
                                    disabled={formState.isSubmitting || disabled}
                                />
                            )}
                        </div>

                        {!!fieldState.error && (
                            <div
                                className={classNames(
                                    {
                                        note: true,
                                    },
                                    [fieldState.error.type],
                                )}
                            >
                                {fieldState.error.message}
                            </div>
                        )}
                    </div>
                );
            }}
            name={name}
            control={control}
        />
    );
};

export default NumberRangePicker;
