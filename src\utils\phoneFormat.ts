export function formatPhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) return '';

    const cleanedPhoneNumber = phoneNumber.replace(/\D/g, '');

    if (cleanedPhoneNumber.length === 10) {
        return `(${cleanedPhoneNumber.substring(0, 3)}) ${cleanedPhoneNumber.substring(
            3,
            6,
        )}-${cleanedPhoneNumber.substring(6)}`;
    } else if (cleanedPhoneNumber.length === 7) {
        return `${cleanedPhoneNumber.substring(0, 3)}-${cleanedPhoneNumber.substring(3)}`;
    } else {
        return phoneNumber;
    }
}

export const cleanFormattedPhoneNumber = (phoneNumber) => {
    return phoneNumber.replace(/\D/g, '');
};
