{"name": "Oope admin", "private": true, "version": "0.2.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "npm run lint:ts", "lint:ts": "tsc && npm run lint:eslint", "lint:eslint": "eslint 'src/**/*.{ts,tsx}'", "lint:fix": "eslint --fix src/**/*.{js,jsx,ts,tsx,json}", "format:ts": "prettier --write 'src/**/*.{ts,tsx}' && npm run lint:eslint --fix", "format": "npm run format:ts", "format:check": "prettier -c --write 'src/**/*.{ts,tsx}'", "test": "jest"}, "dependencies": {"@aws-sdk/client-chime-sdk-identity": "^3.363.0", "@aws-sdk/client-chime-sdk-messaging": "^3.363.0", "@aws-sdk/client-sts": "^3.363.0", "@hookform/resolvers": "^3.1.1", "@types/react-router-dom": "^5.3.3", "amazon-chime-sdk-js": "^3.15.0", "antd": "^5.10.0", "axios": "^1.8.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "classnames": "^2.3.2", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "easy-peasy": "^6.0.0", "eslint-config-prettier": "^8.8.0", "install": "^0.13.0", "just-throttle": "^4.2.0", "npm": "^10.8.2", "react": "^18.2.0", "react-avatar-editor": "^13.0.0", "react-datepicker": "^4.18.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.44.3", "react-infinite-scroll-component": "^6.1.0", "react-input-mask": "^3.0.0-alpha.2", "react-intersection-observer-hook": "^2.1.1", "react-pin-field": "^3.1.3", "react-router-dom": "6", "react-select": "^5.7.4", "react-toastify": "^9.1.3", "react-zoom-pan-pinch": "^3.1.0", "reflect-metadata": "^0.1.13"}, "overrides": {"cross-spawn": "^7.0.6"}, "devDependencies": {"@aws-sdk/types": "^3.357.0", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@babel/preset-typescript": "^7.18.6", "@svgr/webpack": "^8.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.36.1", "@vitejs/plugin-react": "^4.7.0", "eslint": "^8.46.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.0.2", "prettier": "^2.7.1", "sass": "^1.63.3", "typescript": "^4.6.4", "vite": "^7.0.6", "vite-plugin-eslint": "^1.8.1", "vite-plugin-static-copy": "^3.1.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^4.2.0"}, "jest": {"verbose": true, "roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFilesAfterEnv": [], "testMatch": ["<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "moduleNameMapper": {"\\.(css|less|scss|sass)$": "identity-obj-proxy"}}}