import {
    IsEnum,
    IsNotEmpty,
    IsObject,
    IsOptional,
    IsString,
    ValidateNested,
} from 'class-validator';
import { type ImageDimensions } from '../../../utils/imageUpload';
import { UserRole } from '../../user/types/User';

export class MessageMetadataDto {
    @IsNotEmpty()
    @ValidateNested()
    sender!: SenderMetadataDto;

    @IsOptional()
    @ValidateNested()
    attachment?: AttachmentMetaDto;
}

export class SenderMetadataDto {
    @IsNotEmpty()
    @IsString()
    userId!: string;

    @IsNotEmpty()
    @IsString()
    userName!: string;

    @IsNotEmpty()
    @IsString()
    profilePhotoUrl!: string;

    @IsNotEmpty()
    @IsEnum(UserRole)
    role!: UserRole;
}

export class AttachmentMetaDto {
    @IsNotEmpty()
    @IsString()
    url!: string;

    @IsNotEmpty()
    @IsObject()
    dimensions!: ImageDimensions;
}
