.transform {
    background-color: $color-background;

    &-content {
        cursor: grab;
        height: 100%;
        width: 100%;

        &.active {
            cursor: grabbing;
        }
    }

    &-tools {
        @include flex(flex, row, nowrap, space-between, center);
        left: 0;
        padding: 24px;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 5;

        &-zoom {
            &-overlay {
                border-radius: 0;

                ul {
                    background-color: $color-background-secondary !important;
                    border-radius: 0 !important;
                    li {
                        color: $color-gray-3 !important;
                    }
                }
            }
        }
    }

    &-wrapper {
        max-width: 100%;
        max-height: 100%;
    }
}
