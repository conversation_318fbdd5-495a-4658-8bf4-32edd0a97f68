import { Space } from 'antd';
import classNames from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as IconAdd } from '../../../assets/icons/add-sharp.svg';
import { ReactComponent as IconCheck } from '../../../assets/icons/checkmark-sharp.svg';
import { ReactComponent as IconChevron } from '../../../assets/icons/chevron-down.svg';
import { ReactComponent as IconSearch } from '../../../assets/icons/search-outline.svg';
import { ReactComponent as IconClose } from '../../../assets/icons/icon-close.svg';
import { ReactComponent as IconBack } from '../../../assets/icons/arrow-forward-sharp.svg';
import useDebouncedValue from '../../../hooks/useDebouncedValue';
import useInfiniteScroll from '../../../hooks/useInfiniteScroll';
import { useStoreActions, useStoreState } from '../../../store';
import UserDto from '../../../store/user/dto/UserDto';
import { RawUserRole } from '../../../store/user/types/User';
import { PaginationDirection } from '../../../utils/paginate';
import OutsideClickListener from './OutsideClickListener';
import Spinner from './Spinner';

export type MenuItem = {
    name: string;
    email: string;
};

type Role = 'adjuster' | 'admin';

interface Props {
    onSelect: (user: UserDto) => void;
    label?: string;
    initialUser?: UserDto;
    role: Role;
}

const getFilterRoleParams = (role: Role) => {
    if (role === 'adjuster') {
        return [
            {
                field: 'role',
                operator: '=',
                value: RawUserRole.ADJUSTER,
            },
        ];
    } else {
        return [
            {
                field: 'role',
                operator: 'in',
                value: [RawUserRole.ADMIN, RawUserRole.SUPER_ADMIN],
            },
        ];
    }
};

const USER_LIST_LIMIT = 20;

const UserSearchBox: React.FC<Props> = ({ label, initialUser, role, onSelect }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [selectedUser, setSelectedUser] = useState<UserDto | undefined>(initialUser);
    const [showMoreIcon, setShowMoreIcon] = useState(true);
    const userSearchRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    const debouncedTerm = useDebouncedValue<string>(searchTerm, 500);

    const { list: users, hasMore } = useStoreState((state) => state.user);
    const { getAndAppend, unload } = useStoreActions((actions) => actions.user);

    const navigate = useNavigate();

    const handleLoadMore = useCallback(
        async (searchTerm: string) => {
            await getAndAppend({
                search_term: searchTerm,
                order_by: 'email',
                direction: PaginationDirection.ASC,
                limit: USER_LIST_LIMIT,
                params: getFilterRoleParams(role),
            });
            inputRef.current?.focus();
        },
        [getAndAppend, role],
    );

    const handleSelect = (user: UserDto) => {
        setSelectedUser(user);
        unload();
        setIsOpen(false);
        onSelect(user);
    };

    const [loaderRef] = useInfiniteScroll({
        loading: users.loading,
        onLoadMore: () => {
            handleLoadMore(searchTerm);
        },
        hasMore,
    });

    useEffect(() => {
        if (!isOpen) {
            return;
        }
        unload();
        handleLoadMore(debouncedTerm);
    }, [debouncedTerm, handleLoadMore, isOpen, unload]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        const handleEscape = (event) => {
            if (event.key === 'Escape') {
                setIsOpen(false);
            }
        };

        if (isOpen) {
            window.addEventListener('keydown', handleEscape);
        }

        return () => {
            window.removeEventListener('keydown', handleEscape);
        };
    }, [isOpen, unload]);

    useEffect(() => {
        function handleScroll() {
            const element = userSearchRef.current;

            if (element) {
                const isScrolledToBottom =
                    element.scrollHeight - element.scrollTop === element.clientHeight;
                if (isScrolledToBottom) {
                    setShowMoreIcon(false);
                } else {
                    setShowMoreIcon(true);
                }
            }
        }

        if (!isOpen) {
            return;
        }

        const element = userSearchRef.current;

        if (element) {
            if (element.scrollHeight <= element.clientHeight) {
                setShowMoreIcon(false);
            }

            element.addEventListener('scroll', handleScroll);

            return () => {
                element.removeEventListener('scroll', handleScroll);
            };
        }

        return () => {};
    }, [isOpen]);

    useEffect(() => {
        const element = userSearchRef.current;

        if (!isOpen || !element) {
            return;
        }

        const shouldHideArrow =
            element.scrollHeight - element.scrollTop === element.clientHeight ||
            element.scrollHeight <= element.clientHeight;

        setShowMoreIcon(!shouldHideArrow);
    }, [isOpen, users.value]);

    return (
        <div className={classNames('user-search', { 'is-open': isOpen })}>
            {!isOpen && (
                <>
                    {label && <label>{label}</label>}
                    <div
                        className="button"
                        onClick={() => {
                            setIsOpen(true);
                        }}
                    >
                        <div className="value">{selectedUser ? selectedUser.name : 'Select'}</div>
                        <IconChevron className="chevron" />
                    </div>
                </>
            )}
            {isOpen && (
                <OutsideClickListener
                    onOutsideClick={() => {
                        setIsOpen(false);
                    }}
                >
                    <div className="form user-search-inner" ref={userSearchRef}>
                        <IconBack className="icon-back" onClick={() => setIsOpen(false)} />
                        <div className="text-field-container">
                            <div className="text-field-container-inner">
                                <input
                                    ref={inputRef}
                                    value={searchTerm}
                                    placeholder="Search..."
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                                {searchTerm ? (
                                    <IconClose
                                        className="cursor"
                                        onClick={() => setSearchTerm('')}
                                    />
                                ) : (
                                    <IconSearch />
                                )}
                            </div>
                        </div>
                        {!!searchTerm && users.loading && (
                            <div className="flex-center margin-top-32">
                                <Spinner />
                            </div>
                        )}
                        {users.value.map((user, key) => (
                            <UserOption
                                key={key}
                                user={user}
                                isSelected={selectedUser?.id === user.id}
                                onSelect={handleSelect}
                            />
                        ))}
                        {!users.loading && hasMore && <div ref={loaderRef}></div>}
                        {users.loaded && !users.value.length && (
                            <div className="flex-center">No Results</div>
                        )}
                        <div className="actions">
                            <div className="actions-arrow">
                                {showMoreIcon && <IconChevron />}
                                {!showMoreIcon && users.loading && <Spinner />}
                            </div>
                            <div
                                className="add-new-btn cursor"
                                onClick={() => navigate('/settings/users/new')}
                            >
                                <Space>
                                    <IconAdd />
                                    Add New
                                </Space>
                            </div>
                        </div>
                    </div>
                </OutsideClickListener>
            )}
        </div>
    );
};
export default UserSearchBox;

interface UserOptionProps {
    user: UserDto;
    isSelected?: boolean;
    onSelect: (user: UserDto) => void;
}
const UserOption: React.FC<UserOptionProps> = ({ user, isSelected, onSelect }) => {
    return (
        <div
            className={classNames('user-option', { selected: isSelected })}
            onClick={() => onSelect(user)}
        >
            {isSelected && <IconCheck className="icon" />}
            <div className="user-info">
                <div className="name">{user.name}</div>
                <div className="email">{user.email}</div>
            </div>
        </div>
    );
};
