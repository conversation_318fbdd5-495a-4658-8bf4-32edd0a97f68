import { Pagination as AntPagination } from 'antd';
import { ReactComponent as IconArrow } from '../../../assets/icons/arrow-forward-sharp.svg';
import classNames from 'classnames';

interface Props {
    currentPage: number;
    perPage: number;
    total: number;
    simple?: boolean;
    onChange: (pageNumber) => void;
    className?: string;
}

const Pagination: React.FC<Props> = ({
    currentPage,
    perPage,
    total,
    onChange,
    simple = false,
    className,
}) => {
    const isLastPage = currentPage >= Math.ceil(total / perPage);

    if (Math.ceil(total / perPage) <= 1) {
        return null;
    }

    return (
        <div
            className={classNames('flex-space pagination-container', className, {
                simple,
            })}
        >
            <div
                className="flex-space pagination-prev"
                onClick={() => currentPage > 1 && onChange(currentPage - 1)}
            >
                {currentPage > 1 && (
                    <>
                        <IconArrow />
                        <span>Previous</span>
                    </>
                )}
            </div>
            {!simple && (
                <AntPagination
                    className="pagination"
                    total={total}
                    pageSize={perPage}
                    current={currentPage}
                    showSizeChanger={false}
                    onChange={(page) => onChange(page)}
                    simple={simple}
                    itemRender={(page, type, originalElement) => {
                        if (type === 'prev' || type === 'next') {
                            return null;
                        }

                        return originalElement;
                    }}
                />
            )}
            <div
                className="flex-space pagination-next"
                onClick={() => !isLastPage && onChange(currentPage + 1)}
            >
                {!isLastPage && (
                    <>
                        <span>Next</span>
                        <IconArrow />
                    </>
                )}
            </div>
        </div>
    );
};
export default Pagination;
