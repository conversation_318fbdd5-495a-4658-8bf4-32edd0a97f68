import classNames from 'classnames';
import UserDto from '../../../../store/user/dto/UserDto';
import Avatar from '../../../app/shared/Avatar';
import { ReactComponent as IconCheck } from '../../../../assets/icons/checkmark-sharp.svg';
import { useState } from 'react';

interface Props {
    adjuster: UserDto;
    canSend?: boolean;
    onToggleSend?: VoidFunction;
}

const AdjusterInfo: React.FC<Props> = ({ adjuster, canSend, onToggleSend }) => {
    const [selected, setSelected] = useState(false);

    const onToggle = () => {
        setSelected(!selected);
        onToggleSend && onToggleSend();
    };

    return (
        <div className="info-send margin-bottom-24">
            <div className="info-subheading margin-bottom-8">Send to</div>
            <div className="info-send-policyholder ">
                <div className="flex-start">
                    <Avatar
                        size="medium"
                        className="margin-right-12"
                        photoUrl={adjuster.profilePhoto}
                        name={adjuster.name}
                    />
                    <div className="info-send-policyholder-info ">
                        <div className="name">{adjuster.name}</div>
                        <div className="email">{adjuster.email}</div>
                    </div>
                </div>
                {canSend && (
                    <div
                        onClick={() => onToggle()}
                        className={classNames('info-send-policyholder-checkbox', {
                            selected: selected,
                        })}
                    >
                        {selected && <IconCheck />}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AdjusterInfo;
