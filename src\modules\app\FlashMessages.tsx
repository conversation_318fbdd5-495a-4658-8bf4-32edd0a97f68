import React from 'react';
import { Slide, ToastOptions, toast } from 'react-toastify';
import { TypeOptions } from 'react-toastify/dist/types';
import { ReactComponent as IconClose } from '../../assets/icons/close.svg';
import { ReactComponent as IconSuccess } from '../../assets/icons/checkmark-done-sharp.svg';
import { ReactComponent as IconWarn } from '../../assets/icons/warning-outline.svg';
import { ReactComponent as IconError } from '../../assets/icons/close-circle-outline.svg';

import classNames from 'classnames';

enum ToastTypeOptions {
    INFO = 'info',
    WARN = 'warning',
    ERROR = 'error',
    SUCCESS = 'success',
}

const ToastIcons = {
    error: <IconError />,
    success: <IconSuccess />,
    warning: <IconWarn />,
};

const message = (type: TypeOptions = ToastTypeOptions.INFO, content: React.ReactNode) => {
    const CloseButton = ({ closeToast }) => (
        <IconClose className="icon-close" onClick={closeToast} />
    );

    const options: ToastOptions = {
        type: type,
        draggable: false,
        closeOnClick: false,
        icon: false,
        closeButton: CloseButton,
        transition: Slide,
    };

    toast(
        <>
            <div
                className={classNames('icon', {
                    'icon-error': type === ToastTypeOptions.ERROR,
                    'icon-success': type === ToastTypeOptions.SUCCESS,
                    'icon-warning': type === ToastTypeOptions.WARN,
                })}
            >
                {ToastIcons[type]}
            </div>
            <div className="content">
                <span
                    className={classNames('status', {
                        'status-error': type === ToastTypeOptions.ERROR,
                        'status-success': type === ToastTypeOptions.SUCCESS,
                        'status-warning': type === ToastTypeOptions.WARN,
                    })}
                >
                    {type}
                </span>
                <span className="message">{content}</span>
            </div>
        </>,
        options,
    );
};
const FlashMessages = {
    success: (content: React.ReactNode) => message(ToastTypeOptions.SUCCESS, content),
    error: (content: React.ReactNode) => message(ToastTypeOptions.ERROR, content),
    warn: (content: React.ReactNode) => message(ToastTypeOptions.WARN, content),
};

export default FlashMessages;
