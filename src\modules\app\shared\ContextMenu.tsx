import type { MenuProps } from 'antd';
import { Dropdown, Tooltip } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';

import classNames from 'classnames';
import React, { ReactNode, useState } from 'react';
import { ReactComponent as IconClose } from '../../../assets/icons/close.svg';
import { ReactComponent as IconDots } from '../../../assets/icons/ellipsis-vertical.svg';

interface Props {
    items: MenuProps['items'];
    onItemClick: (item: MenuItemType | undefined) => void;
    className?: string;
    overlayClassName?: string;
    toolTipContent?: ReactNode;
    hasTooltip?: boolean;
}

const ContextMenu: React.FC<Props> = ({
    items,
    onItemClick,
    className,
    overlayClassName,
    toolTipContent = 'More options',
    hasTooltip = true,
}) => {
    const [isVisible, setIsVisible] = useState(false);

    const onClick: MenuProps['onClick'] = (info) => {
        setIsVisible(false);
        const item = items?.find((item) => item?.key === info.key);
        onItemClick(item);
    };

    return (
        <Dropdown
            menu={{ items, onClick }}
            trigger={['click']}
            className={classNames('contextmenu', className)}
            overlayClassName={classNames('contextmenu-overlay', overlayClassName)}
            onOpenChange={setIsVisible}
        >
            <Tooltip
                title={hasTooltip ? toolTipContent : undefined}
                overlayClassName="contextmenu-tooltip"
                overlayInnerStyle={{ backgroundColor: '$003049' }}
            >
                <div>
                    {!isVisible ? (
                        <IconDots className="cursor contextmenu-icon-menu" />
                    ) : (
                        <IconClose className="contextmenu-icon-close" />
                    )}
                </div>
            </Tooltip>
        </Dropdown>
    );
};

export default ContextMenu;
