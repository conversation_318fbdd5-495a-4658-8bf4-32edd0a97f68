import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Space } from 'antd';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import { ReactComponent as LogoImage } from '../../assets/images/logo.svg';
// import { ReactComponent as ContactIcon } from '../../assets/icons/contact-icon.svg';
import { useStoreActions } from '../../store';
import { LoginDto } from '../../store/auth/dto/LoginFormDto';
import Form from '../app/shared/form/Form';
import PasswordField from '../app/shared/form/PasswordField';
import TextField from '../app/shared/form/TextField';

interface Props {
    onLoginRequest: (LoginDto) => void;
}

const RequestLoginForm: React.FC<Props> = ({ onLoginRequest }) => {
    const { requestLogin } = useStoreActions((actions) => actions.auth);

    const location = useLocation();
    const navigate = useNavigate();

    const methods = useForm<LoginDto>({
        resolver: classValidatorResolver(LoginDto),
        defaultValues: {
            email: '',
            code: '',
        },
    });

    const onSubmit = async (fields) => {
        try {
            await requestLogin(fields);
            onLoginRequest(fields);
        } catch (err: any) {
            console.error(err.message);
            methods.setError('email', { message: 'Email or password are incorrect' });
            methods.setError('password', { message: 'Email or password are incorrect' });
        }
    };

    useEffect(() => {
        methods.reset();
    }, [location, methods]);

    const email = methods.watch('email');

    return (
        <Form className="auth-form" methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
            <LogoImage className="auth-form-logo" />
            {/* <ContactIcon className="auth-form-contact" /> */}
            <Space.Compact direction="vertical" block>
                <div className="heading h4 margin-bottom-32">Login to Your Account</div>
                <TextField name="email" label="Email" placeholder="Email" type="email" />
                <PasswordField
                    name="password"
                    label="Password"
                    placeholder="Password"
                    className="auth-form-password margin-bottom-8"
                />
                <Button
                    type="link"
                    onClick={() => navigate(`/auth/forgot-password/${email}`)}
                    className="btn-text auth-form-forgot-password"
                >
                    Forgot Password?
                </Button>
            </Space.Compact>

            <Button
                className="btn btn-primary"
                htmlType="submit"
                size="large"
                loading={methods.formState.isSubmitting}
                block
            >
                Login
            </Button>
        </Form>
    );
};

export default RequestLoginForm;
