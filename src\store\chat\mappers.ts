import { ChannelMessageSummary } from '@aws-sdk/client-chime-sdk-messaging';
import { plainToInstance } from 'class-transformer';
import { MessageDto } from './dto/MessageDto';
import ChatConfigDto from './dto/ChatConfigDto';
import ChatCredentialsDto from './dto/ChatCredentialsDto';

export const mapMessage = (rawMessage: ChannelMessageSummary): MessageDto => {
    const {
        Content,
        CreatedTimestamp,
        LastEditedTimestamp,
        LastUpdatedTimestamp,
        Metadata,
        MessageId,
    } = rawMessage;

    if (!Content || !CreatedTimestamp || !Metadata || !MessageId)
        throw new Error('Unparsable message');

    return plainToInstance(MessageDto, {
        id: MessageId,
        text: Content,
        meta: JSON.parse(Metadata),

        createdAt: CreatedTimestamp?.toISOString(),
        editedAt: LastEditedTimestamp?.toISOString(),
        updatedAt: LastUpdatedTimestamp?.toISOString(),
    });
};

export const mapSentMessage = (rawMessage: ChannelMessageSummary): MessageDto => {
    const {
        Content,
        CreatedTimestamp,
        LastEditedTimestamp,
        LastUpdatedTimestamp,
        Metadata,
        MessageId,
    } = rawMessage;

    if (!Content || !CreatedTimestamp || !Metadata || !MessageId)
        throw new Error('Unparsable message');

    return plainToInstance(MessageDto, {
        id: MessageId,
        text: Content,
        meta: JSON.parse(Metadata),

        createdAt: CreatedTimestamp,
        editedAt: LastEditedTimestamp,
        updatedAt: LastUpdatedTimestamp,
    });
};

export const mapMessages = (rawMessages: ChannelMessageSummary[]): MessageDto[] => {
    return rawMessages.reverse().map((rawMessage) => mapMessage(rawMessage));
};

export const mapChatConfig = (raw: any) =>
    plainToInstance(ChatConfigDto, {
        chatUserArn: raw.chat_user_arn,
        chatChannelArn: raw.chat_channel_arn,
        endpointUrl: raw.endpoint_url,
        region: raw.region,
    });

export const simpleDecrypt = (encryptedText, key) => {
    const decoded = atob(encryptedText); // base64 decode
    let result = '';

    for (let i = 0; i < decoded.length; i++) {
        const charCode = decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length);
        result += String.fromCharCode(charCode);
    }

    return result;
};

export const mapCredentials = ({ AccessKeyId, SecretAccessKey, SessionToken, Expiration }: any) =>
    plainToInstance(ChatCredentialsDto, {
        accessKeyId: simpleDecrypt(AccessKeyId, 'oopeclient'),
        secretAccessKey: simpleDecrypt(SecretAccessKey, 'oopeclient'),
        sessionToken: simpleDecrypt(SessionToken, 'oopeclient'),
        expiration: Expiration,
    });
