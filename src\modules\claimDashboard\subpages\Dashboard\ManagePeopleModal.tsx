import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { ClaimDto, ClaimInviteDto } from '../../../../store/claim/dto/ClaimDto';
import { PolicyholderStatus } from '../../../../store/policyHolder/types/PolicyHolder';
import FlashMessages from '../../../app/FlashMessages';
import Avatar from '../../../app/shared/Avatar';
import Modal from '../../../app/shared/Modal';
import UserSearchBox from '../../../app/shared/UserSearchBox';
import Form from '../../../app/shared/form/Form';
import classNames from 'classnames';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
    claim: ClaimDto;
}

const ManagePeopleModal: React.FC<Props> = ({ show, onClose, claim, onSuccess }) => {
    const { policyHolder } = claim;

    const [isLoading, setIsLoading] = useState<'assign' | 'invite' | false>(false);

    const { inviteAll, invitePolicyholder } = useStoreActions((actions) => actions.claim);

    const methods = useForm<ClaimInviteDto>({
        resolver: classValidatorResolver(ClaimInviteDto),
        defaultValues: {
            policy_holder_id: claim.policyHolder.id,
            user_id: claim.adjuster?.id,
            admin_id: claim.admin?.id,
        },
    });

    const onAssign = async (fields) => {
        setIsLoading('assign');
        try {
            await inviteAll({ id: claim.id, dto: fields });
            FlashMessages.success('Invitations sent');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Action failed');
        } finally {
            setIsLoading(false);
        }
    };

    const onInvitePolicyholder = async () => {
        setIsLoading('invite');
        try {
            await invitePolicyholder(claim.id);
            FlashMessages.success('Policyholder invited');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to invite policyholder');
        } finally {
            setIsLoading(false);
            onClose();
        }
    };

    const adjusterId = methods.watch('user_id');
    const adminId = methods.watch('admin_id');

    const submitDisabled = useMemo(() => {
        return adjusterId === claim.adjuster?.id && adminId === claim.admin?.id;
    }, [adjusterId, adminId, claim.adjuster?.id, claim.admin?.id]);

    return (
        <Modal
            show={show}
            onClose={onClose}
            title="Manage People"
            subtitle="Invite and assign people to this claim. Recipients will only receive the invitation one time. If a new Adjuster or OOPE Rep is assigned to the claim, only the new assignee will receive the invitation."
            className="people-modal"
            escapeClose={false}
        >
            <Form
                methods={methods}
                className="people-modal-body"
                onSubmit={methods.handleSubmit(onAssign)}
            >
                <div className="invitation">
                    <div className="invitation-label">Invitation</div>
                    <div className="invitation-policyholder">
                        <Avatar
                            size="medium"
                            photoUrl={policyHolder.profilePhoto}
                            name={policyHolder.name}
                        />
                        <div className="info">
                            <div className="name">{policyHolder.name}</div>
                            <div className="role">Policyholder</div>
                        </div>
                    </div>
                </div>
                {policyHolder.status !== PolicyholderStatus.ACTIVE ? (
                    <Button
                        onClick={() => onInvitePolicyholder()}
                        size="large"
                        className={classNames('btn-primary', {
                            disabled: policyHolder.status === PolicyholderStatus.ACTIVE,
                        })}
                        htmlType="button"
                        disabled={policyHolder.status === PolicyholderStatus.ACTIVE}
                        loading={isLoading === 'invite'}
                    >
                        Send Invitation
                    </Button>
                ) : (
                    <span className="font-light">Invitation Sent and Account Created</span>
                )}
                <hr />
                <UserSearchBox
                    label="Adjuster"
                    initialUser={claim.adjuster}
                    onSelect={(user) => {
                        methods.setValue('user_id', user.id);
                    }}
                    role="adjuster"
                />
                <UserSearchBox
                    label="OOPE Rep"
                    initialUser={claim.admin}
                    onSelect={(user) => {
                        methods.setValue('admin_id', user.id);
                    }}
                    role="admin"
                />
                {!!methods.formState.errors && methods.formState.isSubmitted && (
                    <div className="note">Both the Adjuster and OOPE Rep have to be selected</div>
                )}
                <Button
                    loading={isLoading === 'assign'}
                    htmlType="submit"
                    size="large"
                    disabled={submitDisabled}
                    className={classNames('btn-primary', { disabled: submitDisabled })}
                >
                    Assign Users & Send Invitations
                </Button>
            </Form>
        </Modal>
    );
};
export default ManagePeopleModal;
