.align {
    &-left {
        text-align: left;
    }

    &-right {
        text-align: right;
    }

    &-center {
        text-align: center;
    }

    &-justify {
        text-align: justify;
    }

    &-self {
        &-center {
            align-self: center;
        }
    }
}

.font {
    &-small {
        color: $color-gray-3;
        font-size: 14px;
        font-weight: $font-weight-regular;
        line-height: 130%; /* 18.2px */
    }

    &-medium {
        font-size: 18px;
        line-height: 130%; /* 23.4px */
    }

    &-regular {
        font-size: 16px;
        line-height: 130%; /* 20.8px */
    }

    &-light {
        font-size: 14px;
        font-weight: $font-weight-light;
        line-height: 130%; /* 18.2px */
    }

    &-gray {
        color: $color-gray-3;
    }
}

.text {
    &-uppercase {
        text-transform: uppercase;
    }

    &-lowercase {
        text-transform: lowercase;
    }

    &-capitalize {
        text-transform: capitalize;
    }

    &-underline {
        text-decoration: underline;
    }
}

.margin {
    &-30 {
        margin: 30px;
    }

    &-bottom {
        &-0 {
            margin-bottom: 0;
        }

        &-4 {
            margin-bottom: 5px;
        }

        &-8 {
            margin-bottom: 8px;
        }

        &-12 {
            margin-bottom: 12px;
        }

        &-16 {
            margin-bottom: 16px;
        }

        &-24 {
            margin-bottom: 24px;
        }

        &-32 {
            margin-bottom: 32px;
        }

        &-35 {
            margin-bottom: 35px;
        }

        &-40 {
            margin-bottom: 40px;
        }
    }

    &-left {
        &-0 {
            margin-left: 0;
        }

        &-4 {
            margin-left: 5px;
        }

        &-8 {
            margin-left: 8px;
        }

        &-12 {
            margin-left: 12px;
        }

        &-16 {
            margin-left: 16px;
        }

        &-24 {
            margin-left: 24px;
        }

        &-32 {
            margin-left: 32px;
        }
    }

    &-right {
        &-0 {
            margin-right: 0;
        }

        &-4 {
            margin-right: 4px;
        }

        &-8 {
            margin-right: 8px;
        }

        &-12 {
            margin-right: 12px;
        }

        &-16 {
            margin-right: 16px;
        }

        &-24 {
            margin-right: 24px;
        }

        &-32 {
            margin-right: 32px;
        }

        &-40 {
            margin-right: 40px;
        }

        &-auto {
            margin-right: auto;
        }
    }

    &-top {
        &-0 {
            margin-top: 0;
        }

        &-4 {
            margin-top: 4px;
        }

        &-8 {
            margin-top: 8px;
        }

        &-12 {
            margin-top: 12px;
        }

        &-16 {
            margin-top: 16px;
        }

        &-24 {
            margin-top: 24px;
        }

        &-32 {
            margin-top: 32px;
        }
    }
}

.responsive-img {
    @include responsive-img();
}

.mobile {
    &-hide {
        @include breakpoint(sm) {
            display: none !important;
        }
    }

    &-show {
        display: none;

        @include breakpoint(sm) {
            display: block;
        }
    }
}

.tablet {
    &-hide {
        @include breakpoint(md) {
            display: none !important;
        }
    }

    &-show {
        display: none;

        @include breakpoint(md) {
            display: block;
        }
    }
}

.hide {
    display: none !important;
}

.color {
    &-error {
        color: $color-error !important;
    }

    &-success {
        color: $color-success !important;
    }

    &-warning {
        color: $color-warning !important;
    }

    &-secondary {
        color: $color-secondary !important;

        &.darker {
            color: $color-secondary-darker !important;
        }
    }
}

.flex {
    &-start {
        @include flex(flex, row, wrap, flex-start, center);

        &.start {
            align-items: flex-start;
        }
    }

    &-space {
        @include flex(flex, row, wrap, space-between, center);

        &.start {
            align-items: flex-start;
        }
    }

    &-around {
        @include flex(flex, row, wrap, space-around, center);
    }

    &-end {
        @include flex(flex, row, wrap, flex-end, center);
    }

    &-center {
        @include flex(flex, row, wrap, center, center);
    }

    &-nowrap {
        flex-wrap: nowrap;
    }

    &-col {
        @include flex(flex, column, nowrap, center, stretch);
    }
}

.cursor {
    cursor: pointer;
}

.display-none {
    display: none;
}

.block {
    width: 100%;
}

.box {
    background-color: $color-background-secondary;
    padding: 24px;
    width: 100%;
}

.contact-method {
    text-transform: capitalize;
}

.lowercase {
    text-transform: lowercase !important;
}

.word-break {
    word-wrap: break-word !important;
}
