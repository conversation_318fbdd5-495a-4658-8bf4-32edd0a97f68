import { Input } from 'antd';
import classNames from 'classnames';
import { Controller, useFormContext } from 'react-hook-form';
import { ReactComponent as IconEyeOff } from '../../../../assets/icons/eye-off.svg';
import { ReactComponent as IconEyeOn } from '../../../../assets/icons/eye-on.svg';
import { useState } from 'react';

interface Props {
    name: string;
    label?: string;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
    required?: boolean;
}

const PasswordField: React.FC<Props> = ({
    name,
    label,
    value,
    placeholder,
    disabled,
    required,
    className,
}) => {
    const [isShowPassword, setIsShowPassword] = useState(false);

    const { control, getValues, setValue } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState, formState }) => (
                <>
                    <div
                        className={classNames(
                            {
                                'text-field-container': true,
                            },
                            className,
                        )}
                    >
                        {label && <label htmlFor={name}>{label}</label>}
                        <div className="text-field-container-inner">
                            <Input
                                autoComplete="off"
                                {...field}
                                type={isShowPassword ? 'text' : 'password'}
                                disabled={disabled || formState.isSubmitting}
                                placeholder={placeholder}
                                value={value || getValues(name)}
                                onChange={(v) => setValue(name, v.target.value)}
                                required={required}
                            />
                            <span
                                className="icon icon-after"
                                onClick={() => setIsShowPassword((show) => !show)}
                            >
                                {isShowPassword ? <IconEyeOn /> : <IconEyeOff />}
                            </span>
                        </div>
                        {!!fieldState.error && (
                            <div
                                className={classNames(
                                    {
                                        note: true,
                                    },
                                    [fieldState.error.type],
                                )}
                            >
                                {fieldState.error.message}
                            </div>
                        )}
                    </div>
                </>
            )}
        />
    );
};

export default PasswordField;
