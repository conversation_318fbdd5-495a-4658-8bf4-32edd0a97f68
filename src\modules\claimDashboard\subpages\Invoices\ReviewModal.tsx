import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { InvoiceApproveDto, InvoiceRejectDto } from '../../../../store/invoice/dto/InvoiceDto';
import FlashMessages from '../../../app/FlashMessages';
import Modal from '../../../app/shared/Modal';
import Form from '../../../app/shared/form/Form';
import TextField from '../../../app/shared/form/TextField';

interface Props {
    show: boolean;
    onSuccess: VoidFunction;
    onClose: VoidFunction;
    isApprove: boolean;
    invoiceId: string;
}

const getDto = (isApprove) => (isApprove ? InvoiceApproveDto : InvoiceRejectDto);

const ReviewModal: React.FC<Props> = ({ show, onSuccess, onClose, isApprove, invoiceId }) => {
    const [isLoading, setIsLoading] = useState(false);

    const { approve, reject } = useStoreActions((actions) => actions.invoice);

    const methods = useForm({
        resolver: classValidatorResolver(getDto(isApprove)),
        defaultValues: { note: '', status: isApprove ? 'approved' : 'rejected' },
    });

    const onSubmit = async (fields) => {
        setIsLoading(true);
        try {
            isApprove
                ? await approve({ id: invoiceId, dto: fields })
                : await reject({ id: invoiceId, dto: fields });
            FlashMessages.success(`Invoice ${isApprove ? 'Approved' : 'Rejected'}`);
            onClose();
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error(`Failed To ${isApprove ? 'Approve' : 'Reject'}`);
        } finally {
            setIsLoading(false);
        }
    };

    const title = isApprove ? 'Approve Invoice' : 'Reject Invoice';
    const subtitle = isApprove
        ? 'Please feel free to enter any details regarding the invoice below. This is not required for an Approved Invoice.'
        : 'Please enter the details of why the invoice is being rejected. This is a required field.';

    const label = isApprove ? (
        <span>
            Note <span className="font-small">(Optional)</span>
        </span>
    ) : (
        'Note'
    );

    return (
        <Modal show={show} onClose={onClose} title={title} subtitle={subtitle}>
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <TextField type="textarea" name="note" label={label}></TextField>
                <Button
                    type="primary"
                    size="large"
                    htmlType="submit"
                    className="btn-primary"
                    loading={isLoading}
                    block
                >
                    {isApprove ? 'Approve Invoice' : 'Reject Invoice'}
                </Button>
                <Button
                    type="link"
                    size="large"
                    onClick={() => onClose()}
                    disabled={isLoading}
                    block
                >
                    Go Back
                </Button>
            </Form>
        </Modal>
    );
};
export default ReviewModal;
