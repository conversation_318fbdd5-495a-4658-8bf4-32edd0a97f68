import { useNavigate } from 'react-router-dom';

import { useState } from 'react';
import withoutAuth from '../../hooks/withoutAuth';
import { LoginDto } from '../../store/auth/dto/LoginFormDto';
import AuthLayout from '../app/layouts/AuthLayout';
import CodeLoginForm from './CodeLoginForm';
import RequestLoginForm from './RequestLoginForm';

const LoginPage: React.FC = () => {
    const [hasSubmittedData, setHasSubmittedData] = useState(false);
    const [loginDto, setLoginDto] = useState<LoginDto | undefined>();

    const navigate = useNavigate();

    const onLoginRequest = async (fields) => {
        setLoginDto(fields);
        setHasSubmittedData(true);
    };

    const onLogin = async () => {
        navigate('/');
    };

    const onBack = () => {
        setHasSubmittedData(false);
    };

    return (
        <AuthLayout>
            {!hasSubmittedData && <RequestLoginForm onLoginRequest={onLoginRequest} />}
            {hasSubmittedData && loginDto && (
                <CodeLoginForm onLogin={onLogin} loginInfo={loginDto} onBack={onBack} />
            )}
        </AuthLayout>
    );
};

export default withoutAuth(LoginPage);
