import { IsEnum, IsNotEmpty, IsNumber, IsO<PERSON>al, IsString, Max, <PERSON> } from 'class-validator';
import { PaginationDirection } from '../utils/paginate';

export default class PaginationDto {
    @IsOptional()
    @IsNumber()
    @Min(1)
    page?: number;

    @IsOptional()
    @IsNumber()
    @Min(1)
    @Max(100)
    limit?: number;

    @IsNotEmpty()
    @IsString()
    declare order_by: string;

    @IsNotEmpty()
    @IsString()
    @IsEnum(PaginationDirection)
    direction!: PaginationDirection;
}
