server {
    listen      80;
    server_name localhost;
    root        /usr/share/nginx/html;

    # Hide version numbers
    server_tokens off;

    # SEO and indexing protection
    add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive" always;

    # Enable Gzip compression
    gzip on;
    gzip_comp_level 5;
    gzip_types text/css application/javascript image/svg+xml;

    # Custom error pages
    error_page 404 /index.html;
    error_page 500 502 503 504 /index.html;

    # Main location block
    location / {
        try_files $uri $uri.html $uri/ @rules =404;
    }

    # Limit upload size
    client_max_body_size 20M;
}
