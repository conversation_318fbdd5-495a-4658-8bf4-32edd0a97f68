# builder
FROM node:20-alpine AS builder

COPY . /app

WORKDIR /app

RUN test -d ./dist || test -d ./node_modules || npm install
RUN test -d ./dist || npm run build

# app
FROM nginx:1.25-alpine

# Upgrade all packages and install libxml2
RUN apk --no-cache --update upgrade && \
    apk add --no-cache libxml2

COPY /nginx.conf /etc/nginx/conf.d/default.conf

RUN rm -rf /usr/share/nginx/html/*
COPY --from=builder app/dist/ /usr/share/nginx/html

ENV PORT=80
EXPOSE ${PORT}

CMD [ "nginx", "-g", "daemon off;" ]
