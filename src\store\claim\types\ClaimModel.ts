import { Action, Thunk } from 'easy-peasy';
import { Resource, ResourcePagination } from '../../app/types';
import { ClaimDto, ClaimInviteDto, ClaimPaginateDto } from '../dto/ClaimDto';

export default interface ClaimModel {
    // state
    claim: ClaimDto | undefined;
    latestClaim: ClaimDto | undefined;
    list: Resource<ClaimDto[]>;
    pagination: ResourcePagination | null;

    // actions
    load: Action<ClaimModel, ClaimDto[]>;
    loading: Action<ClaimModel>;
    loadPagination: Action<ClaimModel, ResourcePagination>;
    unload: Action<ClaimModel>;
    setClaim: Action<ClaimModel, ClaimDto>;
    unloadClaim: Action<ClaimModel>;
    // thunks
    get: Thunk<ClaimModel, ClaimPaginateDto>;
    getClaim: Thunk<ClaimModel, string>;
    add: Thunk<ClaimModel, string>;
    inviteAll: Thunk<ClaimModel, { id: string; dto: ClaimInviteDto }>;
    invitePolicyholder: Thunk<ClaimModel, string>;
    close: Thunk<ClaimModel, string>;
    reopen: Thunk<ClaimModel, string>;
    archive: Thunk<ClaimModel, string>;
    getReportHtml: Thunk<ClaimModel, string>;
    getInvoiceHtml: Thunk<ClaimModel, string>;
    sendReminder: Thunk<ClaimModel, string>;
    getMetadata: Thunk<ClaimModel, string>;
    getExpenseRanges: Thunk<ClaimModel, string>;
}
