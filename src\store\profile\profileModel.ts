import { Actions, action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import AppModel from '../app/types/AppModel';
import { UserUpdatePhotoDto } from '../user/dto/UserDto';
import { mapUser } from '../user/mappers';
import { mapProfile } from './mappers';
import ProfileModel from './types/ProfileModel';

const profileModel: ProfileModel = {
    // state
    data: null,

    // actions
    load: action((state, profile) => {
        state.data = profile;
    }),

    unload: action((state) => {
        state.data = null;
    }),

    // thunks
    get: thunk(async (actions) => {
        const res = await api.get('/profile');
        actions.load(mapProfile(res.data.data));
    }),

    update: thunk(async (actions, dto, { getStoreActions }) => {
        const res = await api.post('/profile', dto);
        actions.load(mapProfile(res.data.data));
        (getStoreActions() as Actions<AppModel>).auth.loadAuthUser(mapUser(res.data.data));
    }),

    updatePhoto: thunk(async (actions, photoUrl) => {
        const dto: UserUpdatePhotoDto = {
            document_id: photoUrl,
        };

        const { data } = await api.post('/profile', dto);

        actions.load(mapProfile(data.data));

        return data.data;
    }),
};
export default profileModel;
