import { useStoreState } from '../../store';
import { ChatStatus } from '../../store/chat/types/Chat';
import Spinner from '../app/shared/Spinner';
import Input from './Input';
import MessageList from './MessageList';

const Chat: React.FC = () => {
    const { chatStatus, isInitialized } = useStoreState((state) => state.chat);

    return (
        <div className="chat">
            {chatStatus === ChatStatus.CONNECTED && <MessageList />}
            <Input disabled={chatStatus !== ChatStatus.CONNECTED} />
            {(!isInitialized || chatStatus !== ChatStatus.CONNECTED) && (
                <Spinner type="overlay" message={'Loading'} transparent />
            )}
        </div>
    );
};

export default Chat;
