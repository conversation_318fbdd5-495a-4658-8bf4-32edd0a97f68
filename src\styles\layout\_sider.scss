.ant-layout-sider {
    max-width: unset !important;
    width: auto !important;
    flex: unset !important;
}

.sider {
    background-color: #fff !important;
    border-right: 1px solid $color-gray-5;
    height: 100%;
    min-width: 200px;

    &-nav {
        width: 100%;

        &-menu {
            border: none;
            border-inline-end: none !important;
            font-weight: $font-weight-medium !important;

            .ant-menu-item {
                border-radius: 0;
                font-weight: $font-weight-bold;
                margin: 0 !important;
                margin-bottom: 12px;
                padding: 8px 16px !important;

                &.ant-menu-item-selected {
                    background-color: transparentize($color-secondary, 0.6);
                    color: $color-text;
                }

                span {
                    font-size: 16px;
                    line-height: 18px;
                }
            }
        }

        &-submenu {
            margin-bottom: 24px;
            &-title {
                color: $color-secondary-darker;
                line-height: 16px !important;
                font-size: 14px !important;
                font-weight: $font-weight-bold !important;
                margin-bottom: 16px;
            }
        }
    }

    .ant-layout-sider-children {
        height: 100%;
        overflow: auto;
        padding: $padding;
    }
}
