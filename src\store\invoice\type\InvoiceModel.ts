import { Action, Thunk } from 'easy-peasy';
import {
    InvoiceApproveDto,
    InvoiceCreateDto,
    InvoiceDto,
    InvoicePaginateDto,
    InvoiceRejectDto,
} from '../dto/InvoiceDto';
import { Resource, ResourcePagination } from '../../app/types';

export default interface InvoiceModel {
    // state
    list: Resource<any[]>;
    pagination: ResourcePagination | null;

    // actions
    loading: Action<InvoiceModel>;
    load: Action<InvoiceModel, InvoiceDto[]>;
    setPagination: Action<InvoiceModel, ResourcePagination>;
    unload: Action<InvoiceModel>;

    // thunks
    get: Thunk<InvoiceModel, InvoicePaginateDto>;
    getInvoice: Thunk<InvoiceModel, string>;
    getInvoicePreview: Thunk<InvoiceModel, InvoiceCreateDto>;
    getInvoiceRequest: Thunk<InvoiceModel, InvoiceCreateDto>;
    add: Thunk<InvoiceModel, InvoiceCreateDto>;
    delete: Thunk<InvoiceModel, string>;
    checkIfPdfReady: Thunk<InvoiceModel, string>;
    getPDF: Thunk<InvoiceModel, string>;
    getCSV: Thunk<InvoiceModel, string>;
    uploadedtoQbs: Thunk<InvoiceModel, string>;
    sendToAdjuster: Thunk<InvoiceModel, string>;
    approve: Thunk<InvoiceModel, { id: string; dto: InvoiceApproveDto }>;
    reject: Thunk<InvoiceModel, { id: string; dto: InvoiceRejectDto }>;
}
