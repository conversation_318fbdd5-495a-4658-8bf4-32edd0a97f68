.modal {
    background: rgba(0, 48, 73, 0.25);
    height: 100vh;
    opacity: 0;
    position: fixed;
    right: 0;
    top: 0;
    transition: all 0.3s ease;
    width: 100%;
    visibility: hidden;
    z-index: 11;

    &.show {
        opacity: 1;
        visibility: visible;

        .modal-content,
        .modal-close {
            transform: translate(0);
        }
    }

    &-close {
        cursor: pointer;
        position: absolute;
        top: 16px;
        right: 16px;
        transform: translateX(500px);
        transition: all 0.3s ease;
        z-index: 3;

        path {
            stroke: $color-gray-3;
        }
    }

    &-content {
        @include flex(flex, column, nowrap, flex-start, stretch);
        background-color: white;
        bottom: 0;
        height: 100%;
        max-width: 550px;
        position: absolute;
        right: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
        width: 100%;
        z-index: 2;

        @include breakpoint(xl) {
            max-width: 400px !important;
        }

        &-inner {
            flex-grow: 1;
            overflow-y: auto;
            padding: 40px;
            width: 100%;
        }
    }

    &-header {
        @include flex(flex, column, nowrap, flex-start, stretch);
        border-bottom: 1px solid $color-gray-5;
        margin-bottom: 24px;
        padding-bottom: 24px;

        &-title {
            font-size: 24px;
            font-weight: $font-weight-bold;
            line-height: 28px;
            margin-bottom: 8px;
        }

        &-subtitle {
            color: $color-gray-2;
            font-size: 14px;
            line-height: 18px;
            font-weight: $font-weight-light;
            font-family: $font-family-primary, sans-serif;
        }
    }

    &-overlay {
        bottom: 0;
        left: 0;
        right: 0;
        position: absolute;
        top: 0;
    }

    &-side {
        @include flex(flex, row, nowrap, center, center);
        background-color: $color-background;
        height: 100vh;
        position: absolute;
        top: 0;

        &.small {
            width: 520px;
            left: -520px;
        }

        &.large {
            width: 840px;
            left: -840px;

            @include breakpoint(xl) {
                width: calc(100vw - 400px);
                left: calc((100vw - 400px) * -1);
            }
        }

        &-loader {
            width: calc(100% + $modal-sidebar-width) !important;
            left: -$modal-sidebar-width !important;
        }
    }
}
