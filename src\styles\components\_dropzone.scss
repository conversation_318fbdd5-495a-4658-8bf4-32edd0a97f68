.dropzone {
    @include flex(flex, row, nowrap, center, center);
    border: 1px dashed $color-secondary;
    min-height: 225px;
    transition: all 0.3s ease;

    &.active {
        background-color: $color-secondary;
        color: white;
    }

    &-inner {
        @include flex(flex, column, nowrap, center, center);

        .btn-open {
            cursor: pointer;
            color: $color-secondary;
            font-weight: $font-weight-semi-bold;
            text-decoration: underline;
        }

        .icon-upload {
            color: $color-secondary;
        }

        .img-types {
            color: $color-gray-4;
            font-size: 12px;
            line-height: 16px;
        }

        .title {
            font-size: 15px;
            line-height: 20px;
        }
    }
}
