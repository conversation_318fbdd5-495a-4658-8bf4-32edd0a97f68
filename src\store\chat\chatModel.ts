import {
    ChannelMessagePersistenceType,
    ChannelMessageType,
    ChimeSDKMessagingClient,
    ListChannelMessagesCommand,
    SendChannelMessageCommand,
    SortOrder,
} from '@aws-sdk/client-chime-sdk-messaging';
import {
    ConsoleLogger,
    DefaultMessagingSession,
    LogLevel,
    MessagingSessionConfiguration,
} from 'amazon-chime-sdk-js';
import { Helpers, action, computed, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { transition } from '../app/types';
import AppModel from '../app/types/AppModel';
import { UserRole } from '../user/types/User';
import ChatConfigDto from './dto/ChatConfigDto';
import ChatCredentialsDto from './dto/ChatCredentialsDto';
import { mapChatConfig, mapCredentials, mapMessages, mapSentMessage } from './mappers';
import { ChatStatus, InitStatus } from './types/Chat';
import ChatModel from './types/ChatModel';
import { CREDENTIALS_REFRESH_BUFFER, groupMessages } from './utils';

const chatModel: ChatModel = {
    // state
    isInitialized: InitStatus.UNINITALIZED,
    messages: transition.reset([]),
    chatStatus: ChatStatus.STOPPED,
    nextMessagesToken: undefined,
    credentials: null,
    endpointUrl: null,
    chimeClient: null,
    messagingSession: null,
    channelArn: null,
    userArn: null,
    profilePhotoMap: {},
    unreadCount: 0,

    // computed
    groupedMessages: computed((state) => {
        return groupMessages(state.messages.value);
    }),

    hasMoreMessages: computed((state) => !!state.nextMessagesToken),

    hasUnread: computed((state) => !!state.unreadCount),

    //actions
    load: action((state, { userArn, credentials, channelArn, messagingSession }) => {
        state.userArn = userArn;
        state.credentials = credentials;
        state.channelArn = channelArn;
        state.messagingSession = messagingSession;
        state.isInitialized = InitStatus.INITIALIZED;
    }),

    unload: action((state) => {
        state.isInitialized = InitStatus.UNINITALIZED;
        state.credentials = null;
        state.userArn = null;
        state.channelArn = null;
        state.endpointUrl = null;
        state.chimeClient?.destroy();
        state.chimeClient = null;
        state.messagingSession?.stop();
        state.messagingSession = null;
        state.messages = transition.reset([]);
        state.nextMessagesToken = undefined;
        state.profilePhotoMap = {};
        state.unreadCount = 0;
        state.hasUnread = false;
        state.chatStatus = ChatStatus.STOPPED;
    }),

    setCredentials: action((state, credentials) => {
        state.credentials = credentials;
    }),

    setEndpointUrl: action((state, url) => {
        state.endpointUrl = url;
    }),

    setChatStatus: action((state, status) => {
        state.chatStatus = status;
    }),

    setChimeClient: action((state, client) => {
        state.chimeClient = client;
    }),

    setMessagingSession: action((state, session) => {
        state.messagingSession = session;
    }),

    setIsInitialized: action((state, initStatus) => {
        state.isInitialized = initStatus;
    }),

    setChannelArn: action((state, arn) => {
        state.channelArn = arn;
    }),

    setUserArn: action((state, arn) => {
        state.userArn = arn;
    }),

    loadingMessages: action((state) => {
        state.messages = transition.loading(state.messages.value);
    }),

    loadMessages: action((state, commandOutput) => {
        const { NextToken, ChannelMessages: newRawMessages } = commandOutput;

        if (!newRawMessages) throw new Error('Error getting messages');

        const newMessages = mapMessages(newRawMessages);

        const messages = [...newMessages, ...state.messages.value];

        state.messages = transition.loaded(messages);
        state.nextMessagesToken = NextToken;
    }),

    loadSentMessage: action((state, message) => {
        const messages = state.messages.value;
        state.messages = transition.loaded([...messages, mapSentMessage(message)]);
    }),

    setProfilePhoto: action((state, { userId, photoUrl }) => {
        state.profilePhotoMap = {
            ...state.profilePhotoMap,
            [userId]: photoUrl,
        };
    }),

    setUnreadCount: action((state, unreadCount) => {
        state.unreadCount = unreadCount;
    }),

    // thuks
    init: thunk(
        async (actions, { observer, claimId, claimChannelArn }, { getState, getStoreState }) => {
            const { chatStatus } = getState();

            if (chatStatus !== ChatStatus.STOPPED) return;

            try {
                actions.setIsInitialized(InitStatus.INITIALIZING);
                const { auth } = getStoreState() as AppModel;
                const { user } = auth;

                if (!user) throw new Error('User is undefined');

                const chatConfig: ChatConfigDto = await actions.getConfig(claimId);
                const credentials: ChatCredentialsDto = await actions.getCredentials();

                const chimeClient = new ChimeSDKMessagingClient({
                    region: chatConfig.region,
                    credentials: {
                        secretAccessKey: credentials.secretAccessKey,
                        sessionToken: credentials.sessionToken,
                        accessKeyId: credentials.accessKeyId,
                        expiration: new Date(credentials.expiration),
                    },
                });
                actions.setChimeClient(chimeClient);

                const messagingConfig = new MessagingSessionConfiguration(
                    (user.chatUserArn || chatConfig.chatUserArn) as string,
                    `session-${Math.floor(Math.random() * 100)}`,
                    chatConfig.endpointUrl,
                    chimeClient,
                );

                const logger = new ConsoleLogger('Chat Session', LogLevel.INFO);
                const messagingSession = new DefaultMessagingSession(messagingConfig, logger);
                messagingSession.addObserver(observer);

                actions.load({
                    channelArn: claimChannelArn || chatConfig.chatChannelArn,
                    userArn: user.chatUserArn || chatConfig.chatUserArn,
                    messagingSession,
                    credentials,
                });
            } catch (err: any) {
                console.error(err.message);
                actions.setIsInitialized(InitStatus.UNINITALIZED);
            }
        },
    ),

    start: thunk(async (actions, payload, { getState }) => {
        const { messagingSession, chatStatus } = getState();

        if (chatStatus !== ChatStatus.STOPPED) return;

        if (!messagingSession) throw new Error('Messaging session is null');

        await messagingSession.start();
    }),

    end: thunk((actions, payload, { getState }) => {
        const { messagingSession } = getState();
        if (!messagingSession) throw new Error('Messaging session is null');

        messagingSession.stop();
        actions.unload();
    }),

    getConfig: thunk(async (actions, claimId) => {
        const res = await api.get(`chat/${claimId}`);

        return mapChatConfig(res.data.data);
    }),

    getCredentials: thunk(async () => {
        const res = await api.get('chat');

        return mapCredentials(res.data.data);
    }),

    refreshCredentials: thunk(async (actions) => {
        const credentials = await actions.getCredentials();

        actions.setCredentials(credentials);
        return credentials;
    }),

    _sendMessage: thunk(async (actions, messageDto, { getState }) => {
        const { chimeClient, channelArn, userArn } = getState();
        if (!chimeClient || !channelArn || !userArn || !messageDto)
            throw new Error('Send message error');

        if (actions.needsCredentialRefresh()) {
            await actions.unload();
        }

        try {
            await chimeClient.send(
                new SendChannelMessageCommand({
                    ChannelArn: channelArn,
                    ChimeBearer: userArn,
                    Content: messageDto.text,
                    Type: ChannelMessageType.STANDARD,
                    Persistence: ChannelMessagePersistenceType.PERSISTENT,
                    Metadata: JSON.stringify(messageDto.meta),
                }),
            );
        } catch (err: any) {
            console.error(err);
        }
    }),

    _getMessages: thunk(async (actions, payload, { getState }) => {
        const { nextMessagesToken, chimeClient, channelArn, userArn } = getState();

        if (!chimeClient || !channelArn || !userArn) {
            return;
        }

        actions.loadingMessages();

        if (actions.needsCredentialRefresh()) {
            await actions.unload();
        }

        try {
            const commandOutput = await chimeClient.send(
                new ListChannelMessagesCommand({
                    ChannelArn: channelArn,
                    ChimeBearer: userArn,
                    MaxResults: 20,
                    NextToken: nextMessagesToken,
                    SortOrder: SortOrder.DESCENDING,
                }),
            );
            actions.loadMessages(commandOutput);
        } catch (err: any) {
            console.error(err);
        }
    }),

    getProfilePhoto: thunk(
        async (actions, sender, { getState }: Helpers<ChatModel, AppModel, any>) => {
            const photoUrl = getState().profilePhotoMap[sender.userId];

            if (photoUrl !== undefined) {
                return photoUrl;
            }

            const fetchedPhotoUrl = await actions._getProfilePhoto(sender);

            if (fetchedPhotoUrl) {
                actions.setProfilePhoto({ userId: sender.userId, photoUrl: fetchedPhotoUrl });
            }

            return fetchedPhotoUrl;
        },
    ),

    _getProfilePhoto: thunk(async (actions, sender) => {
        const rolePrefix = Object.values(UserRole).includes(sender.role)
            ? 'users'
            : 'policyholders';

        const res = await api.get(`/${rolePrefix}/${sender.userId}`);

        return res?.data?.data?.profile_photo;
    }),

    pingWritten: thunk(async (actions, claimId) => {
        return api.post(`/chat/${claimId}/written`);
    }),

    pingSeen: thunk(async (actions, claimId) => {
        return api.post(`/chat/${claimId}/seen`);
    }),

    checkHasUnread: thunk(async (actions, claimId) => {
        const { data } = await api.get(`/notifications/chat/${claimId}/unread-count`);
        actions.setUnreadCount(data.data);
    }),

    needsCredentialRefresh: thunk((_, __, { getState }) => {
        const state = getState();

        if (!state.credentials?.expiration) return false;

        const now = Date.now();

        return (
            now >= (Date.parse(state.credentials.expiration) - CREDENTIALS_REFRESH_BUFFER || now)
        );
    }),
};
export default chatModel;
