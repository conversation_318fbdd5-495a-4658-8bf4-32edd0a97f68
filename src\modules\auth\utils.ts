export function hasLowerCase(password: string): boolean {
    return /[a-z]/.test(password);
}
export function hasUpperCase(password: string): boolean {
    return /[A-Z]/.test(password);
}
export function hasNumber(password: string): boolean {
    return /[0-9]/.test(password);
}
export function hasSymbol(password: string): boolean {
    return /[^a-zA-Z0-9]/.test(password);
}
export function hasMinimumLength(password: string, minLength = 8): boolean {
    return password.length >= minLength;
}

export enum PasswordCondition {
    LOWER_CASE = 'has lowercase character',
    UPPER_CASE = 'has uppercase character',
    HAS_NUMBER = 'one number',
    HAS_SYMBOL = 'one symbol ( e.g. ! @ # $ % & )',
    MIN_LENGTH = '8 character minimum',
    PASSWORDS_MATCH = 'PASSWORDS_MATCH',
}

export function getPasswordConditionList(
    password: string,
    repeatPassword: string,
): {
    [condition: string]: boolean;
} {
    return {
        [PasswordCondition.LOWER_CASE]: hasLowerCase(password),
        [PasswordCondition.UPPER_CASE]: hasUpperCase(password),
        [PasswordCondition.HAS_NUMBER]: hasNumber(password),
        [PasswordCondition.HAS_SYMBOL]: hasSymbol(password),
        [PasswordCondition.MIN_LENGTH]: hasMinimumLength(password),
        [PasswordCondition.PASSWORDS_MATCH]: password === repeatPassword,
    };
}
