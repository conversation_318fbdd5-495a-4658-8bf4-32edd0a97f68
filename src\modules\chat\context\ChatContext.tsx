import { MessagingSessionObserver } from 'amazon-chime-sdk-js';
import throttle from 'just-throttle';
import {
    ReactNode,
    createContext,
    useCallback,
    useContext,
    useEffect,
    useMemo,
    useRef,
} from 'react';
import { useStoreActions, useStoreState } from '../../../store';
import { SendMessageDto } from '../../../store/chat/dto/MessageDto';
import { mapSentMessage } from '../../../store/chat/mappers';
import { ChatStatus, InitStatus } from '../../../store/chat/types/Chat';
import { CHAT_PING_INTERVAL, isChannelMessage, isUserMessage } from '../../../store/chat/utils';
import { ClaimDto } from '../../../store/claim/dto/ClaimDto';
import useUnreadChatListener from '../hooks/useUnreadChatListener';
import useCredentialRefreshListener from '../hooks/useCredentialRefreshListener';

export interface ChatContextState {
    messageListRef: React.RefObject<HTMLDivElement>;
    getMessages: () => Promise<void>;
    sendMessage: (message: SendMessageDto) => Promise<void>;
    claim: ClaimDto;
}

const ChatContext = createContext<ChatContextState | null>(null);

type AsyncFunctionOptionalWithParam<T> = (param?: T) => Promise<void>;

interface Props {
    children: ReactNode;
    claim: ClaimDto;
}

export const ChatContextProvider: React.FC<Props> = ({ children, claim }) => {
    const lastScrollHeight = useRef(0);
    const messageListRef = useRef<HTMLDivElement>(null);

    const { user: authUser } = useStoreState((state) => state.auth);

    const { messages, chatStatus, isInitialized } = useStoreState((state) => state.chat);
    const {
        init,
        start,
        end,
        setChatStatus,
        getConfig,
        loadSentMessage: addMessage,
        _getMessages,
        _sendMessage,
        unload,
        pingSeen,
        pingWritten,
    } = useStoreActions((actions) => actions.chat);
    const { getClaim } = useStoreActions((actions) => actions.claim);

    useUnreadChatListener(claim.id);
    useCredentialRefreshListener();

    const throttledSeen = useMemo(
        () =>
            throttle(
                () => {
                    return claim && pingSeen(claim.id);
                },
                CHAT_PING_INTERVAL,
                { leading: true },
            ),
        [claim, pingSeen],
    );

    const throttledWritten = useMemo(
        () => throttle(() => claim && pingWritten(claim.id), CHAT_PING_INTERVAL, { leading: true }),
        [claim, pingWritten],
    );

    async function executeAndUpdateScroll<T>(
        func: AsyncFunctionOptionalWithParam<T>,
        optionalParam?: T,
    ) {
        if (!messageListRef.current) {
            if (optionalParam !== undefined) {
                await func(optionalParam);
            } else {
                await func();
            }
            return;
        }

        const prevScrollHeight = messageListRef?.current.scrollHeight || 0;
        const prevScrollTop = messageListRef?.current.scrollTop || 0;

        if (optionalParam !== undefined) {
            await func(optionalParam);
        } else {
            await func();
        }

        const newScrollHeight = messageListRef?.current?.scrollHeight || 0;
        const newScrollTop = prevScrollTop + (newScrollHeight - prevScrollHeight);
        if (!messageListRef.current) {
            return;
        }
        messageListRef.current.scrollTop = newScrollTop;
        lastScrollHeight.current = newScrollHeight;
    }

    const getMessages = useCallback(async () => {
        if (chatStatus !== ChatStatus.CONNECTED) {
            return;
        }

        messageListRef.current && throttledSeen();
        await executeAndUpdateScroll(_getMessages);
    }, [_getMessages, chatStatus, throttledSeen]);

    const sendMessage = useCallback(
        async (message: SendMessageDto) => {
            throttledWritten();
            await executeAndUpdateScroll<SendMessageDto>(_sendMessage, message);
        },
        [_sendMessage, throttledWritten],
    );

    const onChatStatusChange = useCallback(
        (status: ChatStatus) => {
            if (status === ChatStatus.RECONNECTING) {
                getMessages();
            }

            setChatStatus(status);
        },
        [getMessages, setChatStatus],
    );

    const observer = useMemo(() => {
        return {
            messagingSessionDidStart: () => {
                try {
                    onChatStatusChange(ChatStatus.CONNECTED);
                } catch (err: any) {
                    console.error(err.message);
                    unload();
                }
            },
            messagingSessionDidStartConnecting: (reconnecting) => {
                try {
                    if (reconnecting) {
                        onChatStatusChange(ChatStatus.RECONNECTING);
                        return;
                    }

                    onChatStatusChange(ChatStatus.CONNECTING);
                } catch (err: any) {
                    console.error(err.message);
                    unload();
                }
            },
            messagingSessionDidStop: () => {
                try {
                    onChatStatusChange(ChatStatus.STOPPED);
                } catch (err: any) {
                    console.error(err.message);
                }
            },
            messagingSessionDidReceiveMessage: (message) => {
                try {
                    if (isChannelMessage(message, claim.chatChannelArn || '')) {
                        const parsedMessage = JSON.parse(message.payload);
                        messageListRef.current &&
                            !isUserMessage(mapSentMessage(parsedMessage), authUser) &&
                            throttledSeen();
                        lastScrollHeight.current = 0;
                        addMessage(parsedMessage);
                    }
                } catch (err: any) {
                    console.error(err.message);
                    unload();
                }
            },
        } as MessagingSessionObserver;
    }, [addMessage, authUser, claim.chatChannelArn, onChatStatusChange, throttledSeen, unload]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        unload();
        return () => {
            unload();
        };
    }, [claim.id, unload]);

    useEffect(() => {
        if (isInitialized !== InitStatus.UNINITALIZED || chatStatus !== ChatStatus.STOPPED) return;

        if (!claim.chatChannelArn) {
            getConfig(claim.id).then(() => {
                getClaim(claim.id);
            });
            return;
        }

        init({ observer, claimChannelArn: claim.chatChannelArn || '', claimId: claim.id });
    }, [
        init,
        observer,
        isInitialized,
        claim.chatChannelArn,
        claim.id,
        chatStatus,
        getClaim,
        getConfig,
        claim,
    ]);

    useEffect(() => {
        if (isInitialized !== InitStatus.INITIALIZED || chatStatus !== ChatStatus.STOPPED) return;

        start();
    }, [end, isInitialized, start, chatStatus]);

    useEffect(() => {
        if (chatStatus === ChatStatus.CONNECTED && isInitialized === InitStatus.INITIALIZED) {
            getMessages();
        }
    }, [chatStatus, getMessages, isInitialized]);

    useEffect(() => {
        if (!messageListRef.current) {
            return;
        }

        const newScrollHeight = messageListRef?.current.scrollHeight || 0;

        messageListRef.current.scrollTop += newScrollHeight - (lastScrollHeight?.current || 0);
        lastScrollHeight.current = newScrollHeight;
    }, [messages]);

    return (
        <ChatContext.Provider
            value={{
                messageListRef,
                getMessages,
                sendMessage,
                claim,
            }}
        >
            {children}
        </ChatContext.Provider>
    );
};

export const useChat = (): ChatContextState => {
    const context = useContext<ChatContextState | null>(ChatContext);

    if (!context) {
        throw new Error('useChat hook must be used within a ChatContext');
    }

    return context;
};
