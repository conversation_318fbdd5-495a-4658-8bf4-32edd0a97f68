import { plainToInstance } from 'class-transformer';
import { NotificationDto } from './dto/NotificationDto';
import { NotificationData, RawNotification, RawNotificationData } from './types/Notifications';

export const mapNotificationData = ({
    claim_id,
    report_id,
    policy_holder_id,
    ...rest
}: RawNotificationData): NotificationData => ({
    ...rest,
    claimId: claim_id,
    reportId: report_id,
    policyholderId: policy_holder_id,
});

export const mapNotification = ({
    created_at,
    read_at,
    data,
    ...rest
}: RawNotification): NotificationDto =>
    plainToInstance(NotificationDto, {
        ...rest,
        createdAt: new Date(created_at),
        readAt: read_at ? new Date(read_at) : undefined,
        data: mapNotificationData(data),
    });

export const mapNotifications = (raw: RawNotification[]): NotificationDto[] =>
    raw.map((raw) => mapNotification(raw));
