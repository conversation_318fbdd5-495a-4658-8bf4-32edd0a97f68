import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { ReactComponent as LogoImage } from '../../assets/images/logo.svg';
import withoutAuth from '../../hooks/withoutAuth';
import { useStoreActions } from '../../store';
import ResetPasswordDto from '../../store/auth/dto/ResetPasswordDto';
import AuthLayout from '../app/layouts/AuthLayout';
import Form from '../app/shared/form/Form';
import PasswordField from '../app/shared/form/PasswordField';
import PasswordConditionList from './PasswordContitionList';
import classNames from 'classnames';

const ResetPasswordPage: React.FC = () => {
    const { token } = useParams();

    const [hasMetPasswordConditions, setHasMetPasswordConditions] = useState(false);

    const { resetPassword } = useStoreActions((actions) => actions.auth);

    const canSubmit = useMemo(() => hasMetPasswordConditions, [hasMetPasswordConditions]);

    const methods = useForm<ResetPasswordDto>({
        resolver: classValidatorResolver(ResetPasswordDto),
        defaultValues: {
            password: '',
            password_confirmation: '',
            token,
        },
    });

    const navigate = useNavigate();

    const onSubmit = async (fields) => {
        try {
            if (!token) throw new Error('No token');
            await resetPassword(fields);
            navigate('/auth/login');
        } catch (err: any) {
            console.error(err.message);
            methods.setError('password', { message: err.response.data.message });
        }
    };

    const password = methods.watch('password');
    const password_confirmation = methods.watch('password_confirmation');

    return (
        <AuthLayout>
            <Form className="auth-form" methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <LogoImage className="auth-form-logo"></LogoImage>
                <div>
                    <div className="heading h4 margin-bottom-32">Enter a new password</div>
                    <PasswordField
                        name="password"
                        label="Password"
                        placeholder="Enter your password"
                    />
                    <PasswordField
                        name="password_confirmation"
                        label="Repeat Password"
                        placeholder="Confirm your password"
                    />
                    <PasswordConditionList
                        password={password}
                        repeatPassword={password_confirmation}
                        onConditionsChange={setHasMetPasswordConditions}
                    />
                </div>
                <Button
                    htmlType="submit"
                    type="primary"
                    className={classNames(' btn btn-primary', { disabled: !canSubmit })}
                    size="large"
                    block
                    loading={methods.formState.isSubmitting}
                    disabled={!canSubmit}
                >
                    Continue
                </Button>
            </Form>
        </AuthLayout>
    );
};

export default withoutAuth(ResetPasswordPage);
