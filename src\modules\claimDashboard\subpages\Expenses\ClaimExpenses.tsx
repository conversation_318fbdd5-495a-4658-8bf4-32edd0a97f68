import { Button } from 'antd';
import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate, useParams } from 'react-router-dom';
import { ReactComponent as PlusIcon } from '../../../../assets/icons/add-sharp.svg';
import { canAccess } from '../../../../common/canAccess';
import { isOpen } from '../../../../common/claim';
import { useStoreActions, useStoreState } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ExpenseFilterParam, ExpenseOrderBy } from '../../../../store/expense/types/Expense';
import { UserRole } from '../../../../store/user/types/User';
import { PaginationDirection } from '../../../../utils/paginate';
import FlashMessages from '../../../app/FlashMessages';
import Dialog from '../../../app/shared/Dialog';
import Pagination from '../../../app/shared/Pagination';
import ChangeExpenseStatusModal from './ChangeExpenseStatusModal';
import DetailsModal from './DetailsModal';
import EditModal from './EditModal';
import { ExpensesList } from './ExpensesList';
import SearchAndFilter from './SearchAndFilter';
import Stats from './Stats';
import StatusFilter from './StatusFilters';
import AddModal from './add/AddModal';
import useExpenseActions from './useExpenseActions';
import { ExpenseStatusFilter, ExpenseStatusFilterType, makeExpenseFilterParam } from './utils';
import { ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';
import { ReactComponent as ExpenseIcon } from '../../../../assets/icons/receipt-outline.svg';

interface Props {
    claim: ClaimDto;
}

const ClaimExpenses: React.FC<Props> = ({ claim }) => {
    const params = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const status = searchParams.get('status');
    const isNewExpense = location.pathname.includes('/new');

    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [expenseStatusFilter, setExpenseStatusFilter] = useState<ExpenseStatusFilterType>(
        status || ExpenseStatusFilter.ALL,
    );
    const [filterParams, setFilterParams] = useState<ExpenseFilterParam[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const [editFieldData, setEditFieldsData] = useState({ date: null });
    const [showAddModal, setShowAddModal] = useState(isNewExpense);
    const [listCurrentExpenseIndex, setListCurrentExpenseIndex] = useState<number>();
    const [isExpenseInTheCurrentList, setIsExpenseInTheCurrentList] = useState<boolean>(false);
    const [navigationType, setNavigationType] = useState<string | undefined>('next' || 'prev');
    const [sortDirection, setSortDirection] = useState(PaginationDirection.DESC);
    const { list, pagination } = useStoreState((state) => state.expense);
    const { get, archive } = useStoreActions((actions) => actions.expense);

    const handleGetExpenses = () => {
        const params = [...filterParams];
        let orderBy = ExpenseOrderBy.DATE;
        let limit: number | undefined = undefined;
        let page = currentPage;
        params.push(
            makeExpenseFilterParam(
                'archived_at',
                expenseStatusFilter === ExpenseStatusFilter.ARCHIVED ? 'is not null' : 'is null',
                '',
            ),
        );

        if (
            expenseStatusFilter !== ExpenseStatusFilter.ALL &&
            expenseStatusFilter !== ExpenseStatusFilter.ARCHIVED &&
            expenseStatusFilter !== ExpenseStatusFilter.RECENT
        ) {
            params.push(makeExpenseFilterParam('status', '=', expenseStatusFilter));
        }

        if (expenseStatusFilter === ExpenseStatusFilter.RECENT) {
            params.push(makeExpenseFilterParam('last_activity_at', 'is not null', ''));
            orderBy = ExpenseOrderBy.LAST_ACTIVITY_AT;
            limit = 10;
            page = 1;
        }

        get({
            claimId: claim.id,
            dto: {
                page: page,
                search_term: searchTerm,
                order_by: orderBy,
                direction: sortDirection,
                limit,
                params,
            },
        }).then((r) => {
            const selectedExpenseUpdatedInList: ExpenseDto = r.find(
                (e) => e.id === selectedExpense?.id,
            );
            !!selectedExpenseUpdatedInList && setSelectedExpense(selectedExpenseUpdatedInList);

            if (navigationType === 'next') {
                setSelectedExpense(r[0]);
                setNavigationType(undefined);
            }
            if (navigationType === 'prev') {
                setSelectedExpense(r[9]);
                setNavigationType(undefined);
            }
        });
    };

    const {
        setSelectedExpense,
        selectedExpense,
        showDetailsModal,
        setShowDetailsModal,
        showEditModal,
        handleExpenseStatusChange,
        selectedStatus,
        setShowEditModal,
        onActionClick,
        isStatusLoading,
        showDeclineDialog,
        onDeclineDialogCancel,
        onDeclineDialogOk,
        showChangeExpenseStatusModal,
        onChangeExpenseStatusClose,
        onChangeExpenseStatusSuccess,
        showArchiveDialog,
        onArchiveDialogOk,
        onArchiveDialogCancel,
        receiptDialogLoading,
        showReceiptDialog,
        setShowReceiptDialog,
        handleRequestReceipt,
    } = useExpenseActions(handleGetExpenses);

    useEffect(() => {
        handleGetExpenses();
    }, [filterParams, expenseStatusFilter, get, claim.id, currentPage, searchTerm, sortDirection]); // eslint-disable-line react-hooks/exhaustive-deps

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);
        navigate(`/claim/${claim.id}/expenses/page/${page}?status=${expenseStatusFilter}`);
    };

    const resetFilterState = (status?: ExpenseStatusFilterType, shouldNavigate?: boolean) => {
        setSearchTerm('');
        setSortDirection(PaginationDirection.DESC);
        setExpenseStatusFilter(status || expenseStatusFilter);
        setFilterParams([]);
        setSelectedIds([]);
        setCurrentPage(1);

        shouldNavigate &&
            navigate(`/claim/${claim.id}/expenses?status=${status || expenseStatusFilter}`);
    };

    const handleStatusFilterChange = (status: ExpenseStatusFilterType) => {
        resetFilterState(status);
        navigate(`/claim/${claim.id}/expenses?status=${status}`);
    };

    const handleFilterChange = (filterParams: ExpenseFilterParam[]) => {
        resetFilterState(undefined, true);
        setFilterParams(filterParams);
    };

    const handleArchive = async () => {
        try {
            await archive(selectedIds);
            FlashMessages.success('Expenses Archived');
            setSelectedIds([]);
            await handleGetExpenses();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Archive Expenses');
        }
    };

    const handleAddSuccess = async () => {
        resetFilterState(ExpenseStatusFilter.ALL, true);
        setShowAddModal(false);
    };

    const handleSearch = (searchTerm: string) => {
        setCurrentPage(1);
        setSearchTerm(searchTerm);
        navigate(`/claim/${claim.id}/expenses?status=${status}`);
    };

    const setData = (data) => {
        setEditFieldsData(data);
    };

    const handlePrevExpense = () => {
        if (listCurrentExpenseIndex === undefined || list.loading) {
            return;
        }
        if (!list.value.length && !!pagination?.total) {
            handlePaginationChange(currentPage - 1);
            setNavigationType('prev');
            return;
        }
        if (!isExpenseInTheCurrentList) {
            setSelectedExpense(list.value[listCurrentExpenseIndex]);
            return;
        }
        if (listCurrentExpenseIndex === 0) {
            handlePaginationChange(currentPage - 1);
            setNavigationType('prev');
            return;
        }
        setSelectedExpense(list.value[listCurrentExpenseIndex - 1]);
    };

    const handleNextExpense = () => {
        if (listCurrentExpenseIndex === undefined || list.loading) {
            return;
        }
        if (!isExpenseInTheCurrentList) {
            setSelectedExpense(list.value[listCurrentExpenseIndex]);
            return;
        }
        if (listCurrentExpenseIndex === 9) {
            handlePaginationChange(currentPage + 1);
            setNavigationType('next');
            return;
        }
        setSelectedExpense(list.value[listCurrentExpenseIndex + 1]);
    };

    useEffect(() => {
        if (!selectedExpense) {
            return;
        }
        const expenseListIndex = list.value.findIndex((i) => i.id === selectedExpense?.id);

        if (expenseListIndex !== -1) {
            setListCurrentExpenseIndex(expenseListIndex);
            setIsExpenseInTheCurrentList(true);
            return;
        }
        if (expenseListIndex === -1) {
            return setIsExpenseInTheCurrentList(false);
        }
    }, [selectedExpense?.id, list.value]); // eslint-disable-line react-hooks/exhaustive-deps

    if (!canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])) {
        return <Navigate to="/" />;
    }

    return (
        <div className="box claim-expenses">
            <div className="claim-expenses-heading ">
                <div>
                    <div className="heading medium margin-bottom-8">Expenses</div>
                    <div>
                        Track expenses in the policyholder&apos;s claim. Make updates or get details
                        on any expense.
                    </div>
                </div>
                <div style={{ display: 'flex' }}>
                    {claim.archivedAt ||
                    (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN])) ? null : (
                        <Button
                            className="btn-secondary"
                            size="large"
                            style={{ backgroundColor: '#D6DCDB' }}
                            onClick={() =>
                                navigate(`/claim/${claim.id}/reports/request`, {
                                    state: {
                                        quickRequestReprot: true,
                                    },
                                })
                            }
                        >
                            <ExpenseIcon />
                            <span>Report Request</span>
                        </Button>
                    )}
                    {claim.archivedAt ||
                    (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN])) ? null : (
                        <Button
                            className="btn-primary"
                            size="large"
                            onClick={() => setShowAddModal(true)}
                        >
                            <PlusIcon />
                            <span>Add Expenses</span>
                        </Button>
                    )}
                </div>
            </div>
            <Stats claimId={claim.id} />
            <StatusFilter status={expenseStatusFilter} onFilterChange={handleStatusFilterChange} />
            <SearchAndFilter
                statusFilter={expenseStatusFilter}
                claim={claim}
                searchTerm={searchTerm}
                onSearch={handleSearch}
                filterParams={filterParams}
                onFilterChange={handleFilterChange}
                selectedIds={selectedIds}
                onClearSelected={() => setSelectedIds([])}
                onArchive={handleArchive}
            />
            <ExpensesList
                list={list}
                claim={claim}
                onActionClick={onActionClick}
                selectedIds={
                    isOpen(claim) &&
                    !claim.archivedAt &&
                    expenseStatusFilter !== ExpenseStatusFilter.ARCHIVED
                        ? selectedIds
                        : undefined
                }
                setSelectedIds={
                    isOpen(claim) &&
                    !claim.archivedAt &&
                    expenseStatusFilter !== ExpenseStatusFilter.ARCHIVED
                        ? setSelectedIds
                        : undefined
                }
                onStatusChange={handleExpenseStatusChange}
                readonly={!!claim.archivedAt || !isOpen(claim)}
                isStatusLoading={isStatusLoading}
                sortDirection={sortDirection}
                onToggleSort={() =>
                    setSortDirection((dir) =>
                        dir === PaginationDirection.ASC
                            ? PaginationDirection.DESC
                            : PaginationDirection.ASC,
                    )
                }
                enableDateSorting={expenseStatusFilter !== ExpenseStatusFilter.RECENT}
            />
            {pagination && expenseStatusFilter !== ExpenseStatusFilter.RECENT && (
                <Pagination {...pagination} onChange={handlePaginationChange} />
            )}
            {showDetailsModal && selectedExpense && (
                <DetailsModal
                    claim={claim}
                    expense={selectedExpense}
                    show={showDetailsModal}
                    onClose={() => {
                        setShowDetailsModal(false);
                        if (!list.value.length && !!pagination?.total) {
                            handlePaginationChange(currentPage - 1);
                        }
                    }}
                    onStatusChangeSuccess={() => handleGetExpenses()}
                    onEditSuccess={() => {
                        handleGetExpenses();
                    }}
                    onExpenseUpdate={(expense) => {
                        setSelectedExpense(expense);
                        handleGetExpenses();
                    }}
                    readonly={!!claim.archivedAt || !isOpen(claim)}
                    onPrevExpense={handlePrevExpense}
                    onNextExpense={handleNextExpense}
                />
            )}
            {showAddModal && (
                <AddModal
                    claim={claim}
                    show={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    onSuccess={() => handleAddSuccess()}
                />
            )}
            {(() => {
                console.log('<<<<<<<<expense.imagePath>>>>>>>', editFieldData);
                return null; // Ensure nothing is rendered to the DOM
            })()}
            {showEditModal && selectedExpense && (
                <EditModal
                    claim={claim}
                    expense={selectedExpense}
                    setFieldsData={(data) => setData(data)}
                    show={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        if (!list.value.length && !!pagination?.total) {
                            handlePaginationChange(currentPage - 1);
                        }
                    }}
                    onSuccess={() => {
                        handleGetExpenses();
                    }}
                    onPrevExpense={handlePrevExpense}
                    onNextExpense={handleNextExpense}
                />
            )}
            {showChangeExpenseStatusModal && selectedExpense && selectedStatus && (
                <ChangeExpenseStatusModal
                    show={showChangeExpenseStatusModal}
                    claim={claim}
                    onClose={() => {
                        onChangeExpenseStatusClose();
                        if (!list.value.length && !!pagination?.total) {
                            handlePaginationChange(currentPage - 1);
                        }
                    }}
                    onSuccess={() => onChangeExpenseStatusSuccess()}
                    expense={selectedExpense}
                    status={selectedStatus}
                    onPrevExpense={handlePrevExpense}
                    onNextExpense={handleNextExpense}
                />
            )}
            <Dialog
                show={showDeclineDialog}
                onCancel={onDeclineDialogCancel}
                onOk={onDeclineDialogOk}
                disabled={!!isStatusLoading}
            />
            <Dialog
                show={showArchiveDialog}
                onCancel={onArchiveDialogCancel}
                onOk={onArchiveDialogOk}
                disabled={!!isStatusLoading}
            />
            <Dialog
                onCancel={() => setShowReceiptDialog(false)}
                onOk={() => handleRequestReceipt()}
                show={showReceiptDialog}
                disabled={receiptDialogLoading}
            />
        </div>
    );
};
export default ClaimExpenses;
