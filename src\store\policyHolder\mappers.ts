import { plainToInstance } from 'class-transformer';
import PolicyHolderDto from './dto/PolicyHolderDto';
import { RawPolicyHolder } from './types/PolicyHolder';

export const mapPolicyHolder = ({
    full_name,
    salesforce_id,
    insurance_company,
    preferred_contact_method,
    profile_photo,
    job_title,
    created_at,
    deactivated_at,
    terms_accepted_at,
    activation_sent_at,
    insured_claim_number,
    ...rest
}: RawPolicyHolder): PolicyHolderDto =>
    plainToInstance(PolicyHolderDto, {
        ...rest,
        name: full_name,
        salesforceId: salesforce_id,
        insuranceCompany: insurance_company,
        contactMethod: preferred_contact_method,
        profilePhoto: profile_photo,
        jobTitle: job_title,
        createdAt: created_at,
        deactivatedAt: deactivated_at || undefined,
        emailVerifiedAt: terms_accepted_at || undefined,
        activationSentAt: activation_sent_at || undefined,
        claimNumber: insured_claim_number,
    });

export const mapPolicyHolders = (raw: RawPolicyHolder[]): PolicyHolderDto[] =>
    raw.map((p) => mapPolicyHolder(p));
