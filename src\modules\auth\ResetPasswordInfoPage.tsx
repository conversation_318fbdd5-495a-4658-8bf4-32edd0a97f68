import { Space } from 'antd';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStoreActions } from '../../store';
import AuthLayout from '../app/layouts/AuthLayout';

const ResetPasswordInfoPage: React.FC = () => {
    const navigate = useNavigate();

    const { logout } = useStoreActions((actions) => actions.auth);

    useEffect(() => {
        logout();
    }, [logout]);

    return (
        <AuthLayout>
            <div className="auth-form">
                <Space direction="vertical" size="large" align="center">
                    <div>Check your email.</div>
                    <div>
                        An email with instructions to reset your password has been sent to your
                        email address.
                    </div>
                    <div>
                        If you do not wish to reset your password, you can ignore the email and{' '}
                        <span className="btn-text lowercase " onClick={() => navigate('/')}>
                            <b> login.</b>
                        </span>
                    </div>
                </Space>
            </div>
        </AuthLayout>
    );
};

export default ResetPasswordInfoPage;
