import { Button, Space } from 'antd';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { canAccess } from '../../../../../common/canAccess';
import { ClaimDto } from '../../../../../store/claim/dto/ClaimDto';
import { UserRole } from '../../../../../store/user/types/User';
import { ReactComponent as IconBack } from '../../../../../assets/icons/arrow-forward-sharp.svg';
import ManagePeopleModal from '../../../../claimDashboard/subpages/Dashboard/ManagePeopleModal';
import Avatar from '../../../shared/Avatar';

interface Props {
    claim?: ClaimDto;
    handleGetClaim: VoidFunction;
}

const ClaimDashboardSubHeader: React.FC<Props> = ({ claim, handleGetClaim }) => {
    const [showPeopleModal, setShowPeopleModal] = useState(false);

    const navigate = useNavigate();

    return (
        <div className="header-sub claim-dashboard-subheader">
            <div className="claim-dashboard-subheader-info">
                <Avatar
                    photoUrl={claim?.policyHolder.profilePhoto}
                    size="medium"
                    name={claim?.policyHolder.name}
                />
                <div className="summary">
                    <div className="summary-name">{claim?.policyHolder.name}</div>
                    <Space className="summary-list" wrap size="small">
                        <div className="summary-list-item">
                            Insurance Company: {claim?.insuranceCompany}
                        </div>

                        <div className="summary-list-item">Claim Number: {claim?.claimNumber}</div>

                        <div className="summary-list-item">
                            <span>Adjuster: {claim?.adjuster && claim.adjuster.name}</span>
                        </div>

                        <div className="summary-list-item">
                            <span>OOPE Rep: {claim?.admin && claim.admin.name}</span>
                        </div>

                        <div className="summary-list-item-last">
                            Reports: every {claim?.reportDays} day
                            {claim && claim.reportDays > 1 ? 's' : ''}
                        </div>
                    </Space>
                </div>
            </div>

            <div className="claim-dashboard-subheader-actions">
                {canAccess([UserRole.ADJUSTER]) && (
                    <Button
                        icon={<IconBack className="icon-back" />}
                        size="large"
                        className="btn btn-secondary outline margin-left-32"
                        onClick={() => navigate(-1)}
                    >
                        Back
                    </Button>
                )}
                {claim && (
                    <Button
                        className="btn-secondary outline"
                        size="large"
                        onClick={() => navigate(`/claim/${claim.id}/information`)}
                    >
                        Claim Information
                    </Button>
                )}
                {canAccess([UserRole.SUPER_ADMIN]) && !claim?.archivedAt && (
                    <Button
                        className="btn-secondary outline"
                        size="large"
                        onClick={() => setShowPeopleModal(true)}
                    >
                        Manage People
                    </Button>
                )}
            </div>
            {showPeopleModal && claim && (
                <ManagePeopleModal
                    claim={claim}
                    show={showPeopleModal}
                    onClose={() => setShowPeopleModal(false)}
                    onSuccess={() => {
                        handleGetClaim();
                        setShowPeopleModal(false);
                    }}
                />
            )}
        </div>
    );
};

export default ClaimDashboardSubHeader;
