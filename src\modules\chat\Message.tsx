import {
    ChatMessageGroupData,
    ChatMessage as ChatMessageInterface,
    ChatMessageType,
} from '../../store/chat/types/Chat';
import MessageGroup from './MessageGroup';
import Timestamp from './Timestamp';

interface Props {
    message: ChatMessageInterface;
}

const Message: React.FC<Props> = ({ message }) => {
    if (message.type === ChatMessageType.GROUP) {
        return <MessageGroup group={message.data as ChatMessageGroupData} />;
    }

    if (message.type === ChatMessageType.TIMESTAMP) {
        return <Timestamp timestamp={message.createdAt} />;
    }

    return null;
};
export default Message;
