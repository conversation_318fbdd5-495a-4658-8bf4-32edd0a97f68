import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate } from 'react-router-dom';
import { ReactComponent as LogoImage } from '../../assets/images/logo.svg';
import { useStoreActions } from '../../store';
import ForgotPasswordDto from '../../store/auth/dto/ForgotPasswordDto';
import Form from '../app/shared/form/Form';
import TextField from '../app/shared/form/TextField';

interface Props {
    onSuccess: VoidFunction;
    email?: string;
}

const ForgotPasswordForm: React.FC<Props> = ({ email, onSuccess }) => {
    const { forgotPassword } = useStoreActions((actions) => actions.auth);
    const [formDisabled, setFormDisabled] = useState(false);

    const location = useLocation();
    const navigate = useNavigate();

    const methods = useForm<ForgotPasswordDto>({
        resolver: classValidatorResolver(ForgotPasswordDto),
        defaultValues: { email },
    });

    const onSubmit = async (fields) => {
        setFormDisabled(true);
        try {
            await forgotPassword(fields.email);
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
        } finally {
            setFormDisabled(false);
        }
    };

    useEffect(() => {
        methods.reset();
    }, [location, methods]);

    return (
        <Form className="auth-form" methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
            <LogoImage className="auth-form-logo" />
            <div className="heading h4">Forgotten Password?</div>
            <Space direction="vertical" className="block">
                <TextField
                    name="email"
                    label="Email"
                    placeholder="Email"
                    type="email"
                    disabled={formDisabled}
                />
                <div className="block">
                    <Button
                        className="btn-primary margin-bottom-8"
                        htmlType="submit"
                        type="primary"
                        size="large"
                        loading={formDisabled || methods.formState.isSubmitting}
                        block
                    >
                        Submit
                    </Button>
                    <Button
                        type="link"
                        className="btn-text"
                        onClick={() => navigate('/')}
                        disabled={methods.formState.isSubmitting}
                        size="large"
                        block
                    >
                        Back
                    </Button>
                </div>
            </Space>
        </Form>
    );
};

export default ForgotPasswordForm;
