import classNames from 'classnames';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import Spinner from './Spinner';
import ReactDOM from 'react-dom';
import { ReactComponent as IconClose } from '../../../assets/icons/close.svg';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    children: ReactNode;
    title?: ReactNode;
    sideContent?: ReactNode;
    sideContentProps?: ModalSideProps;
    subtitle?: string;
    className?: string;
    isLoading?: boolean;
    loadingMessage?: string;
    noClose?: boolean;
    escapeClose?: boolean;
}

const Modal: React.FC<Props> = ({
    show,
    onClose,
    children,
    title,
    subtitle,
    className,
    isLoading,
    loadingMessage = 'Loading',
    sideContent,
    sideContentProps,
    noClose,
    escapeClose = true,
}) => {
    const [showModal, setShowModal] = useState(false);

    useEffect(() => {
        setTimeout(() => {
            setShowModal(show);
        }, 1);
    }, [show]);

    const handleClose = useCallback(() => {
        setShowModal(false);
        setTimeout(() => {
            onClose();
        }, 300);
    }, [onClose]);

    useEffect(() => {
        if (!escapeClose) {
            return;
        }

        const handleEscape = (event) => {
            if (event.key === 'Escape') {
                handleClose();
            }
        };

        if (show) {
            window.addEventListener('keydown', handleEscape);
        }

        return () => {
            window.removeEventListener('keydown', handleEscape);
        };
    }, [escapeClose, handleClose, show]);

    return ReactDOM.createPortal(
        <div className={classNames('modal', className, { show: showModal })}>
            {!noClose && <IconClose className="modal-close" onClick={() => handleClose()} />}
            <div onClick={handleClose} className="modal-overlay"></div>
            <div className="modal-content">
                <div className="modal-content-inner">
                    <div className="modal-header">
                        {title && <div className="modal-header-title">{title}</div>}
                        {subtitle && <div className="modal-header-subtitle">{subtitle}</div>}
                    </div>
                    <div>
                        {children}
                        {isLoading && (
                            <Spinner
                                className={classNames({ 'modal-side-loader': !!sideContent })}
                                type="overlay"
                                message={loadingMessage}
                            />
                        )}
                    </div>
                </div>
                {sideContent && <ModalSide {...sideContentProps}>{sideContent}</ModalSide>}
            </div>
        </div>,
        document.body,
    );
};
export default Modal;

export type ModalSideSize = 'small' | 'large';

export interface ModalSideProps {
    children?: ReactNode;
    className?: string;
    size?: ModalSideSize;
}

const ModalSide: React.FC<ModalSideProps> = ({ children, className, size = 'small' }) => {
    return <div className={classNames('modal-side', size, className)}>{children}</div>;
};
