import { FilterParamsOperator } from '../../../types';
import { RawPolicyHolder } from '../../policyHolder/types/PolicyHolder';
import { RawUserData } from '../../user/types/User';

export enum ClaimsOrderBy {
    ID = 'uuid',
    STATUS = 'status',
    ARCHIVED_AT = 'archived_at',
    CLOSED_AT = 'closed_at',
    SALESFORCE_ID = 'salesforce_id',
    USER_ID = 'user_id',
    ADMIN_ID = 'admin_id',
    POLICY_HOLDER_ID = 'policy_holder_id',
    UPDATED_AT = 'updated_at',
    LOSS_DATE = 'date_of_loss',
}

export enum ClaimStatus {
    ACTIVE = 'open',
    CLOSED = 'closed',
    ON_HOLD = 'onhold',
    INVOICED = 'invoiced',
    PAYMENT_PENDING = 'payment Pending',
    CLOSED_WON = 'closed Won',
    CLOSED_LOST = 'closed Lost',
    SIU = 'SIU',
}

export interface RawClaim {
    id: string;
    status: string;
    archived_at: string | null;
    started_at: string | null;
    report_days: number;
    policy_holder: RawPolicyHolder;
    adjuster: RawUserData | null;
    admin: RawUserData | null;
    chat_channel_arn: string | null;
    loss_address: RawClaimLossAddress;
    loss_reason: string | null;
    type_of_policy: string | null;
    temporary_address: string | null;
    number_of_adults_in_household: number | null;
    number_of_children_in_household: number | null;
    number_of_pets: number | null;
    type_of_pets: string | null;
    date_of_loss: string | null;
    ale_limits: string | null;
    normal_daily_food_expenditure: string | null;
    length_of_relocation: string | null;
    special_needs: string | null;
    insurance_company: string | null;
    insured_claim_number: string | null;
    created_at: string;
    updated_at: string;
    approved_receipt_categories: string[] | null;
    approved_receipt_start_date: string | null;
    approved_receipt_end_date: string | null;
    alcohol_approved: string | null;
    alcohol_limited_to: string | null;
    excessive_spending: string | null;
    groceries_approved: string | null;
    unitemized_receipts_approved: string | null;
    bank_statements_approved: string | null;
    approved_receipts_mile_radius: number | null;
    description: string | null;
}

export interface RawClaimLossAddress {
    street_address?: string;
    city?: string;
    state?: string;
    zip_code?: string;
}

export interface ClaimLossAddress {
    streetAddress?: string;
    city?: string;
    state?: string;
    zipCode?: string;
}

export type ClaimFilterableField =
    | 'uuid'
    | 'status'
    | 'archived_at'
    | 'closed_at'
    | 'salesforce_id'
    | 'user_id'
    | 'admin_id'
    | 'policy_holder_id';

export interface ClaimFilterParam {
    field: ClaimFilterableField;
    operator: FilterParamsOperator;
    value?: number | string | string[] | number[];
}

export interface RawMetadata {
    count_total: number;
    grand_total: number;
    approved_audited_amount: number;
    declined_amount: number;
    counts: RawMetadataGroup;
    percentages: RawMetadataGroup;
    totals: RawMetadataGroup;
}

export interface RawMetadataGroup {
    submitted?: number;
    in_review?: number;
    audited?: number;
}

export interface MetadataGroup {
    submitted?: number;
    inReview?: number;
    audited?: number;
}

export interface RawExpenseRanges {
    approved: { min: string; max: string };
    submitted: { min: string; max: string };
}

export const RawClaimStatusMap = {
    opened: ClaimStatus.ACTIVE,
    closed: ClaimStatus.CLOSED,
    onhold: ClaimStatus.ON_HOLD,
    invoiced: ClaimStatus.INVOICED,
    paymentpending: ClaimStatus.PAYMENT_PENDING,
    siu: ClaimStatus.SIU,
    closedwon: ClaimStatus.CLOSED_WON,
    closedlost: ClaimStatus.CLOSED_LOST,
};
