.ant-table {
    border-radius: 0 !important;

    .ant-table-column-sort {
        background: unset !important;
    }

    .select-box {
        min-width: 40px;
    }

    .sort-icon {
        &.asc {
            transform: rotate(-90deg);
        }

        &.desc {
            transform: rotate(90deg);
        }
    }

    td {
        color: $color-text !important;
        font-size: 14px;
        font-weight: 400 !important;
        line-height: 18px;
        padding: 16px 12px !important;

        &:first-child {
            padding-left: 16px !important;
        }

        &:last-child {
            padding-right: 16px !important;
        }

        &.activity,
        &.daterange {
            color: $color-secondary-darker !important;
        }
    }

    &-thead {
        background-color: #fff !important;

        th {
            background-color: #fff !important;
            color: $color-secondary-darker !important;
            padding: 24px 12px !important;

            &:first-child {
                padding-left: 16px !important;
            }

            &:last-child {
                padding-right: 16px !important;
            }
        }
    }

    tr {
        &.ant-table-row-selected {
            td {
                background-color: unset !important;
            }
        }
    }
}
