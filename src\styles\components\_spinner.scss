.spinner {
    z-index: 4;

    &.transparent {
        background: none !important;
    }

    svg {
        path {
            fill: $color-secondary;
            stroke: $color-secondary;
        }
    }

    &.overlay {
        @include flex(flex, column, nowrap, center, center);
        background-color: rgba(255, 255, 255, 0.7);
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 999 !important;
    }

    &.skeleton {
        @include flex(flex, column, nowrap, center, center);
        background-color: rgba(255, 255, 255, 0.7);
        height: 100%;
        left: 0;
        position: relative;
        top: 0;
        width: 100%;
        z-index: 999 !important;
    }
}
