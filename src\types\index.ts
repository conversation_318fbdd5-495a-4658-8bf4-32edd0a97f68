type Transition<T> = {
    loading: boolean;
    loaded: boolean;
    value: T;
    pageCount?: number;
};

export interface Resource<T> {
    value: T;
    loading: boolean;
    loaded: boolean;
}

export const transition = {
    reset: function <T>(value: T): Resource<T> {
        return {
            loading: false,
            loaded: false,
            value,
        };
    },
    loading: function <T>(value: T): Resource<T> {
        return {
            loading: true,
            loaded: false,
            value,
        };
    },
    loaded: function <T>(value: T): Resource<T> {
        return {
            loading: false,
            loaded: true,
            value,
        };
    },
    clone: function <T, TResult>(transition: Transition<T>, value: TResult): Transition<TResult> {
        return {
            ...transition,
            value,
        };
    },
};

export enum ContactMethod {
    PHONE = 'phone',
    EMAIL = 'email',
    TEXT = 'text',
}

export interface FilterParam<T = string> {
    field: T;
    operator: FilterParamsOperator;
    value: any;
}

export type FilterParamsOperator =
    | '='
    | '!='
    | '<>'
    | '>'
    | '<'
    | '>='
    | '<='
    | 'like'
    | 'not like'
    | 'between'
    | 'not between'
    | 'in'
    | 'not in'
    | 'is null'
    | 'is not null';

export enum ExpenseCategory {
    FOOD_AND_MEALS = 'food_and_meals',
    HOUSING_AND_HOTELS = 'housing_and_hotels',
    MISCELLANEOUS = 'miscellaneous',
    TRANSPORTATION = 'transportation',
    UTILITIES = 'utilities',
}

export const ACTION_MENU_DIVIDER = 'ACTION_MENU_DIVIDER';
