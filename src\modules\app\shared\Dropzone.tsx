import { useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { ReactComponent as IconUpload } from '../../../assets/icons/icon-upload-outline.svg';
import classNames from 'classnames';
import FlashMessages from '../FlashMessages';
import { MAX_IMAGE_SIZE } from '../../../store/chat/utils';

interface Props {
    onChange: (files: File[]) => void;
    disabled?: boolean;
    className?: string;
    maxFileNo?: number;
}

const Dropzone: React.FC<Props> = ({ onChange, disabled = false, className, maxFileNo }) => {
    const onDrop = useCallback(
        (acceptedFiles) => {
            if (maxFileNo && acceptedFiles.length > maxFileNo) {
                FlashMessages.warn(`Please Select up to ${maxFileNo} Files`);
                return;
            }

            acceptedFiles = acceptedFiles.filter((file) => {
                if (file.size > MAX_IMAGE_SIZE) {
                    FlashMessages.warn(
                        `Image "${file.name.substr(0, 10)} ${
                            file.name.length > 10 ? '...' : ''
                        }" is too large`,
                    );
                    return false;
                }

                return true;
            });

            onChange(acceptedFiles);
        },
        [maxFileNo, onChange],
    );
    const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
        useFsAccessApi: !disabled,
        noClick: true,
        onDrop,
        accept: {
            'image/jpeg': [],
            'image/png': [],
        },
    });

    return (
        <div
            className={classNames(
                'dropzone',
                { cursor: !disabled },
                { active: isDragActive },
                className,
            )}
            {...getRootProps()}
            onClick={() => !disabled && open()}
            style={{
                cursor: disabled ? 'no-drop' : 'pointer',
            }}
        >
            <input {...getInputProps()} disabled={disabled} />
            <div className="dropzone-inner">
                {!isDragActive ? (
                    <>
                        <IconUpload className="icon-upload margin-bottom-16" />
                        <div className="title margin-bottom-8">
                            Drag & Drop or <span className="btn-open">choose files</span> to upload
                        </div>
                        <div className="img-types">JPG or PNG</div>
                    </>
                ) : (
                    <p>Drag and drop some files here, or click to select files</p>
                )}
            </div>
        </div>
    );
};
export default Dropzone;
