import { action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { DEFAULT_PER_PAGE, mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapPolicyHolder, mapPolicyHolders } from './mappers';
import PolicyHolderModel from './types/PolicyHolderModel';

const policyHolderModel: PolicyHolderModel = {
    // state
    list: transition.reset([]),
    pagination: null,

    // actions
    load: action((state, payload) => {
        state.list = transition.loaded(payload);
    }),

    setPagination: action((state, payload) => {
        state.pagination = payload;
    }),

    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    // thunks
    get: thunk(async (actions, dto) => {
        dto.page = dto.page || 1;
        dto.limit = dto.limit || DEFAULT_PER_PAGE;

        actions.loading();
        const {
            data: { data, meta },
        } = await api.get('/policyholders', { params: dto });

        actions.load(mapPolicyHolders(data));
        actions.setPagination(mapPagination(meta));
    }),

    getPolicyHolder: thunk(async (actions, id) => {
        const { data } = await api.get(`/policyholders/${id}`);

        return mapPolicyHolder(data.data);
    }),

    deactivate: thunk(async (actions, id) => {
        await api.post(`/policyholders/${id}/deactivate`);
    }),

    activate: thunk(async (actions, id) => {
        await api.post(`/policyholders/${id}/activate`);
    }),
};
export default policyHolderModel;
