import { MenuProps } from 'antd';
import { canAccess } from '../../common/canAccess';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { ClaimStatus } from '../../store/claim/types/Claim';
import { UserRole } from '../../store/user/types/User';

export enum ClaimActions {
    VIEW_CLAIM = 'VIEW_CLAIM',
    ADD_EXPENSE = 'ADD_EXPENSE',
    SEND_MESSAGE = 'SEND_MESSAGE',
    EXPENSE_REMINDER = 'EXPENSE_REMINDER',
    CLOSE_CLAIM = 'CLOSE_CLAIM',
    REOPEN_CLAIM = 'REOPEN_CLAIM',
    ARCHIVE_CLAIM = 'ARCHIVE_CLAIM',
}

export enum PathnamePrefixes {
    MY_CLAIMS = 'my-claims',
    ALL_CLAIMS = 'all-claims',
    RECENT = 'recent',
}

export const addMoreClaimActions = (actionItems: MenuProps['items'], claim: ClaimDto) => {
    if (!actionItems) {
        return undefined;
    }

    if (claim.status === ClaimStatus.ACTIVE && canAccess([UserRole.SUPER_ADMIN])) {
        return actionItems.concat(
            {
                type: 'divider',
            },
            {
                label: 'Close Claim',
                key: ClaimActions.CLOSE_CLAIM,
            },
        );
    }

    if (claim.status === ClaimStatus.CLOSED && canAccess([UserRole.SUPER_ADMIN])) {
        return actionItems.concat(
            {
                type: 'divider',
            },
            {
                label: 'Reopen Claim',
                key: ClaimActions.REOPEN_CLAIM,
            },
            {
                label: 'Archive Claim',
                key: ClaimActions.ARCHIVE_CLAIM,
            },
        );
    }

    return actionItems;
};
