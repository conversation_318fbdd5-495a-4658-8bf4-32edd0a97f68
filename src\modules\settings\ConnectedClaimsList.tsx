import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { Resource } from '../../store/app/types';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { ClaimStatus } from '../../store/claim/types/Claim';
import DataGrid from '../app/shared/DataGrid';
import { capitalizeString } from '../../utils/strings';

interface Props {
    list: Resource<ClaimDto[]>;
}

const ConnectedClaimsList: React.FC<Props> = ({ list }) => {
    const navigate = useNavigate();

    const columns: ColumnsType<ClaimDto> = [
        {
            key: 'claimNumber',
            title: 'Claim Number',
            render: ({ claimNumber, id }: ClaimDto) => (
                <span className="cursor" onClick={() => navigate(`/claim/${id}/information`)}>
                    {claimNumber}
                </span>
            ),
        },

        {
            key: 'status',
            title: 'Status',
            className: 'flex-center',
            render: (claim: ClaimDto) => (
                <div
                    className={classNames(
                        {
                            'pill primary': [
                                ClaimStatus.ACTIVE,
                                ClaimStatus.ON_HOLD,
                                ClaimStatus.INVOICED,
                                ClaimStatus.PAYMENT_PENDING,
                            ].includes(claim.status),
                        },
                        {
                            'pill inactive': [
                                ClaimStatus.SIU,
                                ClaimStatus.CLOSED,
                                ClaimStatus.CLOSED_WON,
                                ClaimStatus.CLOSED_LOST,
                            ].includes(claim.status),
                        },
                    )}
                >
                    {capitalizeString(claim.status)}
                </div>
            ),
        },
    ];

    return <DataGrid<ClaimDto> columns={columns} list={list} />;
};
export default ConnectedClaimsList;
