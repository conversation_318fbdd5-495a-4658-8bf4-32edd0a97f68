import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Row, Space } from 'antd';
import classNames from 'classnames';
import React, { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ReactComponent as IconEdit } from '../../../../assets/icons/pencil-sharp.svg';
import { canAccess } from '../../../../common/canAccess';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ChangeExpenseStatusDto, ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';
import { toRawStatusMap } from '../../../../store/expense/mappers';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import { UserRole } from '../../../../store/user/types/User';
import { formatCurrency } from '../../../../utils/currencyFormat';
import {
    convertUtcToSpecifiedTimeZone,
    DATE_ONLY,
    DATE_TIME,
    formatDateString,
} from '../../../../utils/dateFormat';
import FlashMessages from '../../../app/FlashMessages';
import { ReactComponent as MoneyIcon } from '../../../../assets/icons/money icon.svg';
import { ReactComponent as TimePeriodIcon } from '../../../../assets/icons/time period_icon.svg';
import Avatar from '../../../app/shared/Avatar';
import Dialog from '../../../app/shared/Dialog';
import Modal from '../../../app/shared/Modal';
import Form from '../../../app/shared/form/Form';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';
import SelectBox from '../../../app/shared/form/SelectBox';
import TextField from '../../../app/shared/form/TextField';
import EditModal from './EditModal';
import ReceiptPreview from './ReceiptPreview';
import {
    AuditedType,
    ExpenseStatus,
    getAuditedTypeByReviewsCommentType,
    getReadableExpenseCategory,
    getReviewerCommentByStatus,
    getReviewerCommentsByStatusToSelectBox,
    getReviewerCommentValueByStatus,
    isOtherField,
} from './utils';
import { format } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { ReviewersCommentsType } from '../../../../common/ReviewersCommentsType';
import ModalNavigationWrapper from './ModalNavigationWrapper';

interface Props {
    claim: ClaimDto;
    disableNav?: boolean;
    expense: ExpenseDto;
    onClose: VoidFunction;
    onEditSuccess: VoidFunction;
    onNextExpense?: VoidFunction;
    onPrevExpense?: VoidFunction;
    onExpenseUpdate?: (expense: ExpenseDto) => void;
    onStatusChangeSuccess: VoidFunction;
    readonly: boolean;
    show: boolean;
}

const StatusOptions: SelectBoxOption[] = Object.keys(ExpenseStatus).map((key) => ({
    label: ExpenseStatus[key],
    value: RawExpenseStatus[key],
}));

const DetailsModal: React.FC<Props> = ({
    disableNav,
    claim,
    expense,
    onClose,
    onEditSuccess,
    onExpenseUpdate,
    onNextExpense,
    onPrevExpense,
    onStatusChangeSuccess,
    readonly,
    show,
}) => {
    const [showEditModal, setShowEditModal] = useState(false);
    const [editFieldData, setEditFieldsData] = useState({ date: '' });
    const [showApproveFields, setShowApproveFields] = useState(false);
    const [firstChange, setFirstChange] = useState(false);
    const [showRequestReceiptDialog, setShowRequestReceiptDialog] = useState(false);
    const [requestReceiptDialogDisabled, setRequestReceiptDialogDisabled] = useState(false);
    const [canChange, setCanChange] = useState(false);
    const [isToastVisible, setIsToastVisible] = useState(false);
    const [auditedType, setAuditedType] = useState<AuditedType | undefined>(
        getAuditedTypeByReviewsCommentType(expense.reviewer_comment_type),
    );
    const [reviewersComments, setReviewersComments] = useState<SelectBoxOption[]>([]);

    const { update, requestReceipt, view } = useStoreActions((actions) => actions.expense);

    const methods = useForm<ChangeExpenseStatusDto>({
        resolver: classValidatorResolver(ChangeExpenseStatusDto),
        defaultValues: {
            status: toRawStatusMap[expense.status],
            reviewer_comment: expense.reviewerComment,
            reviewer_comment_type: getReviewerCommentByStatus(
                toRawStatusMap[expense.status],
                expense,
                expense.reviewer_comment_type,
            ),
            approved_amount: expense.approvedAmount
                ? expense.approvedAmount.toFixed(2)
                : ((!expense.declinedAmount && expense.submittedAmount) || 0).toFixed(2),
            declined_amount: expense.declinedAmount ? expense.declinedAmount?.toFixed(2) : '0.00',
        },
    });

    const currentStatus = methods.watch('status');
    const currentReviewerCommentType = methods.watch('reviewer_comment_type');
    const watchedApprovedAmount = methods.watch('approved_amount');
    const watchedDeclinedAmount = methods.watch('declined_amount');

    // Check if the current status is the same as the original expense status
    const isSameStatus = currentStatus === toRawStatusMap[expense.status];

    const onSubmit = useCallback(
        async (fields) => {
            try {
                if (
                    fields.status === RawExpenseStatus.AUDITED &&
                    +(+fields.approved_amount + +fields.declined_amount).toFixed(2) !==
                        expense?.submittedAmount
                ) {
                    methods.setError('approved_amount', {
                        message: 'Ensure that both fields are filled out',
                    });
                    methods.setError('declined_amount', {
                        message: 'Ensure that both fields are filled out',
                    });
                    return;
                }
                if (fields.status !== RawExpenseStatus.AUDITED) {
                    delete fields.approved_amount;
                }
                if (fields.status === RawExpenseStatus.SUBMITTED) {
                    delete fields.reviewer_comment;
                    delete fields.reviewer_comment_type;
                } else {
                    // Only keep free-text comment for Other type; do not auto-fill with type label
                    if (!isOtherField(fields.reviewer_comment_type)) {
                        delete fields.reviewer_comment;
                    }
                }
                delete fields.declined_amount;
                const updatedExpense = await update({
                    id: expense.id,
                    dto: fields,
                });
                methods.reset({
                    status: toRawStatusMap[updatedExpense.status],
                    reviewer_comment_type: getReviewerCommentByStatus(
                        toRawStatusMap[updatedExpense.status],
                        updatedExpense,
                        updatedExpense.reviewer_comment_type,
                        getAuditedTypeByReviewsCommentType(updatedExpense.reviewer_comment_type),
                    ),
                    reviewer_comment:
                        updatedExpense.reviewerComment ??
                        methods.getValues('reviewer_comment') ??
                        '',
                    approved_amount: updatedExpense.approved_amount,
                    declined_amount: updatedExpense.declined_amount,
                });
                const updatedAuditedType = getAuditedTypeByReviewsCommentType(
                    updatedExpense.reviewer_comment_type,
                );
                setAuditedType(updatedAuditedType);
                setReviewersComments(
                    getReviewerCommentsByStatusToSelectBox(
                        toRawStatusMap[updatedExpense.status],
                        updatedAuditedType,
                    ),
                );
                !!onExpenseUpdate && onExpenseUpdate(updatedExpense);
                setFirstChange(false);
                setIsToastVisible(true);
                FlashMessages.success('Expense Status Changed');
                // Reset toast visibility after toast duration (default is 5000ms)
                setTimeout(() => {
                    setIsToastVisible(false);
                }, 5000);
                onStatusChangeSuccess();
                // Keep approve fields visible so the selected Partial reason remains shown
                setShowApproveFields(true);
            } catch (err: any) {
                console.error(err.message);
                FlashMessages.error('Failed to Update Expense Status');
            }
        },
        [expense.id, methods, onStatusChangeSuccess, update], // eslint-disable-line react-hooks/exhaustive-deps
    );

    useEffect(() => {
        if (methods.formState.isSubmitting) {
            return;
        }
        // Original behavior: toggle when status moves into one of these states
        if (
            [
                RawExpenseStatus.SUBMITTED,
                RawExpenseStatus.IN_REVIEW,
                RawExpenseStatus.AUDITED,
            ].includes(currentStatus)
        ) {
            setShowApproveFields(true);
        }
    }, [currentStatus, setShowApproveFields, methods]); // eslint-disable-line react-hooks/exhaustive-deps

    // Minimal fix: when opening in In Review and no type selected, set a default type and options
    useEffect(() => {
        if (currentStatus !== RawExpenseStatus.IN_REVIEW) {
            return;
        }
        const typeVal = methods.getValues('reviewer_comment_type');
        if (!typeVal) {
            const opts = getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.IN_REVIEW);
            setReviewersComments(opts);
            if (opts.length > 0) {
                methods.setValue('reviewer_comment_type', opts[0].value as any);
            }
        }
    }, [currentStatus]); // eslint-disable-line react-hooks/exhaustive-deps

    // Derive auditedType from amounts when status is AUDITED to mirror row behavior
    useEffect(() => {
        if (currentStatus !== RawExpenseStatus.AUDITED) {
            return;
        }
        const approved = parseFloat(watchedApprovedAmount ?? '0');
        const declined = parseFloat(watchedDeclinedAmount ?? '0');
        if (!isFinite(approved) || !isFinite(declined)) {
            return;
        }
        if (declined <= 0) {
            setAuditedType(undefined);
            return;
        }
        if (declined >= (expense.submittedAmount ?? 0)) {
            setAuditedType(AuditedType.DECLINED);
            return;
        }
        setAuditedType(AuditedType.PARTIAL);
    }, [currentStatus, watchedApprovedAmount, watchedDeclinedAmount, expense.submittedAmount]);

    const canEdit =
        !readonly &&
        !(expense.status === ExpenseStatus.AUDITED && !canAccess([UserRole.SUPER_ADMIN]));

    const ModalHeader = () => (
        <div className="claim-expenses-details-header">
            <div className="title">
                <Row align="middle" justify="start" gutter={[8, 0]} className="block">
                    <Col className="vendor" flex="0 2 auto">
                        {expense.vendor}{' '}
                        {/* {new Date(claim.approvedReceiptEndDate ?? '').toLocaleDateString() >
                            new Date(expense.date ?? '').toLocaleDateString() &&
                            claim.approvedReceiptEndDate && (
                                <span className="claim-expenses-error claim-expenses-center">
                                    •
                                </span>
                            )} */}
                    </Col>
                    <Col flex="6 0 auto">
                        <div
                            className={classNames({
                                status: true,
                                'font-small': true,
                                pill: true,
                                info: expense.status === ExpenseStatus.SUBMITTED,
                                warning: expense.status === ExpenseStatus.IN_REVIEW,
                                success: expense.status === ExpenseStatus.AUDITED,
                            })}
                        >
                            {expense.status}
                        </div>
                    </Col>
                    {canEdit && (
                        <Col flex="0 0 100px" className="flex-end">
                            <Button
                                className="btn-secondary outline"
                                size="large"
                                onClick={() => setShowEditModal(true)}
                            >
                                <Space>
                                    Edit
                                    <IconEdit />
                                </Space>
                            </Button>
                        </Col>
                    )}
                </Row>
            </div>
        </div>
    );

    const handleRequestReceipt = async () => {
        setRequestReceiptDialogDisabled(true);
        try {
            await requestReceipt(expense.id);
            FlashMessages.success('Receipt Requested');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Request Receipt');
        } finally {
            setRequestReceiptDialogDisabled(false);
            setShowRequestReceiptDialog(false);
        }
    };

    const setData = (data) => {
        setEditFieldsData(data);
    };

    const handleApproveAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        setFirstChange(true);
        setCanChange(false);
        methods.clearErrors();
        const amountToNumber = parseFloat(value);

        if (amountToNumber <= 0) {
            methods.setValue('approved_amount', '0.00');
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }
        if (amountToNumber >= expense.submittedAmount) {
            methods.setValue('approved_amount', expense.submittedAmount.toFixed(2));
            methods.setValue('declined_amount', '0.00');
            setReviewersComments(getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.AUDITED));
            handleReviewersCommentsChange();
            setCanChange(false);
            return;
        }
        methods.setValue('approved_amount', value);
        methods.setValue('declined_amount', (expense.submittedAmount - amountToNumber).toFixed(2));
        if (!canChange) {
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
        }
        setCanChange(true);
    };

    const handleDeclinedAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        setFirstChange(true);
        setCanChange(false);
        methods.clearErrors();
        const amountToNumber = parseFloat(value);
        if (amountToNumber >= expense.submittedAmount) {
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            methods.setValue('approved_amount', '0.00');
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }

        if (amountToNumber <= 0) {
            methods.setValue('declined_amount', '0.00');
            methods.setValue('approved_amount', expense.submittedAmount.toFixed(2));
            setReviewersComments(getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.AUDITED));
            handleReviewersCommentsChange();
            setCanChange(false);
            return;
        }
        methods.setValue('declined_amount', value);
        methods.setValue('approved_amount', (expense.submittedAmount - amountToNumber).toFixed(2));

        if (!canChange) {
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setCanChange(true);
        }
    };

    const handleReviewersCommentsChange = (
        auditedType?: AuditedType,
        commentType?: ReviewersCommentsType,
    ) => {
        setAuditedType(auditedType);
        // Preserve explicit "Other" selection for In Review; don't replace with default type
        if (currentStatus === RawExpenseStatus.IN_REVIEW && isOtherField(commentType)) {
            methods.setValue('reviewer_comment_type', commentType as ReviewersCommentsType);
            // For In Review - Other, always clear the comment
            methods.setValue('reviewer_comment', expense.reviewerComment ?? '');
            return;
        } else {
            methods.setValue(
                'reviewer_comment_type',
                getReviewerCommentByStatus(currentStatus, expense, commentType, auditedType),
            );
        }
        // Only auto-fill comment for non-Other types; for Other, keep empty or user-entered
        if (!isOtherField(commentType as ReviewersCommentsType)) {
            methods.setValue(
                'reviewer_comment',
                getReviewerCommentValueByStatus(currentStatus, commentType, auditedType),
            );
        } else if (expense.reviewer_comment_type !== commentType) {
            methods.setValue('reviewer_comment', '');
        }
    };

    useEffect(() => {
        if (methods.formState.isSubmitting) {
            return;
        }
        handleReviewersCommentsChange(auditedType, currentReviewerCommentType);
        // Do not override for Other or Audited; only auto-fill for non-Other on non-Audited
        if (
            !isOtherField(currentReviewerCommentType) &&
            currentReviewerCommentType &&
            currentStatus !== RawExpenseStatus.AUDITED
        ) {
            methods.setValue(
                'reviewer_comment',
                getReviewerCommentValueByStatus(
                    currentStatus,
                    currentReviewerCommentType,
                    auditedType,
                ),
            );
        }
        if (
            currentStatus !== toRawStatusMap[expense.status] &&
            !isOtherField(currentReviewerCommentType) &&
            !firstChange
        ) {
            methods.setValue(
                'reviewer_comment_type',
                getReviewerCommentByStatus(currentStatus, expense, currentReviewerCommentType),
            );
            if (currentStatus !== RawExpenseStatus.AUDITED) {
                methods.setValue(
                    'reviewer_comment',
                    getReviewerCommentValueByStatus(
                        currentStatus,
                        currentReviewerCommentType,
                        auditedType,
                    ),
                );
            }
        }

        if (
            isOtherField(currentReviewerCommentType) &&
            expense.reviewer_comment_type !== currentReviewerCommentType
        ) {
            methods.setValue('reviewer_comment', '');
        }

        if (
            !isOtherField(currentReviewerCommentType) &&
            currentReviewerCommentType &&
            currentStatus !== RawExpenseStatus.AUDITED
        ) {
            methods.setValue(
                'reviewer_comment',
                getReviewerCommentValueByStatus(currentStatus, currentReviewerCommentType),
            );
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        expense.reviewerComment,
        expense.reviewer_comment_type,
        methods,
        expense,
        firstChange,
        currentStatus,
    ]);

    useEffect(() => {
        if (methods.formState.isSubmitting) {
            return;
        }
        // Do not override user input once they started editing amounts
        if (firstChange) {
            return;
        }
        setReviewersComments(getReviewerCommentsByStatusToSelectBox(currentStatus, auditedType));
        if (currentStatus !== RawExpenseStatus.AUDITED) {
            methods.setValue('approved_amount', '0.00');
            methods.setValue('declined_amount', '0.00');
        } else {
            methods.setValue(
                'approved_amount',
                expense.approvedAmount
                    ? expense.approvedAmount.toFixed(2)
                    : ((!expense.declinedAmount && expense.submittedAmount) || 0).toFixed(2),
            );
            methods.setValue(
                'declined_amount',
                expense.declinedAmount ? expense.declinedAmount?.toFixed(2) : '0.00',
            );
        }
        methods.clearErrors('approved_amount');
        methods.clearErrors('declined_amount');
    }, [currentStatus, auditedType, firstChange]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        methods.setValue('status', toRawStatusMap[expense.status]);
    }, [expense]); // eslint-disable-line react-hooks/exhaustive-deps

    // change reviewer comment after reviewer comment type is changed or auditedType
    useEffect(() => {
        if (methods.formState.isSubmitting) {
            return;
        }
        // Special-case: In Review - Other should always be empty
        if (
            currentStatus === RawExpenseStatus.IN_REVIEW &&
            isOtherField(currentReviewerCommentType)
        ) {
            methods.setValue('reviewer_comment', '');
            return;
        }
        // For any other "Other" type, do not overwrite user-entered text; only clear if empty
        if (isOtherField(currentReviewerCommentType)) {
            const existingComment = (methods.getValues('reviewer_comment') ?? '').trim();
            if (existingComment === '') {
                methods.setValue('reviewer_comment', '');
            }
            return;
        }
        // Do not auto-fill with type label. Only auto-fill for non-Audited statuses if needed.
        if (currentStatus === RawExpenseStatus.AUDITED) {
            methods.setValue('reviewer_comment', '');
        } else {
            methods.setValue(
                'reviewer_comment',
                getReviewerCommentValueByStatus(
                    currentStatus,
                    currentReviewerCommentType,
                    auditedType,
                ) ?? '',
            );
        }
    }, [currentReviewerCommentType, auditedType, currentStatus, methods]);

    const handleSave = () => {
        if (!methods.formState.isDirty) {
            const values = methods.getValues(); // Get current values
            onSubmit(values); // Trigger manual submit with unchanged values
        } else {
            methods.handleSubmit(onSubmit)();
        }
    };

    useEffect(() => {
        view(expense.id);
    }, [expense.id, view]);

    // Original behavior on expense change: only update auditedType and reviewer_comment content
    useEffect(() => {
        setAuditedType(getAuditedTypeByReviewsCommentType(expense.reviewer_comment_type));
        if (isOtherField(expense.reviewer_comment_type)) {
            methods.setValue('reviewer_comment', expense.reviewerComment ?? '');
        } else {
            methods.setValue('reviewer_comment', '');
        }
    }, [expense.id, expense.reviewer_comment_type]); // eslint-disable-line react-hooks/exhaustive-deps

    // Original behavior on status change: no forced reset other than amounts (handled elsewhere)

    // Ensure a default reviewer_comment_type is selected so dropdown renders for In Review/Audited
    useEffect(() => {
        if (!showApproveFields) {
            return;
        }
        if (![RawExpenseStatus.AUDITED, RawExpenseStatus.IN_REVIEW].includes(currentStatus)) {
            return;
        }
        const typeVal = methods.getValues('reviewer_comment_type');
        if ((!typeVal || typeVal === undefined) && reviewersComments.length > 0) {
            methods.setValue('reviewer_comment_type', reviewersComments[0].value as any);
        }
    }, [reviewersComments, showApproveFields, currentStatus]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
        <>
            <Modal
                className="claim-expenses-details"
                show={show}
                onClose={onClose}
                title={<ModalHeader />}
                sideContent={
                    <ModalNavigationWrapper
                        onPrev={() => !!onPrevExpense && onPrevExpense()}
                        onNext={() => !!onNextExpense && onNextExpense()}
                        expense={expense}
                        disableNav={disableNav}
                    >
                        <ReceiptPreview
                            photoUrl={expense.imagePath}
                            canRequestReceipt={!readonly}
                            onRequestReceipt={() => setShowRequestReceiptDialog(true)}
                        />
                    </ModalNavigationWrapper>
                }
            >
                <>
                    <Row gutter={[16, 24]}>
                        <Col span={12} className="info-group">
                            <div className="title">Expense Date:</div>
                            <div className="value">
                                {format(
                                    convertUtcToSpecifiedTimeZone(
                                        editFieldData && editFieldData.date
                                            ? editFieldData.date
                                            : expense.date,
                                        claim.timezone,
                                    ),
                                    DATE_TIME,
                                    { locale: enUS },
                                )}
                            </div>
                        </Col>
                        <Col span={12} className="info-group">
                            <div className="title">Category:</div>
                            <div className="value">
                                {getReadableExpenseCategory(expense.category)}
                            </div>
                        </Col>
                        <Col span={12} className="info-group">
                            <div className="title">Location:</div>
                            <div className="value">{expense.location}</div>
                        </Col>
                        <Col span={12} className="info-group">
                            <div className="title">Attention:</div>
                            <div className="value">
                                {(claim.excessiveSpending === '$100>' ||
                                    claim.excessiveSpending?.toLowerCase() === 'yes') &&
                                    (expense?.submittedAmount ?? 0) >= 100 && (
                                        <>
                                            <MoneyIcon
                                                className="claim-expenses-image"
                                                width={24}
                                                height={24}
                                                viewBox="0 0 2084 2084"
                                                style={{ display: 'block' }}
                                            />{' '}
                                            Excessive Spending
                                            <br />
                                        </>
                                    )}
                                {claim.approvedReceiptEndDate &&
                                    (() => {
                                        // Convert expense date to local time zone and get YYYY-MM-DD
                                        const expenseDateStr = new Date(
                                            format(
                                                convertUtcToSpecifiedTimeZone(
                                                    expense.date,
                                                    claim.timezone,
                                                ),
                                                DATE_ONLY,
                                                { locale: enUS },
                                            ),
                                        )
                                            .toISOString()
                                            .split('T')[0];

                                        const approvedStartDateStr = claim.approvedReceiptStartDate
                                            ? new Date(claim.approvedReceiptStartDate)
                                                  .toISOString()
                                                  .split('T')[0]
                                            : null;

                                        const approvedEndDateStr = claim.approvedReceiptEndDate
                                            ? new Date(claim.approvedReceiptEndDate)
                                                  .toISOString()
                                                  .split('T')[0]
                                            : null;

                                        // Compare purely by date strings
                                        if (
                                            (approvedStartDateStr &&
                                                expenseDateStr < approvedStartDateStr) ||
                                            (approvedEndDateStr &&
                                                expenseDateStr > approvedEndDateStr)
                                        ) {
                                            return (
                                                <>
                                                    <TimePeriodIcon
                                                        className="claim-expenses-image"
                                                        width={24}
                                                        height={24}
                                                        viewBox="0 0 2084 2084"
                                                        style={{ display: 'block' }}
                                                    />
                                                </>
                                            );
                                        }

                                        return null;
                                    })()}
                            </div>
                        </Col>
                        {expense.notes && (
                            <Col span={24} className="info-group">
                                <div className="title">Note:</div>
                                <div className="value">{expense.notes}</div>
                            </Col>
                        )}
                    </Row>
                    <hr />
                    <Row gutter={[16, 24]}>
                        <Col span={8} className="info-group">
                            <div className="color-grey-3 font-regular margin-bottom-8">
                                Submitted:
                            </div>
                            <div className="font-medium">
                                <b>{formatCurrency(expense.submittedAmount || 0)}</b>
                            </div>
                        </Col>
                        <Col span={8} className="info-group">
                            <div className="color-grey-3 font-regular margin-bottom-8">
                                Approved:
                            </div>
                            <div className="font-medium">
                                <b>{formatCurrency(expense.approvedAmount || 0)}</b>
                            </div>
                        </Col>
                        <Col span={8} className="info-group">
                            <div className="color-grey-3 font-regular margin-bottom-8">
                                Declined:
                            </div>
                            <div className="font-medium">
                                <b>{formatCurrency(expense.declinedAmount || 0)}</b>
                            </div>
                        </Col>
                    </Row>
                    <hr />
                    <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                        <div
                            className={classNames({
                                'selectbox-disabled-cursor':
                                    isToastVisible || methods.formState.isSubmitting,
                            })}
                        >
                            <SelectBox
                                disabled={
                                    readonly ||
                                    methods.formState.isSubmitting ||
                                    isToastVisible ||
                                    (expense.status === ExpenseStatus.AUDITED &&
                                        !canAccess([UserRole.SUPER_ADMIN]))
                                }
                                label="Status"
                                options={StatusOptions}
                                name="status"
                            />
                        </div>
                        {!!expense.reviewerComment &&
                            !showApproveFields &&
                            currentStatus !== RawExpenseStatus.SUBMITTED &&
                            currentStatus !== RawExpenseStatus.AUDITED &&
                            currentStatus !== RawExpenseStatus.IN_REVIEW && (
                                <Row>
                                    <Col span={24} className="info-group">
                                        <div className="title">Reviewer Comment:</div>
                                        <div className="value">
                                            {isOtherField(expense.reviewer_comment_type) ? (
                                                <>
                                                    {getReviewerCommentValueByStatus(
                                                        toRawStatusMap[expense.status],
                                                        expense.reviewer_comment_type,
                                                        auditedType,
                                                    )}{' '}
                                                    ({expense.reviewerComment})
                                                </>
                                            ) : (
                                                expense.reviewerComment
                                            )}
                                        </div>
                                    </Col>
                                </Row>
                            )}

                        {showApproveFields && (
                            <>
                                {currentStatus === RawExpenseStatus.AUDITED && (
                                    <Row gutter={[16, 24]}>
                                        <Col span={12}>
                                            <TextField
                                                name="approved_amount"
                                                label="Approved Amount"
                                                type="number"
                                                step={0.01}
                                                max={expense.submittedAmount}
                                                min={0}
                                                contentBefore="$"
                                                disabled={isSameStatus}
                                                onChange={(value) => handleApproveAmount(value)}
                                            />
                                        </Col>
                                        <Col span={12}>
                                            <TextField
                                                name="declined_amount"
                                                label="Declined Amount"
                                                type="number"
                                                step={0.01}
                                                max={expense.submittedAmount}
                                                min={0}
                                                contentBefore="$"
                                                disabled={isSameStatus}
                                                onChange={(value) => handleDeclinedAmount(value)}
                                            />
                                        </Col>
                                    </Row>
                                )}
                                {[RawExpenseStatus.AUDITED, RawExpenseStatus.IN_REVIEW].includes(
                                    currentStatus,
                                ) &&
                                    reviewersComments.length > 0 && (
                                        <SelectBox
                                            name="reviewer_comment_type"
                                            options={reviewersComments}
                                            disabled={isSameStatus}
                                        />
                                    )}
                                {isOtherField(
                                    methods.getValues(
                                        'reviewer_comment_type',
                                    ) as ReviewersCommentsType,
                                ) && (
                                    <TextField
                                        name="reviewer_comment"
                                        label="Reviewer Comment"
                                        type="textarea"
                                        disabled={isSameStatus}
                                    />
                                )}
                                {!isSameStatus && (
                                    <Button
                                        className="btn-primary margin-bottom-8"
                                        size="large"
                                        type="primary"
                                        htmlType="button" // Avoid default submit behavior
                                        loading={methods.formState.isSubmitting}
                                        onClick={handleSave} // Use custom handler
                                    >
                                        Save Change
                                    </Button>
                                )}
                            </>
                        )}
                    </Form>
                    <hr />
                    {expense.createdBy && (
                        <div className="submitter">
                            <div className="label">Submitted by:</div>
                            <div className="info-container">
                                <Avatar
                                    photoUrl={expense.createdBy.profilePhoto}
                                    name={expense.createdBy.name}
                                    size="medium"
                                    className="margin-right-12"
                                />
                                <div className="info">
                                    <div className="name">{expense.createdBy.name}</div>
                                    <div className="email">{expense.createdBy.email}</div>
                                </div>
                            </div>
                        </div>
                    )}
                    <hr />
                    <div className="date">Submitted: {formatDateString(expense.createdAt)}</div>
                    {showEditModal && (
                        <EditModal
                            claim={claim}
                            show={showEditModal}
                            disableNav={disableNav}
                            onClose={() => setShowEditModal(false)}
                            onSuccess={() => {
                                onEditSuccess();
                                setShowEditModal(false);
                            }}
                            expense={
                                editFieldData && editFieldData.date
                                    ? { ...expense, ...editFieldData }
                                    : expense
                            }
                            onPrevExpense={() => !!onPrevExpense && onPrevExpense()}
                            onNextExpense={() => !!onNextExpense && onNextExpense()}
                            setFieldsData={(data) => setData(data)}
                        />
                    )}
                </>
            </Modal>
            <Dialog
                show={showRequestReceiptDialog}
                onCancel={() => setShowRequestReceiptDialog(false)}
                onOk={() => handleRequestReceipt()}
                disabled={requestReceiptDialogDisabled}
            />
        </>
    );
};
export default DetailsModal;
