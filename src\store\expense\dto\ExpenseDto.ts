import {
    IsArray,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
    ValidateIf,
} from 'class-validator';
import PaginationDto from '../../../common/PaginationDto';
import { ExpenseStatus } from '../../../modules/claimDashboard/subpages/Expenses/utils';
import { ExpenseCategory } from '../../../types';
import { type MetadataGroup } from '../../claim/types/Claim';
import { ExpenseFilterParam, RawExpenseStatus, type ExpenseCreatedBy } from '../types/Expense';
import { Type } from 'class-transformer';
import { ReviewersCommentsType } from '../../../common/ReviewersCommentsType';

export class ExpenseDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsEnum(ExpenseCategory)
    category!: ExpenseCategory;

    @IsNotEmpty()
    @IsString()
    location!: string;

    @IsNotEmpty()
    @IsString()
    vendor!: string;

    @IsNotEmpty()
    @IsString()
    date!: string;

    @IsOptional()
    @IsNumber()
    submittedAmount?: number;

    @IsOptional()
    @IsNumber()
    approvedAmount?: number;

    @IsOptional()
    @IsNumber()
    declinedAmount?: number;

    @IsNotEmpty()
    // @IsEnum(ExpenseStatus)
    status!: ExpenseStatus;

    @IsNotEmpty()
    @IsString()
    imagePath!: string;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;

    @IsNotEmpty()
    @IsString()
    archivedAt!: string;

    @IsNotEmpty()
    @IsString()
    claimId!: string;

    @IsOptional()
    @IsString()
    notes?: string;

    @IsOptional()
    @IsString()
    reviewerComment?: string;

    @IsOptional()
    @IsString()
    reviewer_comment_type?: ReviewersCommentsType;

    @IsOptional()
    @IsObject()
    createdBy?: ExpenseCreatedBy;
}

export class ExpenseAddDto {
    @IsString()
    @IsNotEmpty()
    document_id!: string;

    @IsString()
    @IsNotEmpty()
    claim_id!: string;

    @IsEnum(ExpenseCategory)
    @IsNotEmpty({ message: 'Required' })
    category!: ExpenseCategory;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    location!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    vendor!: string;

    @IsNotEmpty({ message: 'Required' })
    submitted_amount!: string;

    @IsNotEmpty({ message: 'Required' })
    date!: any;

    @IsOptional()
    @IsString()
    notes?: string;
}

export class ExpenseUpdateDto {
    @IsEnum(ExpenseCategory)
    @IsNotEmpty({ message: 'Required' })
    category!: ExpenseCategory;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    location!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    vendor!: string;

    @IsNotEmpty({ message: 'Required' })
    date!: any;

    @Type(() => Number)
    @IsNotEmpty({ message: 'Required' })
    submitted_amount!: string;

    @Type(() => Number)
    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object: ExpenseUpdateDto) => object.status === RawExpenseStatus.AUDITED)
    approved_amount!: string;

    @IsOptional()
    declined_amount!: string;

    @IsString()
    @IsOptional()
    notes!: string;

    @ValidateIf((object: ExpenseUpdateDto) => {
        return (
            ReviewersCommentsType.IN_REVIEW_OTHER === object.reviewer_comment_type ||
            ReviewersCommentsType.APPROVED_OTHER === object.reviewer_comment_type ||
            ReviewersCommentsType.APPROVED_PARTIAL_OTHER === object.reviewer_comment_type ||
            ReviewersCommentsType.NOT_APPROVED_OTHER === object.reviewer_comment_type
        );
    })
    @IsNotEmpty({ message: 'Required' })
    @IsString()
    reviewer_comment!: string;

    @IsOptional()
    @IsString()
    reviewer_comment_type?: ReviewersCommentsType;

    @IsEnum(RawExpenseStatus)
    @IsNotEmpty({ message: 'Required' })
    status!: RawExpenseStatus;
}

export class ChangeExpenseStatusDto {
    @IsNotEmpty({ message: 'Required' })
    @Type(() => Number)
    @ValidateIf((object: ExpenseUpdateDto) => object.status === RawExpenseStatus.AUDITED)
    approved_amount!: string;

    @IsOptional()
    declined_amount!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    reviewer_comment!: string;

    @IsEnum(RawExpenseStatus)
    @IsNotEmpty({ message: 'Required' })
    status!: RawExpenseStatus;

    @IsOptional()
    @IsString()
    reviewer_comment_type?: ReviewersCommentsType | undefined;
}

export class ExpensePaginateDto extends PaginationDto {
    @IsString()
    @IsNotEmpty()
    meta_group_by?: string;

    @IsString()
    @IsOptional()
    search_term?: string;

    @IsOptional()
    @IsArray()
    params?: ExpenseFilterParam[];
}

export class ExpenseFilterDto {
    @IsOptional()
    @IsArray()
    category?: ExpenseCategory[];

    @IsOptional()
    dateRange?: [any, any];

    @IsOptional()
    submittedRange?: [number, number];

    @IsOptional()
    approvedRange?: [number, number];
}

export class ExpenseMetadataDto {
    @IsNotEmpty()
    @IsNumber()
    countTotal!: number;

    @IsNotEmpty()
    @IsNumber()
    grandTotal!: number;

    @IsNotEmpty()
    @IsNumber()
    approved_audited_amount!: number;

    @IsNotEmpty()
    @IsNumber()
    declined_amount!: number;

    @IsNotEmpty()
    @IsObject()
    counts!: MetadataGroup;

    @IsNotEmpty()
    @IsObject()
    percentages!: MetadataGroup;

    @IsNotEmpty()
    @IsObject()
    totals!: MetadataGroup;
}
