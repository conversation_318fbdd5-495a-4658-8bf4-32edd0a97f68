import { ReactComponent as ArrowLeft } from '../../../../assets/icons/arrow-flat-left.svg';
import { ReactComponent as ArrowRight } from '../../../../assets/icons/arrow-flat-right.svg';
import React from 'react';
import classNames from 'classnames';
import { useStoreState } from '../../../../store';
import { ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';

interface Props {
    children: React.ReactNode;
    disableNav?: boolean;
    disablePrev?: boolean;
    disableNext?: boolean;
    onNext: VoidFunction;
    onPrev: VoidFunction;
    expense?: ExpenseDto;
}

const ModalNavigationWrapper: React.FC<Props> = ({
    children,
    expense,
    disableNav,
    onPrev,
    onNext,
}) => {
    const { list, pagination } = useStoreState((state) => state.expense);
    const listCurrentExpenseIndex: number = list.value.findIndex((i) => i.id === expense?.id) + 1;

    const listLeftoverNumber =
        (pagination?.total && pagination?.perPage && pagination?.total % pagination?.perPage) || 0;

    const isLastPage =
        pagination?.total &&
        pagination?.perPage &&
        pagination?.currentPage >= pagination?.total / pagination?.perPage;

    const disableNext =
        isLastPage &&
        (!listLeftoverNumber
            ? listCurrentExpenseIndex === pagination?.perPage
            : listLeftoverNumber === listCurrentExpenseIndex);

    return (
        <div className="claim-expenses-details-side">
            {!disableNav && (
                <>
                    <div
                        className={classNames({
                            'claim-expenses-details-side-arrow': true,
                            left: true,
                            disabled:
                                pagination?.currentPage === 1 && listCurrentExpenseIndex === 1,
                        })}
                        onClick={() => {
                            !(pagination?.currentPage === 1 && listCurrentExpenseIndex === 1) &&
                                onPrev();
                        }}
                    >
                        <ArrowLeft />
                    </div>
                    <div
                        className={classNames({
                            'claim-expenses-details-side-arrow': true,
                            right: true,
                            disabled: disableNext,
                        })}
                        onClick={() => !disableNext && onNext()}
                    >
                        <ArrowRight />
                    </div>
                </>
            )}
            {children}
        </div>
    );
};
export default ModalNavigationWrapper;
