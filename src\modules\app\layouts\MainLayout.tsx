import { Layout } from 'antd';
import { Header } from './Header';

const { Content } = Layout;

interface Props {
    children: React.ReactNode;
    SubHeaderComponent?: React.ReactNode;
}

const MainLayout: React.FC<Props> = ({ children, SubHeaderComponent }) => {
    return (
        <Layout className="main-layout">
            <Header SubHeaderComponent={SubHeaderComponent} />
            <Content className="main-layout-content">{children}</Content>
        </Layout>
    );
};

export default MainLayout;
