.claim-expenses {
    &-heading {
        @include flex(flex, row, nowrap, space-between, center);
        border-bottom: 1px solid $color-gray-5;
        padding-bottom: 24px;

        button {
            @include flex(flex, row, nowrap, space-between, center);
            font-size: 16px;
            font-weight: $font-weight-semi-bold;
            line-height: 20px;
            gap: 16px;
            margin-right: 8px;
            padding: 13px 16px;
        }
    }

    &-add {
        &-form {
            .count {
                color: $color-gray-3;
                font-size: 12px;
                line-height: 16px;
                text-align: right;
            }

            .receipt {
                font-size: 18px;
                font-weight: $font-weight-semi-bold;
                line-height: 24px;
            }
        }

        .upload-file-card {
            @include flex(flex, column, nowrap, flex-start, stretch);
            background-color: $color-secondary-transparent;
            box-sizing: border-box;
            gap: 8px;
            padding: 16px 12px;

            &.error {
                background-color: $color-warning-transparent;

                .upload-progress-bar {
                    background-color: $color-warning !important;
                }
            }

            .filename {
                font-size: 15px;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
            }

            .filesize,
            .progress {
                color: $color-secondary-darker;
                font-size: 12px;
                line-height: 16px;
            }
        }
    }

    &-error {
        color: $color-error;
    }

    &-center {
        text-align: center;
    }

    &-attention {
        margin-bottom: 24px;
    }

    &-image {
        width: 24px;
        height: 22px;
    }

    &-moneyIcon {
        width: 18px;
        height: 16px;
    }

    &-disableIcon {
        width: 18px;
        height: 16px;
        position: absolute;
        top: 45px;
        right: 10px;
        opacity: 0.5;
    }

    &-attentionBlock {
        position: relative;
    }

    &-details {
        .date {
            color: $color-gray-3;
            line-height: 18px;
        }
        &-header {
            @include flex(flex, column, nowrap, flex-start, stretch);
            gap: 24px;

            .amounts {
                @include flex(flex, row, nowrap, space-between, flex-start);
                gap: 16px;

                .amount {
                    flex: 1;

                    .title {
                        color: $color-gray-3;
                        font-size: 16px;
                        font-weight: $font-weight-semi-bold;
                        line-height: 20px;
                        margin-bottom: 8px;
                    }

                    .value {
                        font-size: 18px;
                        font-weight: $font-weight-semi-bold;
                        line-height: 24px;
                    }
                }
            }

            .title {
                @include flex(flex, row, nowrap, space-between, flex-start);

                .edit {
                    @include flex(flex, row, nowrap, space-between, center);
                    border: 1px solid $color-gray-3;
                    color: $color-gray-3;
                    font-size: 16px;
                    font-weight: $font-weight-semi-bold;
                    gap: 4px;
                    line-height: 20px;
                    padding: 8px 16px;
                    transition: all 0.3s ease !important;

                    &:hover {
                        svg {
                            path {
                                stroke: black;
                            }
                        }
                    }
                }

                .vendor {
                    font-size: 24px;
                    font-weight: $font-weight-bold;
                    line-height: 28px;
                    max-width: 200px;
                }
            }

            .status {
                max-width: 100px;
            }
        }

        .info-group {
            .title {
                font-size: 16px;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
                margin-bottom: 8px;
            }

            .value {
                font-size: 16px;
                font-weight: $font-weight-light;
                line-height: 20px;
            }
        }

        .submitter {
            .label {
                font-size: 16px;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
                margin-bottom: 20px;
            }

            .info-container {
                @include flex(flex, row, nowrap, flex-start, stretch);

                .info {
                    @include flex(flex, column, nowrap, center, flex-start);
                    gap: 8px;

                    .name {
                        font-weight: $font-weight-semi-bold;
                        line-height: 18px;
                    }

                    .email {
                        color: $color-gray-3;
                        line-height: 18px;
                    }
                }
            }
        }

        &-side {
            @include flex(flex, row, nowrap, center, center);
            height: 100%;
            width: 100%;

            &-arrow {
                @include flex(flex, row, nowrap, center, center);
                background-color: #fff;
                border: 1px solid $color-gray-4;
                cursor: pointer;
                font-size: 16px;
                height: 48px;
                position: absolute;
                top: calc(50% - 24px);
                transition: all 0.3s ease;
                width: 48px;
                z-index: 2;

                &.left {
                    left: 24px;
                }

                &.right {
                    right: 24px;
                }

                &:hover {
                    opacity: 0.6;
                }

                &.disabled {
                    cursor: not-allowed;

                    &:hover {
                        background-color: transparent;
                    }
                }
            }
        }
    }

    &-list {
        &-category {
            color: $color-secondary;
            svg {
                height: 24px;
                width: 24px;
            }
        }

        &-date {
            span {
                color: $color-secondary-darker !important;
            }
        }

        &-status {
            @include flex(flex, row, nowrap, space-between, center);
            border-radius: 24px !important;
        }
    }

    &-search {
        @include flex(flex, row, nowrap, space-between, center);
        gap: 16px;
        padding: 24px 0;

        .filter-container {
            @include flex(flex, row, nowrap, flex-start, stretch);
            height: 100%;
        }

        .btn-clear {
            cursor: pointer;
            font-size: 16px;
            font-weight: $font-weight-semi-bold;
            line-height: 20px;
            text-decoration-line: underline;
        }
    }

    &-stats {
        @include flex(flex, row, nowrap, space-between, stretch);
        border-bottom: 1px solid $color-gray-5;
        margin-bottom: 24px;
        padding: 24px 0;

        &-cell {
            @include flex(flex, row, nowrap, space-between, stretch);
            border-right: 1px solid $color-gray-5;
            flex-grow: 1;
            padding: 0 20px;

            &.no-border {
                border-right: none;
            }

            &:first-child {
                padding-left: 0;
            }

            &:last-child {
                border-right: none;
            }

            .count {
                font-size: 18px;
            }

            &-info {
                @include flex(flex, column, nowrap, flex-start, flex-start);
                gap: 8px;
            }

            &-percent {
                @include flex(flex, column, nowrap, center, flex-end);
                font-size: 18px;
            }
        }
    }

    &-statuses {
        @include flex(flex, row, nowrap, flex-start, center);
        gap: 8px;
        margin: 24px 0;
    }
}
