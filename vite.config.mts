import { defineConfig } from 'vite';
import type { Plugin } from 'vite';
import react from '@vitejs/plugin-react';
import eslint from 'vite-plugin-eslint';
import viteTsConfigPaths from 'vite-tsconfig-paths';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import svgr from 'vite-plugin-svgr';

// Custom plugin to support both default (URL) and named (ReactComponent) exports for bare SVG imports
const svgDualExport = (): Plugin => ({
  name: 'svg-dual-export',
  enforce: 'pre',
  load(id) {
    if (id.endsWith('.svg') && !id.includes('?url') && !id.includes('?react')) {
      return `import ReactComponent from '${id}?react';
import url from '${id}?url';
export { ReactComponent };
export default url;`;
    }
    return null;
  },
});

export default defineConfig({
  plugins: [
    eslint({
      cache: false,
    }),
    react(),
    svgr({
      include: '**/*.svg',
      svgrOptions: {
        exportType: 'named',
        ref: true,
        svgo: false,
        titleProp: true,
      },
    }),
    viteTsConfigPaths({
      root: './',
    }),
    viteStaticCopy({
      targets: [
        {
          src: './src/assets/*',
          dest: 'assets/',
        },
      ],
    }),
  ],

  server: {
    port: 3600,
    host: 'localhost',
  },

  preview: {
    port: 3601,
    host: 'localhost',
  },
});
