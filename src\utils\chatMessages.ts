export const parseUrls = (text: string) => {
    const urlRegex =
        /(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/gim;

    return text.replace(urlRegex, (url) => {
        const httpRegex = /(?:https?|ftp|file):\/\//gim;
        const isHttp = httpRegex.test(url);

        return `<a href="${
            !isHttp ? 'https://' : ''
        }${url}" target="_blank" rel="noopener noreferrer">${url}</a>`;
    });
};
