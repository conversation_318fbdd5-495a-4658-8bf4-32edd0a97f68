import { plainToInstance } from 'class-transformer';
import UserDto from './dto/UserDto';
import { RawUserData, RawUserRole, UserRole } from './types/User';

export const mapUsers = (data: RawUserData[]): UserDto[] => data.map((rawUser) => mapUser(rawUser));

export const mapUser = (userData: RawUserData): UserDto => {
    const {
        profile_photo,
        created_at,
        role,
        job_title,
        chat_user_arn,
        preferred_contact_method,
        activation_sent_at,
        terms_accepted_at,
        ...data
    } = userData;

    return plainToInstance(UserDto, {
        ...data,
        profilePhoto: profile_photo,
        createdAt: created_at,
        jobTitle: job_title,
        chatUserArn: chat_user_arn,
        role: fromRawRole[role],
        contactMethod: preferred_contact_method || undefined,
        activationSentAt: activation_sent_at || undefined,
        emailVerifiedAt: terms_accepted_at || undefined,
    });
};

export const fromRawRole: {
    [key: string]: UserRole;
} = {
    [RawUserRole.ADJUSTER]: UserRole.ADJUSTER,
    [RawUserRole.ADMIN]: UserRole.ADMIN,
    [RawUserRole.SUPER_ADMIN]: UserRole.SUPER_ADMIN,
};

export const toRawRole: {
    [key: string]: RawUserRole;
} = {
    [UserRole.ADJUSTER]: RawUserRole.ADJUSTER,
    [UserRole.ADMIN]: RawUserRole.ADMIN,
    [UserRole.SUPER_ADMIN]: RawUserRole.SUPER_ADMIN,
};
