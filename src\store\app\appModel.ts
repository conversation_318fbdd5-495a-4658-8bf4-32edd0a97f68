import { persist } from 'easy-peasy';
import AppModel from './types/AppModel';
import authModel from '../auth/authModel';
import userModel from '../user/userModel';
import chatModel from '../chat/chatModel';
import claimModel from '../claim/claimModel';
import expenseModel from '../expense/expenseModel';
import profileModel from '../profile/profileModel';
import documentsModel from '../documents/documentsModel';
import policyHolderModel from '../policyHolder/policyHolderModel';
import reportModel from '../report/reportModel';
import notificationModel from '../notifications/notificationModel';
import invoiceModel from '../invoice/invoiceModel';

const appModel: AppModel = {
    auth: persist(authModel, {
        storage: 'localStorage',
    }),
    claim: claimModel,
    chat: chatModel,
    expense: expenseModel,
    profile: profileModel,
    documents: documentsModel,
    policyholder: policyHolderModel,
    report: reportModel,
    invoice: invoiceModel,
    user: userModel,
    notification: notificationModel,
};

export default appModel;
