<p align="center">
  <br>
  <img width="400" src="./src/assets/images/react.svg" alt="logo of viterts repository...">
  <br>
  <br>
</p>

# OoPE

## Features

- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/) - more strict
- [React Router](https://reactrouter.com/) - with lazy-loading / code-splitting use case
- [Prettier](https://prettier.io/) - with editor configuration [file](./.vscode/settings.json)
- [SASS/SCSS](https://sass-lang.com/) with [CSS Modules](https://github.com/css-modules/css-modules)

## Installation

### `npm install`

## Getting started

##### `npm run dev`

##### `npm run build`

##### `npm run preview`

##### `npm run lint`

##### `npm run lint:ts`

##### `npm run lint:eslint`

##### `npm run format:ts`

##### `npm run format`

##### `npm run format:check`

##### `npm run test`

## Deployment to production and Pull Request Flow

### Creating a Pull Request from `dev` to `main`

1. **Push to Remote**
   ```sh
   git push origin dev
   ```

2. **Open a Pull Request**
- Navigate to the GitHub repository.
- Click on the `Compare & pull request` button.
- Base branch: `main`
- Compare branch: `dev`
- Provide a descriptive title and detailed description.
- Link relevant issues or tickets.
- Request reviews from team members.

### Code Review

1. **Review Process**
- Team members review the PR and provide feedback.
- Address comments by pushing additional commits to the `dev` branch.

2. **Approval**
- Once approved and checks pass, a team member merges the PR into the `main` branch.

**Note:**
- Keep your `dev` branch up-to-date before creating a PR.
- Ensure all tests pass before pushing changes.


[Aljosa Numic](https://github.com/Gosim89)
[Bojan Topalovic](https://github.com/TopBojan)
[Milan Predic](https://github.com/milanpredic)
