name: CI / CD

on:
    push:
        branches:
            - main
            - dev
    workflow_dispatch:

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3
              with:
                  fetch-depth: 0

            - uses: actions/setup-node@v3
              with:
                  node-version: '18'
                  cache: 'npm'

            - uses: actions/cache@v3
              with:
                  path: dist
                  key: dist-${{ github.sha }}

            - name: Install
              run: npm ci

            - name: Build PROD
              if: github.ref == 'refs/heads/main'
              run: npm run build
              env:
                  VITE_API_URL: ${{ secrets.VITE_PROD_API_URL }}

            - name: Build DEV
              if: github.ref == 'refs/heads/dev'
              run: npm run build
              env:
                VITE_API_URL: ${{ secrets.VITE_API_URL }}

    admin-app:
        needs: [build]
        runs-on: ubuntu-latest
        steps:
            - uses: actions/checkout@v3

            - uses: actions/cache@v3
              with:
                  path: dist
                  key: dist-${{ github.sha }}

            - uses: docker/login-action@v2
              with:
                  registry: 738408760783.dkr.ecr.us-east-1.amazonaws.com
                  username: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  password: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

            - id: meta
              uses: docker/metadata-action@v4
              with:
                  images: 738408760783.dkr.ecr.us-east-1.amazonaws.com/oope-admin-app

            - uses: docker/build-push-action@v3
              with:
                  context: .
                  push: true
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
                  file: Dockerfile

    deploy-k8s:
        needs: [admin-app]
        runs-on: ubuntu-latest
        steps:
            - uses: aws-actions/configure-aws-credentials@v1
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: us-east-1

            - uses: actions/checkout@v3
              with:
                  sparse-checkout: 'kubernetes'
                  fetch-depth: 1

            - name: Apply PROD deployment to k8s...
              if: github.ref == 'refs/heads/main'
              uses: kodermax/kubectl-aws-eks@master
              env:
                  KUBE_CONFIG_DATA: ${{ secrets.PROD_KUBECONFIG }}
                  KUBECTL_VERSION: 'v1.27.2'
              with:
                  args: apply -f kubernetes/oope-admin-prod.yaml

            - name: Apply DEV deployment to k8s...
              if: github.ref == 'refs/heads/dev'
              uses: kodermax/kubectl-aws-eks@master
              env:
                  KUBE_CONFIG_DATA: ${{ secrets.KUBECONFIG }}
                  KUBECTL_VERSION: 'v1.27.2'
              with:
                  args: apply -f kubernetes/oope-admin-dev.yaml

            - name: Deploy to PROD
              if: github.ref == 'refs/heads/main'
              uses: kodermax/kubectl-aws-eks@master
              env:
                  KUBE_CONFIG_DATA: ${{ secrets.PROD_KUBECONFIG }}
                  KUBECTL_VERSION: 'v1.27.2'
              with:
                  args: rollout restart deployment oope-admin -n prod

            - name: Deploy to DEV
              if: github.ref == 'refs/heads/dev'
              uses: kodermax/kubectl-aws-eks@master
              env:
                  KUBE_CONFIG_DATA: ${{ secrets.KUBECONFIG }}
                  KUBECTL_VERSION: 'v1.27.2'
              with:
                  args: rollout restart deployment oope-admin -n dev

    send-notification:
        needs: [deploy-k8s]
        if: success()
        runs-on: ubuntu-latest
        steps:
            - name: Get current date
              id: date
              run: echo "date=$(date +'%Y-%m-%d %H:%M:%S %Z')" >> $GITHUB_OUTPUT

            - name: Set deployment status
              id: deployment_status
              run: |
                if [[ "${{ job.status }}" == 'success' ]]; then
                  echo "STATUS=success" >> $GITHUB_OUTPUT
                  echo "EMOJI=✅" >> $GITHUB_OUTPUT
                else
                  echo "STATUS=failed" >> $GITHUB_OUTPUT
                  echo "EMOJI=❌" >> $GITHUB_OUTPUT
                fi

            - name: Send deployment notification
              uses: dawidd6/action-send-mail@v3
              if: always()
              with:
                  server_address: smtp.sendgrid.net
                  server_port: 465
                  username: apikey
                  password: ${{ secrets.SENDGRID_API_KEY }}
                  subject: 'Deployment Notification: ${{ github.workflow }} - ${{ github.sha }}'
                  to: ${{ secrets.ADMIN_EMAILS }}
                  from: 'Oope <<EMAIL>>'
                  secure: true
                  ssl: true
                  content_type: text/plain
                  body: |
                    ===== DEPLOYMENT NOTIFICATION =====

                    Application: OOPE Admin
                    Environment: ${{ github.ref == 'refs/heads/main' && 'Production' || 'Development' }}
                    Status: ${{ steps.deployment_status.outputs.EMOJI }} ${{ steps.deployment_status.outputs.STATUS }}
                    Branch: ${{ github.ref_name }}
                    Commit: ${{ github.sha }}
                    Deployment Time: ${{ steps.date.outputs.date }}
                    
                    --- Workflow Details ---
                    Run URL: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}
                    
                    This is an automated notification. Please do not reply to this email.
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}



