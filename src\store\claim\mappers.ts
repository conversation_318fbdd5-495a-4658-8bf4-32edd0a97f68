import { plainToInstance } from 'class-transformer';
import { mapPolicyHolder } from '../policyHolder/mappers';
import { mapUser } from '../user/mappers';
import { ClaimDto, ExpenseRangesDto } from './dto/ClaimDto';
import {
    type ClaimLossAddress,
    RawClaim,
    RawClaimLossAddress,
    RawMetadata,
    RawMetadataGroup,
    MetadataGroup,
    RawExpenseRanges,
    RawClaimStatusMap,
} from './types/Claim';
import { ExpenseMetadataDto } from '../expense/dto/ExpenseDto';

export const mapClaim = ({
    id,
    archived_at,
    started_at,
    report_days,
    policy_holder,
    adjuster,
    admin,
    created_at,
    updated_at,
    chat_channel_arn,
    loss_address,
    number_of_adults_in_household,
    number_of_children_in_household,
    number_of_pets,
    type_of_pets,
    date_of_loss,
    ale_limits,
    normal_daily_food_expenditure,
    length_of_relocation,
    special_needs,
    insurance_company,
    insured_claim_number,
    temporary_address,
    status,
    loss_reason,
    type_of_policy,
    approved_receipt_categories,
    approved_receipt_start_date,
    approved_receipt_end_date,
    alcohol_approved,
    excessive_spending,
    alcohol_limited_to,
    groceries_approved,
    unitemized_receipts_approved,
    bank_statements_approved,
    approved_receipts_mile_radius,
    description,
    ...rawClaim
}: RawClaim): ClaimDto =>
    plainToInstance(ClaimDto, {
        ...rawClaim,
        id,
        createdAt: created_at,
        updatedAt: updated_at,
        startedAt: started_at || undefined,
        archivedAt: archived_at || undefined,
        reportDays: report_days,
        policyHolder: mapPolicyHolder(policy_holder),
        adjuster: adjuster ? mapUser(adjuster) : undefined,
        admin: admin ? mapUser(admin) : undefined,
        chatChannelArn: chat_channel_arn,
        lossAddress: mapLossAddress(loss_address),
        temporaryAddress: temporary_address || undefined,
        numberAdults: number_of_adults_in_household,
        numberChildren: number_of_children_in_household,
        numberPets: number_of_pets,
        petTypes: type_of_pets,
        lossDate: date_of_loss,
        aleLimit: ale_limits,
        normalDailyExpense: normal_daily_food_expenditure,
        relocationLength: length_of_relocation,
        specialNeeds: special_needs,
        insuranceCompany: insurance_company,
        claimNumber: insured_claim_number,
        status: RawClaimStatusMap[status],
        lossReason: loss_reason,
        typeOfPolicy: type_of_policy,
        approvedReceiptCategories: approved_receipt_categories,
        approvedReceiptStartDate: approved_receipt_start_date,
        approvedReceiptEndDate: approved_receipt_end_date,
        alcoholApproved: alcohol_approved,
        alcoholLimitedTo: alcohol_limited_to,
        excessiveSpending: excessive_spending,
        groceriesApproved: groceries_approved,
        unitemizedReceiptsApproved: unitemized_receipts_approved,
        bankStatementsApproved: bank_statements_approved,
        approvedReceiptsMileRadius: approved_receipts_mile_radius,
        description: description,
    });

export const mapClaims = (rawClaims: RawClaim[]): ClaimDto[] =>
    rawClaims.map((rawClaim) => mapClaim(rawClaim));

export const mapLossAddress = ({
    street_address,
    zip_code,
    ...rest
}: RawClaimLossAddress): ClaimLossAddress => ({
    ...rest,
    streetAddress: street_address,
    zipCode: zip_code,
});

export const mapMetadata = ({
    counts,
    percentages,
    totals,
    ...raw
}: RawMetadata): ExpenseMetadataDto =>
    plainToInstance(ExpenseMetadataDto, {
        countTotal: raw.count_total,
        grandTotal: raw.grand_total,
        approved_audited_amount: raw.approved_audited_amount,
        declined_amount: raw.declined_amount,
        counts: mapMetadataGroup(counts),
        percentages: mapMetadataGroup(percentages),
        totals: mapMetadataGroup(totals),
    });

export const mapMetadataGroup = ({
    in_review,
    submitted,
    audited,
}: RawMetadataGroup): MetadataGroup => ({
    submitted: submitted || undefined,
    inReview: in_review || undefined,
    audited: audited || undefined,
});

export const mapExpenseRanges = ({ approved, submitted }: RawExpenseRanges): ExpenseRangesDto => {
    return plainToInstance(ExpenseRangesDto, {
        approved: {
            min: Number(approved.min),
            max: Number(approved.max),
        },
        submitted: {
            max: Number(submitted.max),
            min: Number(submitted.min),
        },
    });
};
