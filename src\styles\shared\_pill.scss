@keyframes fadeIn { 
    from { opacity: 0; } 
}

.pill {
    padding: 4px 8px !important;
    text-align: center;

    &.primary {
        color: $color-secondary;
    }

    &.inactive {
        color: $color-secondary-darker;
    }

    &.success {
        background-color: $color-success;
        color: white !important;
        border-radius: 24px;

        &:hover {
            border: none !important;
        }
    }

    &.ghost {
        background: none !important;
        border: none !important;
        color: $color-gray-3 !important;
        line-height: 18px;
    }

    &.info {
        color: $color-info !important;

        &:hover {
            border-color: $color-info !important;
        }
    }

    &.warning {
        color: $color-warning !important;

        &:hover {
            border-color: $color-warning !important;
        }
    }

    &.error {
        color: $color-error !important;

        &:hover {
            border-color: $color-error !important;
        }
    }

    &.animated {
        animation: fadeIn 1s infinite alternate;
    }
}
