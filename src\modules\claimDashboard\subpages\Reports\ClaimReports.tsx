import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { canAccess } from '../../../../common/canAccess';
import { useStoreActions, useStoreState } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ReportFilterParam, ReportFilterableFields } from '../../../../store/report/type/Report';
import { UserRole } from '../../../../store/user/types/User';
import { DEFAULT_PER_PAGE, PaginationDirection, makeFilterParam } from '../../../../utils/paginate';
import FlashMessages from '../../../app/FlashMessages';
import Dialog from '../../../app/shared/Dialog';
import FilterButton from '../../../app/shared/FilterButton';
import Pagination from '../../../app/shared/Pagination';
import DetailsModal from './DetailsModal';
import FilterModal from './FilterModal';
import Header from './Header';
import List from './List';
import useReportActions from './useReportActions';
import RequestReportList from './RequestReportList';
import { QuickActions } from './QuickActions';
import { ReportDto, RequestReportDto } from '../../../../store/report/dto/ReportDto';

interface Props {
    claim: ClaimDto;
}

const Reports: React.FC<Props> = ({ claim }) => {
    const params = useParams();
    const navigate = useNavigate();
    const isReportRequest = location.pathname.includes('/request');
    const [showReportRequest, setShowReportRequest] = useState(isReportRequest);
    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [filterParams, setFilterParams] = useState<ReportFilterParam[]>([]);
    const [showFilterModal, setShowFilterModal] = useState(false);
    const [adminRequestReportManualList, setadminRequestReportManual] = useState<
        RequestReportDto[]
    >([]);
    const [notAdminRequestReportManualList, setnotAdminRequestReportManual] = useState<ReportDto[]>(
        [],
    );
    const { getClaim } = useStoreActions((actions) => actions.claim);

    const { list, pagination } = useStoreState((state) => state.report);
    const { get, checkIfPdfReady } = useStoreActions((actions) => actions.report);
    const { latestClaim } = useStoreState((state) => state.claim);

    const handleGetReports = useCallback(() => {
        const params = [
            ...filterParams,
            makeFilterParam<ReportFilterableFields>('claim_id', '=', claim.id),
        ];
        canAccess([UserRole.ADJUSTER]) &&
            params.push(makeFilterParam<ReportFilterableFields>('status', '!=', 'created'));

        get({
            page: currentPage,
            limit: DEFAULT_PER_PAGE,
            order_by: 'created_at',
            direction: PaginationDirection.DESC,
            params,
        });
    }, [claim.id, currentPage, filterParams, get]);

    const handleGetClaim = useCallback(() => {
        if (!claim.id) return;
        getClaim(claim.id);
    }, [getClaim, claim.id]);

    useEffect(() => {
        const pollStatus = async (itemId) => {
            try {
                const response = await checkIfPdfReady(itemId);
                if (response.is_ready) {
                    handleGetReports();
                }
            } catch (error) {
                console.error(`Error checking status:`, error);
            }
        };

        const pendingItems = list.value?.filter((item) => item.status === 'preparing');
        const adminRequestReportManual = list.value?.filter(
            (item) => item.reportType === 'adminRequestReportManual',
        );
        setadminRequestReportManual(adminRequestReportManual);
        const notAdminRequestReportManual = list.value?.filter(
            (item) => item.reportType !== 'adminRequestReportManual',
        );
        setnotAdminRequestReportManual(notAdminRequestReportManual);
        const timers = pendingItems.map((item) => setInterval(() => pollStatus(item.id), 5000));

        return () => {
            timers.forEach((timer) => clearInterval(timer));
        };
    }, [list, checkIfPdfReady, handleGetReports]);

    const {
        selectedReport,
        showDetailsModal,
        setShowDetailsModal,
        onActionClick,
        actionLoading,
        showDeleteDialog,
        onDeleteCancel,
        onDeleteOk,
    } = useReportActions(handleGetReports);

    useEffect(() => {
        handleGetReports();
        handleGetClaim();
    }, [handleGetReports, handleGetClaim]);

    useEffect(() => {
        setShowReportRequest(isReportRequest);
    }, [isReportRequest]);

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);
        navigate(`/claim/${claim.id}/reports/page/${page}`);
    };

    const handleAddSuccess = async () => {
        FlashMessages.success('Your report is being prepared.');
        setCurrentPage(1);
        setFilterParams([]);
        navigate(`/claim/${claim.id}/reports`);
    };

    const handleAddRequestSuccess = async () => {
        setCurrentPage(1);
        setFilterParams([]);
        navigate(`/claim/${claim.id}/reports/request`);
    };

    return (
        <>
            <div className="claim-reports">
                <QuickActions claim={claim} />
                {!showReportRequest && (
                    <div className="claim-reports box">
                        <Header claim={claim} onAddSuccess={() => handleAddSuccess()} />

                        <FilterButton
                            onClick={() => setShowFilterModal(true)}
                            onClear={() => {
                                setCurrentPage(1);
                                setFilterParams([]);
                                navigate(`/claim/${claim.id}/reports`);
                            }}
                            filterCount={filterParams.length}
                            className="margin-bottom-24 claim-reports-filter-btn"
                        />
                        <List
                            claimArchived={!!claim && !!claim.archivedAt}
                            list={list}
                            onActionClick={onActionClick}
                            actionLoading={actionLoading}
                            claim={claim}
                            notAdminRequestReportManualList={{
                                value: notAdminRequestReportManualList,
                                loading: false,
                                loaded: true,
                            }}
                        />

                        {pagination && (
                            <Pagination {...pagination} onChange={handlePaginationChange} />
                        )}
                        {showDetailsModal && selectedReport && claim && (
                            <DetailsModal
                                onActionClick={onActionClick}
                                claim={claim}
                                show={showDetailsModal}
                                onClose={() => setShowDetailsModal(false)}
                                onSuccess={() => {
                                    setShowDetailsModal(false);
                                    handleGetReports();
                                }}
                                report={selectedReport}
                            />
                        )}
                        {showFilterModal && claim && (
                            <FilterModal
                                show={showFilterModal}
                                onClose={() => setShowFilterModal(false)}
                                claim={claim}
                                onFilterChange={(params) => {
                                    setFilterParams(params);
                                    setCurrentPage(1);
                                    navigate(`/claim/${claim.id}/reports`);
                                }}
                                filterParams={filterParams}
                            />
                        )}
                        <Dialog
                            show={showDeleteDialog}
                            onCancel={onDeleteCancel}
                            onOk={onDeleteOk}
                            disabled={!!actionLoading}
                        />
                    </div>
                )}
                {showReportRequest && (
                    <div className="claim-reports box">
                        <Header claim={claim} onAddSuccess={() => handleAddRequestSuccess()} />
                        <RequestReportList
                            claimArchived={!!claim && !!claim.archivedAt}
                            list={list}
                            onActionClick={onActionClick}
                            actionLoading={actionLoading}
                            // claim={claim}
                            claim={latestClaim || claim}
                            adminRequestReportManualList={{
                                value: adminRequestReportManualList,
                                loading: false,
                                loaded: true,
                            }}
                        />
                        {pagination && (
                            <Pagination {...pagination} onChange={handlePaginationChange} />
                        )}
                        {showDetailsModal && selectedReport && claim && (
                            <DetailsModal
                                onActionClick={onActionClick}
                                claim={claim}
                                show={showDetailsModal}
                                onClose={() => setShowDetailsModal(false)}
                                onSuccess={() => {
                                    setShowDetailsModal(false);
                                    handleGetReports();
                                }}
                                report={selectedReport}
                            />
                        )}
                        <Dialog
                            show={showDeleteDialog}
                            onCancel={onDeleteCancel}
                            onOk={onDeleteOk}
                            disabled={!!actionLoading}
                        />
                    </div>
                )}
            </div>
        </>
    );
};

export default Reports;
