import { Button } from 'antd';
import { useForm } from 'react-hook-form';
import { ReactComponent as LogoImage } from '../../assets/images/logo.svg';
import { LoginDto } from '../../store/auth/dto/LoginFormDto';

import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { useState } from 'react';
import { useStoreActions } from '../../store';
import CodeVerificationField from '../app/shared/form/CodeVerificationField';
import Form from '../app/shared/form/Form';

interface Props {
    loginInfo: LoginDto;
    onLogin: VoidFunction;
    onBack: VoidFunction;
}

const CodeLoginForm: React.FC<Props> = ({ loginInfo, onLogin, onBack }) => {
    const [codeError, setCodeError] = useState(false);
    const [formDisabled, setFormDisabled] = useState(false);
    const [resendDisabled, setResendDisabled] = useState(false);

    const { login, requestLogin } = useStoreActions((actions) => actions.auth);

    const methods = useForm<LoginDto>({
        resolver: classValidatorResolver(LoginDto),
        defaultValues: loginInfo,
    });

    const handleLogin = async (fields) => {
        setFormDisabled(true);

        try {
            await login(fields);
            setCodeError(false);
            return onLogin();
        } catch {
            setCodeError(true);
            methods.setValue('code', '');
        } finally {
            setFormDisabled(false);
        }
    };

    const onResendCode = async () => {
        setResendDisabled(true);
        try {
            await requestLogin(loginInfo);
        } catch (err: any) {
            console.error(err.message);
        } finally {
            setResendDisabled(false);
        }
    };

    return (
        <Form className="auth-form" methods={methods} onSubmit={methods.handleSubmit(handleLogin)}>
            <LogoImage className="auth-form-logo" />
            <div>
                <div className="heading h4 margin-bottom-24">Authenticate Account</div>
                <div className="auth-form-code-verification-text margin-bottom-24">
                    For your security, please enter the 6-digit code we sent to your email to
                    confirm it&lsquo;s you.
                </div>
                <div className="auth-form-code-verification-text margin-bottom-24">
                    Check your inbox for an email from us with the code and check spam and other
                    folders just in case.
                </div>
                <div className="auth-form-code-verification-text">
                    Enter the 6-digit code in the box below.
                </div>
            </div>

            <div>
                <CodeVerificationField
                    name="code"
                    error={codeError}
                    disabled={formDisabled}
                    onFinish={() => methods.handleSubmit(handleLogin)()}
                />
                <Button
                    loading={resendDisabled}
                    type="link"
                    className="btn-text auth-form-resend-code-link"
                    onClick={() => onResendCode()}
                >
                    Resend new code
                </Button>
            </div>
            <div className="auth-form-actions">
                <Button
                    htmlType="submit"
                    size="large"
                    className="btn-primary margin-bottom-8"
                    loading={methods.formState.isSubmitting}
                    block
                >
                    Submit
                </Button>
                <Button
                    size="large"
                    type="link"
                    onClick={onBack}
                    disabled={methods.formState.isSubmitting}
                    block
                >
                    Back
                </Button>
            </div>
        </Form>
    );
};

export default CodeLoginForm;
