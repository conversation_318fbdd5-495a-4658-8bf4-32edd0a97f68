import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import PaginationDto from '../../../common/PaginationDto';
import { PolicyHolderFilterParam } from '../types/PolicyHolder';

export default class PolicyHolderDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    name!: string;

    @IsNotEmpty()
    @IsEmail()
    email!: string;

    @IsNotEmpty()
    @IsString()
    phone!: string;

    @IsString()
    @IsOptional()
    address?: string;

    @IsNotEmpty()
    @IsString()
    company!: string;

    @IsNotEmpty()
    @IsString()
    status!: string;

    @IsNotEmpty()
    @IsString()
    profilePhoto!: string;

    @IsNotEmpty()
    @IsString()
    jobTitle!: string;

    @IsNotEmpty()
    @IsString()
    contactMethod!: string;

    @IsNotEmpty()
    @IsString()
    timezone!: string;

    @IsNotEmpty()
    @IsString()
    chatUserArn!: string | null;

    @IsString()
    @IsOptional()
    insuranceCompany?: string;

    @IsString()
    @IsNotEmpty()
    claimNumber!: string;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;

    @IsString()
    @IsOptional()
    deactivatedAt?: string;

    @IsOptional()
    @IsString()
    emailVerifiedAt?: string;

    @IsOptional()
    @IsString()
    activationSentAt?: string;
}

export class PolicyHolderPaginateDto extends PaginationDto {
    @IsOptional()
    @IsString()
    search_term?: string;

    @IsOptional()
    params?: PolicyHolderFilterParam[];
}
