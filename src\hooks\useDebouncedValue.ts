import { useEffect, useState } from 'react';

export default function useDebouncedValue<T = any>(value: T, time = 250) {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
        const timeout = setTimeout(() => {
            setDebouncedValue(value);
        }, time);

        return () => {
            clearTimeout(timeout);
        };
    }, [value, time]);

    return debouncedValue;
}
