import { ThemeConfig } from 'antd';

const colorSecondary = '#6bcaba';
const colorText = '#003049';
const colorGray6 = '#F2F2F2';
const colorTertiaryLighter = '#f0faf8';
const colorBackground = '#eff2f7';

const theme: ThemeConfig = {
    token: {
        colorPrimary: colorSecondary,
        fontFamily: 'Mulish',
        colorLink: colorText,
        colorText: colorText,
        screenXL: 1400,
        screenXLMin: 1400,
        screenLGMax: 1399,
    },
    components: {
        Button: {
            borderRadius: 0,
            colorBgContainerDisabled: colorGray6,
        },
        Slider: {
            handleSize: 24,
            handleColor: colorSecondary,
            handleSizeHover: 24,
            trackBg: colorTertiaryLighter,
            trackHoverBg: colorTertiaryLighter,
            handleLineWidthHover: 2,
            railBg: colorBackground,
        },
        Select: {
            colorBgContainer: colorGray6,
            colorBgElevated: colorGray6,
            colorPrimary: colorSecondary,
            borderRadius: 0,
            colorText: '#003049',
            colorPrimaryBorder: colorSecondary,
            boxShadow: undefined,
            controlOutline: undefined,
            controlOutlineWidth: 0,
        },
        Checkbox: {
            colorPrimary: colorSecondary,
            colorPrimaryHover: colorSecondary,
        },
        DatePicker: {
            colorPrimary: colorSecondary,
            colorBgTextHover: 'red',
            colorBorder: colorSecondary,
            colorBorderBg: colorSecondary,
            colorBgSpotlight: colorSecondary,
            colorPrimaryBorder: colorSecondary,
            colorBorderSecondary: colorSecondary,
            colorPrimaryBorderHover: colorSecondary,
            colorInfoBorder: colorSecondary,
            colorInfoBorderHover: colorSecondary,
            boxShadow: 'none',
            boxShadowSecondary: 'none',
        },
    },
};

export default theme;
