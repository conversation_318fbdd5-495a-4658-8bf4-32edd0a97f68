import { Button, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as IconArrow } from '../../../../assets/icons/arrow-forward-sharp.svg';
import { isOpen } from '../../../../common/claim';
import { useStoreActions, useStoreState } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ExpenseFitlerableFields, ExpenseOrderBy } from '../../../../store/expense/types/Expense';
import { PaginationDirection, makeFilterParam } from '../../../../utils/paginate';
import Dialog from '../../../app/shared/Dialog';
import ChangeExpenseStatusModal from '../Expenses/ChangeExpenseStatusModal';
import DetailsModal from '../Expenses/DetailsModal';
import EditModal from '../Expenses/EditModal';
import { ExpensesList } from '../Expenses/ExpensesList';
import useExpenseActions from '../Expenses/useExpenseActions';
import { ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';

interface Props {
    claim: ClaimDto;
}

const LatestExpenses: React.FC<Props> = ({ claim }) => {
    const navigate = useNavigate();
    const [editFieldData, setEditFieldsData] = useState({ date: null });

    const { list, pagination } = useStoreState((state) => state.expense);
    const { get } = useStoreActions((actions) => actions.expense);

    const setData = (data) => {
        setEditFieldsData(data);
    };

    const handleGetExpenses = () => {
        get({
            claimId: claim.id,
            dto: {
                order_by: ExpenseOrderBy.DATE,
                direction: PaginationDirection.DESC,
                limit: 5,
                params: [
                    makeFilterParam<ExpenseFitlerableFields>('claim_id', '=', claim.id),
                    makeFilterParam<ExpenseFitlerableFields>('archived_at', 'is null'),
                ],
            },
        }).then((r) => {
            const selectedExpenseUpdatedInList: ExpenseDto = r.find(
                (e) => e.id === selectedExpense?.id,
            );
            !!selectedExpenseUpdatedInList && setSelectedExpense(selectedExpenseUpdatedInList);
        });
    };
    // [claim.id, get], // eslint-disable-line react-hooks/exhaustive-deps

    const {
        setSelectedExpense,
        selectedExpense,
        showDetailsModal,
        setShowDetailsModal,
        showEditModal,
        selectedStatus,
        setShowEditModal,
        onActionClick,
        onExpenseStatusChange,
        isStatusLoading,
        showDeclineDialog,
        onDeclineDialogCancel,
        onDeclineDialogOk,
        showChangeExpenseStatusModal,
        onChangeExpenseStatusClose,
        onChangeExpenseStatusSuccess,
        showArchiveDialog,
        onArchiveDialogOk,
        onArchiveDialogCancel,
        receiptDialogLoading,
        showReceiptDialog,
        setShowReceiptDialog,
        handleRequestReceipt,
    } = useExpenseActions(handleGetExpenses);

    useEffect(() => {
        handleGetExpenses();
    }, [claim.id, get]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
        <div className="box margin-bottom-16 claim-dashboard-widget">
            <div className="claim-dashboard-widget-heading flex-space">
                <div className="heading medium margin-bottom-16">Expenses by Date</div>
                {(pagination?.total || 0) > 5 && (
                    <Button
                        type="primary"
                        size="large"
                        className="btn-secondary outline"
                        onClick={() => navigate(`/claim/${claim.id}/expenses`)}
                    >
                        <Space>
                            <span>
                                <b>View All</b>
                            </span>
                            <IconArrow />
                        </Space>
                    </Button>
                )}
            </div>
            <ExpensesList
                readonly={!!claim.archivedAt || !isOpen(claim)}
                list={list}
                claim={claim}
                onActionClick={onActionClick}
                onStatusChange={onExpenseStatusChange}
                isStatusLoading={isStatusLoading}
            />
            {showChangeExpenseStatusModal && selectedExpense && selectedStatus && (
                <ChangeExpenseStatusModal
                    show={showChangeExpenseStatusModal}
                    disableNav={true}
                    claim={claim}
                    onClose={() => onChangeExpenseStatusClose()}
                    onSuccess={() => onChangeExpenseStatusSuccess()}
                    expense={selectedExpense}
                    status={selectedStatus}
                />
            )}
            {(() => {
                console.log('<<<<<<<<expense.imagePath>>>>>>>', editFieldData);
                return null; // Ensure nothing is rendered to the DOM
            })()}
            {showDetailsModal && selectedExpense && (
                <DetailsModal
                    claim={claim}
                    disableNav={true}
                    expense={selectedExpense}
                    onClose={() => setShowDetailsModal(false)}
                    onStatusChangeSuccess={() => handleGetExpenses()}
                    show={showDetailsModal}
                    onEditSuccess={() => {
                        handleGetExpenses();
                    }}
                    readonly={!!claim.archivedAt || !isOpen(claim)}
                />
            )}
            {showEditModal && selectedExpense && (
                <EditModal
                    claim={claim}
                    disableNav={true}
                    expense={selectedExpense}
                    setFieldsData={(data) => setData(data)}
                    show={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                    }}
                    onSuccess={() => {
                        handleGetExpenses();
                    }}
                />
            )}

            <Dialog
                show={showDeclineDialog}
                onCancel={onDeclineDialogCancel}
                onOk={onDeclineDialogOk}
                disabled={!!isStatusLoading}
            />

            <Dialog
                show={showArchiveDialog}
                onCancel={onArchiveDialogCancel}
                onOk={onArchiveDialogOk}
                disabled={!!isStatusLoading}
            />
            <Dialog
                onCancel={() => setShowReceiptDialog(false)}
                onOk={() => handleRequestReceipt()}
                show={showReceiptDialog}
                disabled={receiptDialogLoading}
            />
        </div>
    );
};
export default LatestExpenses;
