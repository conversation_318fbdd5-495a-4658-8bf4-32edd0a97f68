import { Button, Col, Row, Space } from 'antd';
import { useStoreActions } from '../../../../../store';
import { ReactComponent as IconLogout } from '../../../../../assets/icons/log-out-outline.svg';

const MyAccountSubHeader: React.FC = () => {
    const { logout } = useStoreActions((actions) => actions.auth);

    const onLogout = () => {
        logout();
    };

    return (
        <div className="header-sub account-subheader">
            <Row justify="space-between" align="middle">
                <Col>
                    <h5 className="account-subheader-title">Account Settings</h5>
                </Col>
                <Col>
                    <Button className="btn-text color-error" onClick={onLogout} type="link">
                        <Space>
                            <IconLogout />
                            <span>Logout</span>
                        </Space>
                    </Button>
                </Col>
            </Row>
        </div>
    );
};

export default MyAccountSubHeader;
