import { IsEmail, IsEnum, IsNotEmpty, IsOptional, IsString, ValidateIf } from 'class-validator';

import PaginationDto from '../../../common/PaginationDto';
import { ContactMethod } from '../../../types';
import { RawUserRole, UserRole } from '../types/User';

export default class UserDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsString()
    @IsNotEmpty()
    name!: string;

    @IsNotEmpty()
    @IsEmail()
    email!: string;

    @IsString()
    @IsNotEmpty()
    phone!: string;

    @IsNotEmpty()
    @IsString()
    company!: string;

    @IsNotEmpty()
    @IsString()
    status!: string;

    @IsNotEmpty()
    @IsString()
    role!: UserRole;

    @IsNotEmpty()
    @IsString()
    profilePhoto!: string;

    @IsNotEmpty()
    @IsString()
    jobTitle!: string;

    @IsOptional()
    @IsEnum(ContactMethod)
    contactMethod?: ContactMethod;

    @IsNotEmpty()
    @IsString()
    timezone!: string;

    @IsNotEmpty()
    @IsString()
    chatUserArn!: string | null;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;

    @IsOptional()
    @IsString()
    emailVerifiedAt?: string;

    @IsOptional()
    @IsString()
    activationSentAt?: string;
}

export class UserUpdateDto {
    @IsString()
    @IsNotEmpty({ message: 'Required' })
    name!: string;

    @IsOptional()
    @IsEmail(undefined, { message: 'Email Invalid' })
    email!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    phone!: string;

    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as UserCreateDto).role === RawUserRole.ADJUSTER)
    company!: string;

    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as UserCreateDto).role === RawUserRole.ADJUSTER)
    job_title!: string;

    @IsNotEmpty({ message: 'Required' })
    @IsEnum(ContactMethod, { message: 'Please enter a valid preferred method of contact' })
    @ValidateIf((object) => (object as UserCreateDto).role === RawUserRole.ADJUSTER)
    preferred_contact_method!: ContactMethod;

    @IsOptional()
    @IsString()
    status!: string;

    @IsOptional()
    @IsString()
    role!: RawUserRole;

    @IsOptional()
    @IsString()
    timezone!: string;
}

export class UserCreateDto {
    @IsNotEmpty({ message: 'Required' })
    @IsEnum(RawUserRole, { message: 'Please select a valid role' })
    role!: RawUserRole;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    name!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    email!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    phone!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as UserCreateDto).role === RawUserRole.ADJUSTER)
    company!: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as UserCreateDto).role === RawUserRole.ADJUSTER)
    job_title!: string;

    @IsEnum(ContactMethod, { message: 'Please enter a valid preferred method of contact' })
    @IsNotEmpty({ message: 'Required' })
    preferred_contact_method!: ContactMethod;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    timezone!: string;
}
export class UserUpdatePhotoDto {
    @IsNotEmpty()
    document_id!: string | null;
}

export class UserPaginateDto extends PaginationDto {
    @IsOptional()
    @IsString()
    search_term?: string;

    @IsOptional()
    params?: any;
}
