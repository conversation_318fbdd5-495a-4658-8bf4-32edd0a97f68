import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useState } from 'react';
import { useStoreActions } from '../../../../store';
import { ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import FlashMessages from '../../../app/FlashMessages';
import { ExpenseActions } from './utils';

const useExpenseActions = (onGetExpenses: any) => {
    const [selectedExpense, setSelectedExpense] = useState<ExpenseDto>();
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [isStatusLoading, setIsStatusLoading] = useState<string | boolean>(false);
    const [showDeclineDialog, setShowDeclineDialog] = useState(false);
    const [showChangeExpenseStatusModal, setShowChangeExpenseStatusModal] = useState(false);
    const [showArchiveDialog, setShowArchiveDialog] = useState(false);
    const [showReceiptDialog, setShowReceiptDialog] = useState(false);
    const [receiptDialogLoading, setReceiptDialogLoading] = useState(false);
    const [selectedStatus, setSelectedStatus] = useState<RawExpenseStatus | undefined>();

    const { archive, requestReceipt, update, restore } = useStoreActions(
        (actions) => actions.expense,
    );

    const handleArchive = async (ids: string[]) => {
        setIsStatusLoading(true);
        try {
            await archive(ids);
            FlashMessages.success('Expense Archived');
            await onGetExpenses();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to archive expense');
        } finally {
            setIsStatusLoading(false);
        }
    };

    const handleRequestReceipt = async () => {
        setReceiptDialogLoading(true);
        try {
            if (!selectedExpense) throw new Error('No selected expense');
            await requestReceipt(selectedExpense?.id);
            FlashMessages.success('Receipt Requested');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to request receipt');
        } finally {
            setReceiptDialogLoading(false);
            setShowReceiptDialog(false);
        }
    };

    const handleRestore = async (ids: string[]) => {
        setIsStatusLoading(ids[0]);
        try {
            await restore(ids);
            FlashMessages.success('Expense Restored');
            await onGetExpenses();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Restore Expense');
        } finally {
            setIsStatusLoading(false);
        }
    };

    const handleActionClick = (item: MenuItemType | undefined, expense: ExpenseDto) => {
        if (!item) {
            return;
        }

        switch (item.key) {
            case ExpenseActions.VIEW:
                setSelectedExpense(expense);
                setShowDetailsModal(true);
                break;
            case ExpenseActions.EDIT:
                setSelectedExpense(expense);
                setShowEditModal(true);
                break;
            case ExpenseActions.ARCHIVE:
                setSelectedExpense(expense);
                setShowArchiveDialog(true);
                break;
            case ExpenseActions.REQUEST_RECEIPT:
                setSelectedExpense(expense);
                setShowReceiptDialog(true);
                break;
            case ExpenseActions.RESTORE:
                handleRestore([expense.id]);
                break;
        }
    };

    const handleDeclineDialogOk = async () => {
        try {
            if (!selectedExpense) {
                throw new Error('No expense selected');
            }
            setIsStatusLoading(true);
            await handleUpdateStatus(RawExpenseStatus.AUDITED, selectedExpense);
        } catch (err: any) {
            console.error(err.message);
        } finally {
            setSelectedExpense(undefined);
            setShowDeclineDialog(false);
            setIsStatusLoading(false);
        }
    };

    const handleArchiveDialogOk = async () => {
        try {
            if (!selectedExpense) {
                throw new Error('No expense selected');
            }
            setIsStatusLoading(true);
            await handleArchive([selectedExpense.id]);
        } catch (err: any) {
            console.error(err.message);
        } finally {
            setIsStatusLoading(false);
            setSelectedExpense(undefined);
            setShowArchiveDialog(false);
        }
    };

    const handleExpenseStatusChange = (newStatus: RawExpenseStatus, expense: ExpenseDto) => {
        setSelectedExpense(expense);
        if (newStatus === RawExpenseStatus.SUBMITTED) {
            handleUpdateStatus(newStatus, expense);
            return;
        }

        setSelectedStatus(newStatus);
        setShowChangeExpenseStatusModal(true);
    };

    const handleUpdateStatus = async (newStatus: RawExpenseStatus, expense: ExpenseDto) => {
        setIsStatusLoading(expense.id);
        try {
            await update({ id: expense.id, dto: { status: newStatus } });
            FlashMessages.success('Expense Status Changed');
            await onGetExpenses();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Change Expense Status');
        } finally {
            setIsStatusLoading(false);
        }
    };

    return {
        handleRequestReceipt,
        handleExpenseStatusChange,
        showReceiptDialog,
        selectedStatus,
        setShowReceiptDialog,
        receiptDialogLoading,
        selectedExpense,
        setSelectedExpense,
        showDetailsModal,
        setShowDetailsModal: (show: boolean) => {
            setShowDetailsModal(show);
            !show && setSelectedExpense(undefined);
        },
        showEditModal,
        setShowEditModal: (show: boolean) => {
            setShowEditModal(show);
            !show && setSelectedExpense(undefined);
        },
        onActionClick: handleActionClick,
        onExpenseStatusChange: handleExpenseStatusChange,
        isStatusLoading,
        showDeclineDialog,
        onDeclineDialogCancel: () => setShowDeclineDialog(false),
        onDeclineDialogOk: handleDeclineDialogOk,
        showChangeExpenseStatusModal,
        onChangeExpenseStatusClose: () => {
            setSelectedExpense(undefined);
            setShowChangeExpenseStatusModal(false);
            setSelectedStatus(undefined);
        },
        onChangeExpenseStatusSuccess: () => {
            onGetExpenses();
        },
        showArchiveDialog,
        onArchiveDialogCancel: () => setShowArchiveDialog(false),
        onArchiveDialogOk: handleArchiveDialogOk,
    };
};
export default useExpenseActions;
