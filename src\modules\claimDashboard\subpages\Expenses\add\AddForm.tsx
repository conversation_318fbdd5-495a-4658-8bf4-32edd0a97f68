import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Row } from 'antd';
import { plainToInstance } from 'class-transformer';
import classNames from 'classnames';
import React, { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../../store';
import { ClaimDto } from '../../../../../store/claim/dto/ClaimDto';
import { ExpenseAddDto } from '../../../../../store/expense/dto/ExpenseDto';
import {
    convertLocalDateToUTC,
    DATE_AT_TIME_MASK,
    DATE_AT_TIME_PLACEHOLDER,
    DATE_TIME_DISPLAY_FORMAT,
    isValidDateString,
} from '../../../../../utils/dateFormat';
import FlashMessages from '../../../../app/FlashMessages';
import ProgressBar from '../../../../app/shared/ProgressBar';
import Form from '../../../../app/shared/form/Form';
import SelectBox from '../../../../app/shared/form/SelectBox';
import TextField from '../../../../app/shared/form/TextField';
import { AddExpenseCategoryOptions, ExpenseAddEntry } from '../utils';
import RcDatePicker from '../../../../app/shared/form/DatePicker';
import { endOfToday, isAfter, isBefore } from 'date-fns';
import InputMask from 'react-input-mask';

interface Props {
    onBack: VoidFunction;
    onNext: VoidFunction;
    onSuccess: (dto: ExpenseAddDto) => void;
    onFinish: VoidFunction;
    initialData: ExpenseAddEntry;
    step: number;
    totalSteps: number;
    setIsLoading: (string) => void;
    claim: ClaimDto;
}

const minDate = new Date(1970, 1, 1);

const getProperInitialDate = (date: string) => {
    if (
        !isValidDateString(date) ||
        isBefore(new Date(date), minDate) ||
        isAfter(new Date(date), new Date())
    ) {
        return undefined;
    }
    return new Date(date);
};

const AddForm: React.FC<Props> = ({
    onBack,
    onNext,
    onSuccess,
    onFinish,
    initialData,
    step,
    totalSteps,
    setIsLoading,
    claim,
}) => {
    const { add } = useStoreActions((actions) => actions.expense);

    const methods = useForm<ExpenseAddDto>({
        resolver: classValidatorResolver(ExpenseAddDto),
        mode: 'onChange',
    });

    const isLastStep = useMemo(() => step === totalSteps - 1, [step, totalSteps]);

    useEffect(() => {
        methods.reset(
            {
                ...initialData.data,
                submitted_amount: parseFloat(initialData.data?.submitted_amount || '0').toFixed(2),
                notes: initialData.data.notes || '',
                date: getProperInitialDate(initialData.data.date),
            },
            { keepErrors: false },
        );
    }, [claim.lossDate, initialData, methods]);

    const onSubmit = async (fields) => {
        fields.date = convertLocalDateToUTC(fields.date, claim.timezone).toISOString();
        fields.submitted_amount = fields.submitted_amount.toString();

        const dto = plainToInstance(ExpenseAddDto, fields);

        if (initialData.added) {
            onNext();
            return;
        }

        setIsLoading('Saving Expense');
        try {
            await add(dto);
            methods.reset({}, { keepErrors: false });

            isLastStep ? onFinish() : onSuccess(dto);
        } catch (err: any) {
            err.response.data.message
                ? FlashMessages.error(err.response.data.message)
                : FlashMessages.error('Failed to Add Expense');
            setIsLoading(undefined);
        } finally {
            setIsLoading(undefined);
        }
    };

    return (
        <Form
            className="claim-expenses-add-form"
            methods={methods}
            onSubmit={methods.handleSubmit(onSubmit)}
        >
            <div className="flex-space margin-bottom-24">
                <div className="receipt">Receipt</div>
                <div className="count">
                    {step + 1}/{totalSteps}
                </div>
            </div>
            <TextField label="Vendor Name" name="vendor" placeholder="Vendor" />
            <SelectBox
                label="Category"
                name="category"
                placeholder={initialData.data.category}
                options={AddExpenseCategoryOptions}
            />
            <TextField label="Location" name="location" placeholder="Location" />
            <RcDatePicker
                name="date"
                label="Expense Date"
                maxDate={endOfToday()}
                showTimeSelect={true}
                minDate={minDate}
                dateFormat={DATE_TIME_DISPLAY_FORMAT}
                placeholder={DATE_AT_TIME_PLACEHOLDER}
                customInput={
                    <InputMask
                        mask={DATE_AT_TIME_MASK}
                        maskPlaceholder={DATE_AT_TIME_PLACEHOLDER}
                    />
                }
            />
            <TextField
                label="Amount"
                name="submitted_amount"
                type="number"
                step={0.01}
                placeholder="Insert Expense Amount"
                contentBefore="$"
            />
            <TextField label="Note" name="notes" placeholder="Insert Expense Note" />
            <ProgressBar className="margin-bottom-24" percent={((step + 1) / totalSteps) * 100} />
            <Row gutter={[16, 0]}>
                <Col span={isLastStep ? 10 : 12}>
                    <Button
                        size="large"
                        type="link"
                        onClick={() => {
                            methods.reset();
                            onBack();
                        }}
                        disabled={methods.formState.isSubmitting}
                        block
                    >
                        Back
                    </Button>
                </Col>
                <Col span={isLastStep ? 14 : 12}>
                    <Button
                        htmlType="submit"
                        size="large"
                        className={classNames('btn btn-primary', {
                            // disabled: !!Object.keys(methods.formState.errors).length,
                            disabled: !methods.formState.isValid,
                        })}
                        loading={methods.formState.isSubmitting}
                        // disabled={!!Object.keys(methods.formState.errors).length}
                        disabled={!methods.formState.isValid}
                        block
                    >
                        {isLastStep ? 'Save Expenses' : 'Next'}
                    </Button>
                </Col>
            </Row>
        </Form>
    );
};
export default AddForm;
