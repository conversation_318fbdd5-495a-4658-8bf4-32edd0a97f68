import { <PERSON><PERSON><PERSON>y, Is<PERSON>num, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import PaginationDto from '../../../common/PaginationDto';
import { RawExpenseStatus } from '../../expense/types/Expense';
import { InvoiceFilterParam, type InvoiceStatus, type InvoiceType } from '../type/Invoice';
import { SelectBoxOption } from '../../../modules/app/shared/form/MultiSelectBox';
import { ExpenseCategory } from '../../../types';

export class InvoiceDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    claimId!: string;

    @IsNotEmpty()
    @IsString()
    startDate!: string;

    @IsNotEmpty()
    @IsString()
    endDate!: string;

    @IsNotEmpty()
    expensesCount!: any;

    @IsOptional()
    no_of_months_billed!: any;

    @IsOptional()
    no_of_limited_months_billed!: any;

    @IsOptional()
    invoice_no!: any;

    @IsOptional()
    no_of_unlimited_months_billed!: any;

    @IsOptional()
    ready_for_download_at!: any;

    @IsOptional()
    invoice_amount!: any;

    @IsNotEmpty()
    type!: InvoiceType;

    @IsNotEmpty()
    @IsString()
    status!: InvoiceStatus;

    @IsOptional()
    @IsNumber()
    submitted?: number;

    @IsOptional()
    @IsNumber()
    audited?: number;

    @IsOptional()
    @IsNumber()
    costSavings?: number;

    @IsOptional()
    @IsNumber()
    recommendedReimbursement?: number;

    @IsOptional()
    @IsNumber()
    inReview?: number;

    @IsOptional()
    @IsNumber()
    approved?: number;

    @IsOptional()
    @IsNumber()
    declined?: number;

    @IsNotEmpty()
    @IsArray()
    categories!: ExpenseCategory[];

    html?: string;

    @IsNotEmpty()
    @IsString()
    invoice_url!: string;

    @IsNotEmpty()
    @IsString()
    metadata!: InvoiceMetadataDto;
}

export class InvoiceMetadataDto {
    @IsOptional()
    @IsString()
    cost_savings?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_audited_amount?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_audited_count?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_in_review_amount?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_in_review_count?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_submitted_count?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_submitted_amount?: number;

    @IsOptional()
    @IsString()
    recommended_reimbursement?: number;
}

export class InvoiceCreateFormDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    dateRange!: [any, any];

    @IsOptional()
    @IsArray()
    status?: RawExpenseStatus[];

    @IsOptional()
    @IsArray()
    category?: ExpenseCategory[];
}
export class InvoiceRequestFormDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    dateRange!: [any, any];

    @IsOptional()
    @IsArray()
    status?: RawExpenseStatus[];

    @IsOptional()
    @IsArray()
    category?: ExpenseCategory[];

    @IsNotEmpty()
    approved_receipt_end_date!: any;

    @IsNotEmpty()
    approved_receipt_start_date!: any;

    @IsOptional()
    @IsString()
    previous_invoice_sent!: string;

    @IsOptional()
    @IsString()
    additional_comments!: string;
}

export class InvoiceCreateDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    @IsString()
    start_date!: string;

    @IsNotEmpty()
    @IsString()
    end_date!: string;

    @IsNotEmpty()
    @IsArray()
    status!: RawExpenseStatus[];

    @IsNotEmpty()
    @IsEnum(ExpenseCategory)
    category!: ExpenseCategory;
}

export class InvoicePaginateDto extends PaginationDto {
    @IsOptional()
    params?: InvoiceFilterParam[];
}

export class InvoiceFilterDto {
    @IsOptional()
    @IsArray()
    type?: string;

    @IsOptional()
    @IsArray()
    status?: SelectBoxOption[];

    @IsOptional()
    dateRange?: [any, any];
}

export class InvoiceApproveDto {
    @IsOptional()
    @IsString()
    note?: string;

    @IsOptional()
    @IsString()
    status!: InvoiceStatus;
}

export class InvoiceRejectDto {
    @IsNotEmpty()
    @IsString()
    note!: string;

    @IsOptional()
    @IsString()
    status!: InvoiceStatus;
}
