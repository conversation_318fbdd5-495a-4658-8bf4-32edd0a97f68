import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString } from 'class-validator';
import { NotificationType, type NotificationData } from '../types/Notifications';
import PaginationDto from '../../../common/PaginationDto';

export class NotificationDto {
    @IsOptional()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsObject()
    data!: NotificationData;

    @IsNotEmpty()
    @IsEnum(NotificationType)
    type!: NotificationType;

    @IsOptional()
    readAt?: Date;

    @IsNotEmpty()
    createdAt!: Date;
}

export class NotificationPaginateDto extends PaginationDto {}
