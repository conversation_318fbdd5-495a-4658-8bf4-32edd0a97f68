.btn {
    &.disabled {
        background-color: #f0f3f8;

        &:hover {
            background-color: $color-gray-6 !important;
        }
    }

    &:hover {
        color: $color-text !important;
    }

    &-ghost {
        background: none;
        border: 1px solid $color-gray-4;
        color: $color-gray-3 !important;

        &-bg {
            background-color: $color-background-secondary !important;

            &:hover {
                background-color: $color-background-secondary !important;
            }
        }

        &:hover {
            border: 1px solid $color-gray-4 !important;
            opacity: 0.6;
        }
    }

    &-text {
        cursor: pointer;
        background: none;
        border: none;
        font-size: 16px;
        font-weight: $font-weight-medium;
        line-height: 18px;
        padding: 0;
        text-transform: capitalize;
    }

    &-primary {
        background-color: $color-primary;
        border: none;

        &:hover {
            background-color: #f1c40f !important;
        }

        &.disabled {
            &:hover {
                background-color: $color-gray-6 !important;
            }
        }

        &.outline {
            background: none;
            border: 1px solid $color-primary;
            color: $color-primary !important;

            &:hover {
                background: none !important;
                border-color: #f1c40f !important;
                color: #f1c40f !important;
            }
        }
    }

    &-secondary {
        background-color: $color-secondary-transparent;
        border: none;

        &.outline {
            background: none;
            border: 1px solid $color-secondary;
            color: $color-secondary !important;

            &:hover {
                background: $color-secondary-transparent;
                border: 1px solid rgba(0, 0, 0, 0) !important;
                color: $color-text !important;
            }
        }
    }

    &-tab {
        font-weight: $font-weight-medium !important;
        padding: 8px 16px !important;
    }
}
