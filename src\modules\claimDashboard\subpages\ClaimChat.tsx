import { Navigate } from 'react-router-dom';
import { canAccess } from '../../../common/canAccess';
import { UserRole } from '../../../store/user/types/User';
import Chat from '../../chat/Chat';
import { useChat } from '../../chat';
import { useEffect } from 'react';

const ClaimChat: React.FC = () => {
    const { messageListRef } = useChat();

    useEffect(() => {
        if (!messageListRef.current) {
            return;
        }

        messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }, [messageListRef]);

    if (!canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])) {
        return <Navigate to="/" />;
    }

    return (
        <div className="chat-container">
            <Chat />
        </div>
    );
};

export default ClaimChat;
