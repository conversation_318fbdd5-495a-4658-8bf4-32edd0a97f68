import { Layout } from 'antd';
import MainHeader from './MainHeader';

const { Header: AntHeader } = Layout;

interface Props {
    SubHeaderComponent?: React.ReactNode;
}

const Header: React.FC<Props> = ({ SubHeaderComponent = null }) => {
    return (
        <AntHeader className="header">
            <MainHeader />
            {SubHeaderComponent}
        </AntHeader>
    );
};

export default Header;
