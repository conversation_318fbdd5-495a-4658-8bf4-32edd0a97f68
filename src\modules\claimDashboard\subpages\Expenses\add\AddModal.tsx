import { Button } from 'antd';
import { plainToInstance } from 'class-transformer';
import { useEffect, useMemo, useState } from 'react';
import { useStoreActions } from '../../../../../store';
import { ClaimDto } from '../../../../../store/claim/dto/ClaimDto';
import { DocumentDto } from '../../../../../store/documents/dto/DocumentDto';
import { DocumentPathTypes } from '../../../../../store/documents/types/Documents';
import { ExpenseAddDto } from '../../../../../store/expense/dto/ExpenseDto';
import FlashMessages from '../../../../app/FlashMessages';
import Dropzone from '../../../../app/shared/Dropzone';
import Modal from '../../../../app/shared/Modal';
import Spinner from '../../../../app/shared/Spinner';
import TransformContainer from '../../../../app/shared/TransformContainer';
import AddForm from './AddForm';
import UploadList from './UploadList';
import { AnalyzeError, FileUpload } from './types';
import classNames from 'classnames';
import { ExpenseAddEntry, MAX_FILE_UPLOADS, UploadStatus } from '../utils';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
    claim: ClaimDto;
}

const AddModal: React.FC<Props> = ({ show, onClose, claim, onSuccess }) => {
    const [isUploadDisabled, setIsUpladDisabled] = useState(false);
    const [fileUploads, setFileUploads] = useState<Array<FileUpload>>([]);
    const [isLoading, setIsLoading] = useState<string | undefined>();
    const [expenseDtos, setExpenseDtos] = useState<ExpenseAddEntry[]>([]);
    const [uploadStatus, setUploadStatus] = useState<UploadStatus>('idle');
    const [isUploadStep, setIsUploadStep] = useState(true);

    const [analyzeErrors, setAnalyzeErrors] = useState<AnalyzeError[]>([]);

    const totalSteps = useMemo(() => expenseDtos.length, [expenseDtos]);
    const [currentStep, setCurrentStep] = useState(0);
    const currentExpense: ExpenseAddEntry | undefined = useMemo(
        () => expenseDtos[currentStep],
        [currentStep, expenseDtos],
    );

    const { uploadMultipeToS3, getDocuments, getDocumentAnalyze, resetUploadMultipleProgress } =
        useStoreActions((actions) => actions.documents);

    const handleFileChange = async (files: File[]) => {
        if (!files.length) {
            FlashMessages.warn('No images for upload');
            return;
        }

        setIsUpladDisabled(true);
        setUploadStatus('inProgress');

        try {
            const preparedDocuments: DocumentDto[] = await getDocuments({
                path: DocumentPathTypes.DOCUMENTS,
                count: files.length,
            });

            const newUploads = preparedDocuments.map((doc, i) => ({
                ...doc,
                file: files[i],
            }));

            setFileUploads([...fileUploads, ...newUploads]);

            if (newUploads.length) {
                setUploadStatus('ready');
            }
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to prepare images');
            setUploadStatus('idle');
        } finally {
            setIsUpladDisabled(false);
        }
    };

    const handleUpload = async (fileUploads: FileUpload[]) => {
        setIsUpladDisabled(true);

        try {
            await uploadMultipeToS3(fileUploads);

            FlashMessages.success('Images Uploaded');

            setIsLoading('Analyzing Receipts');
            const analyzeErrors: AnalyzeError[] = [];
            const analyzePromises = fileUploads.map(async (upload) => {
                const data = await getDocumentAnalyze(upload.id);
                if (!data) {
                    analyzeErrors.push({
                        id: upload.id,
                        presignedUrl: upload.presignedUrl,
                        error: 'error',
                    });
                }

                return plainToInstance(ExpenseAddDto, {
                    ...data,
                    claim_id: claim.id,
                    document_id: upload.id,
                });
            });
            const dtos = await Promise.all(analyzePromises).then((dtos) =>
                dtos.map((dto) => ({ data: dto } as ExpenseAddEntry)),
            );
            setAnalyzeErrors(analyzeErrors);
            setExpenseDtos(dtos);
            if (analyzeErrors.length) {
                FlashMessages.warn('Failed to Analyze Some Receipts');
            } else {
                FlashMessages.success('Receipt Data Analyzed');
            }
            setUploadStatus('completed');
        } catch (err: any) {
            console.error(err.message);
            setFileUploads([]);
            resetUploadMultipleProgress();
            FlashMessages.error('Failed to upload receipts');
        } finally {
            setIsLoading(undefined);
        }
    };

    const handleRemoveImage = (url: string) => {
        setFileUploads((uploads) => uploads.filter((upload) => upload.presignedUrl !== url));
    };

    useEffect(() => {
        if (fileUploads.length === 0) {
            setUploadStatus('idle');
            setIsUpladDisabled(false);
        }
    }, [fileUploads.length]);

    const handleAddExpenseSuccess = (newExpense: ExpenseAddDto) => {
        FlashMessages.success('Expense Saved');
        const newDtos = expenseDtos.map((entry, index) =>
            index === currentStep ? { added: true, data: newExpense } : entry,
        );
        setExpenseDtos(newDtos);
        setCurrentStep(currentStep + 1);
    };

    const handleFinishAddExpenses = () => {
        FlashMessages.success('All Expenses Saved');
        onSuccess();
    };

    const handleContinue = (status: UploadStatus | undefined) => {
        switch (status) {
            case 'idle':
            case 'inProgress':
                return;
            case 'ready':
                handleUpload(fileUploads);
                setUploadStatus('inProgress');
                return;
            case 'completed':
                setIsUploadStep(false);
                return;
        }
    };

    const continueDisabled = useMemo(() => {
        if (uploadStatus === 'completed' || uploadStatus === 'ready') {
            return false;
        } else if (uploadStatus === 'inProgress' || uploadStatus === 'idle') {
            return true;
        }

        return true;
    }, [uploadStatus]);

    const handleBackStep = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        } else {
            setIsUploadStep(true);
        }
    };

    return (
        <Modal
            show={show}
            onClose={onClose}
            title="New Expenses"
            subtitle={`When bulk uploading expenses, make sure that OCR scans are correct. If there are necessary changes, you can edit the individual expense from the Expenses page. Click "Next" to continue reviewing.`}
            className="claim-expenses-add"
            sideContent={
                !isUploadStep ? (
                    <ReceiptPreview
                        photoUrl={
                            !isLoading
                                ? fileUploads.find(
                                      (upload) => upload.id === currentExpense.data.document_id,
                                  )?.imagePath
                                : undefined
                        }
                    />
                ) : undefined
            }
            sideContentProps={{ size: 'large' }}
            isLoading={!!isLoading}
            loadingMessage={isLoading}
        >
            {isUploadStep && (
                <>
                    <Dropzone
                        maxFileNo={MAX_FILE_UPLOADS}
                        className="margin-bottom-16"
                        onChange={handleFileChange}
                        disabled={
                            isUploadDisabled ||
                            (uploadStatus !== 'idle' && uploadStatus !== 'ready')
                        }
                    />
                    <UploadList
                        errors={analyzeErrors}
                        fileUploads={fileUploads}
                        onRemoveImage={(url) => handleRemoveImage(url)}
                    />
                    <Button
                        className={classNames('btn-primary', {
                            disabled: continueDisabled,
                        })}
                        size="large"
                        disabled={continueDisabled}
                        onClick={() => handleContinue(uploadStatus)}
                    >
                        Continue
                    </Button>
                </>
            )}
            {!isUploadStep && currentExpense && (
                <AddForm
                    onNext={() => setCurrentStep((currentStep) => currentStep + 1)}
                    onBack={() => handleBackStep()}
                    claim={claim}
                    setIsLoading={setIsLoading}
                    step={currentStep}
                    totalSteps={totalSteps}
                    initialData={currentExpense}
                    onSuccess={handleAddExpenseSuccess}
                    onFinish={handleFinishAddExpenses}
                />
            )}
        </Modal>
    );
};
export default AddModal;

interface ReceiptPreviewProps {
    photoUrl?: string;
    onRequestReceipt?: VoidFunction;
}

const ReceiptPreview: React.FC<ReceiptPreviewProps> = ({ photoUrl }) => {
    return (
        <TransformContainer>
            {!photoUrl && <Spinner type="skeleton" />}
            {photoUrl && <img className="responsive-imge" src={photoUrl} alt="Receipt" />}
        </TransformContainer>
    );
};
