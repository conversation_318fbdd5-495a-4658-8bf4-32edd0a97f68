import { Button, Space } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { ReactComponent as IconAdd } from '../../assets/icons/add-sharp.svg';
import { useStoreActions, useStoreState } from '../../store';
import PolicyHolderDto from '../../store/policyHolder/dto/PolicyHolderDto';
import UserDto from '../../store/user/dto/UserDto';
import { PaginationDirection } from '../../utils/paginate';
import FlashMessages from '../app/FlashMessages';
import Dialog from '../app/shared/Dialog';
import Pagination from '../app/shared/Pagination';
import Search from '../app/shared/Search';
import AddUserModal from './AddUserModal';
import EditUserModal from './EditUserModal';
import PolicyholderDetailsModal from './PolicyholderDetailsModal';
import PolicyholderList from './PolicyholderList';
import UserDetailsModal from './UserDetailsModal';
import UserList from './UserList';
import { UserActions, UserFilter, getFilterParams } from './utils';
import classNames from 'classnames';

const Users: React.FC = () => {
    const params = useParams();
    const navigate = useNavigate();
    const location = useLocation();
    const isNewUser = location.pathname.includes('/new');

    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [searchTerm, setSearchTerm] = useState('');
    const [showAddUserModal, setShowAddUserModal] = useState(isNewUser);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [deleteDialogDisabled, setDeleteDialogDisabled] = useState(false);
    const [showPolicyholderModal, setShowpolicyholderModal] = useState(false);
    const [userFilter, setUserFilter] = useState<UserFilter>('Admins');
    const [selectedUser, setSelectedUser] = useState<UserDto | PolicyHolderDto>();

    const { list: users, pagination: userPagination } = useStoreState((state) => state.user);
    const {
        get: getUsers,
        setPagination: setUserPagination,
        deleteUser,
    } = useStoreActions((actions) => actions.user);
    const { list: policyholders, pagination: policyholderPagination } = useStoreState(
        (state) => state.policyholder,
    );
    const { get: getPolicyholders, setPagination: setPolicyholderPagination } = useStoreActions(
        (actions) => actions.policyholder,
    );

    const handleGetUsers = useCallback(
        (page = 1, search = '') => {
            if (userFilter !== 'Policyholders') {
                getUsers({
                    search_term: search,
                    page: page,
                    order_by: 'name',
                    direction: PaginationDirection.ASC,
                    params: getFilterParams(userFilter),
                });
            } else {
                getPolicyholders({
                    search_term: search,
                    page: page,
                    order_by: 'full_name',
                    direction: PaginationDirection.ASC,
                });
            }
        },
        [getPolicyholders, getUsers, userFilter],
    );

    useEffect(() => {
        handleGetUsers(currentPage, searchTerm);
    }, [getUsers, currentPage, searchTerm, userFilter, handleGetUsers]);

    const handleFilterChange = (filter: UserFilter) => {
        setSearchTerm('');
        setUserPagination(null);
        setPolicyholderPagination(null);
        setCurrentPage(1);
        navigate('/settings/users');
        setUserFilter(filter);
    };

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);
        navigate(`/settings/users/page/${page}`);
    };

    const handleUserActionClick = (
        item: MenuItemType | undefined,
        user: UserDto | PolicyHolderDto,
    ) => {
        if (!item) {
            return;
        }

        setSelectedUser(user);

        switch (item.key) {
            case UserActions.VIEW_USER:
                setShowDetailsModal(true);
                break;
            case UserActions.VIEW_HOLDER:
                setShowpolicyholderModal(true);
                break;
            case UserActions.EDIT:
                setShowEditModal(true);
                break;
            case UserActions.DELETE:
                setShowDeleteDialog(true);
                break;
        }
    };

    const handleDelete = async () => {
        if (!selectedUser) {
            return;
        }

        setDeleteDialogDisabled(true);
        try {
            await deleteUser(selectedUser?.id);
            await handleGetUsers(currentPage, searchTerm);
            FlashMessages.success('User deleted');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to delete user');
        } finally {
            setShowDeleteDialog(false);
            if (showDetailsModal) {
                setShowDetailsModal(false);
            }
            setSelectedUser(undefined);
            setDeleteDialogDisabled(false);
        }
    };

    const handleAddSuccess = async () => {
        FlashMessages.success('User added');
        await handleGetUsers(currentPage, searchTerm);
        setShowAddUserModal(false);
    };

    const handleEditSuccess = async () => {
        setSelectedUser(undefined);
        FlashMessages.success('User edited');
        await handleGetUsers(currentPage, searchTerm);
        setShowEditModal(false);
    };

    const onPolicyholderToggleSuccess = async () => {
        await handleGetUsers(currentPage, searchTerm);
        setShowpolicyholderModal(false);
    };

    const handleSearch = (searchTerm: string) => {
        setCurrentPage(1);
        setSearchTerm(searchTerm);
        navigate('/settings/users');
    };
    return (
        <>
            <div className="box settings-page-main">
                <div className="flex-space">
                    <div className="info">
                        <div className="info-title">Users</div>
                        <div className="info-subtitle">
                            View user lists by role: Admins, Adjusters, Policyholders
                        </div>
                    </div>
                    <Button
                        size="large"
                        className="btn-primary"
                        onClick={() => setShowAddUserModal(true)}
                    >
                        <Space>
                            <IconAdd />
                            <span>Add New User</span>
                        </Space>
                    </Button>
                </div>

                <div className="flex-start">
                    <Button
                        type={userFilter === 'Admins' ? 'primary' : 'link'}
                        onClick={() => handleFilterChange('Admins')}
                        className={classNames('margin-right-8', {
                            'btn-secondary btn-tab': userFilter === 'Admins',
                        })}
                    >
                        <b> Admins</b>
                    </Button>
                    <Button
                        type={userFilter === 'Adjusters' ? 'primary' : 'link'}
                        onClick={() => handleFilterChange('Adjusters')}
                        className={classNames('margin-right-8', {
                            'btn-secondary btn-tab': userFilter === 'Adjusters',
                        })}
                    >
                        <b> Adjusters</b>
                    </Button>
                    <Button
                        className={
                            userFilter === 'Policyholders' ? 'btn-secondary btn-tab' : undefined
                        }
                        type={userFilter === 'Policyholders' ? 'primary' : 'link'}
                        onClick={() => handleFilterChange('Policyholders')}
                    >
                        <b> Policyholders</b>
                    </Button>
                </div>

                <hr />

                <Search
                    value={searchTerm}
                    onSearch={handleSearch}
                    className="settings-page-search"
                />

                {userFilter !== 'Policyholders' && (
                    <div>
                        <UserList
                            userFilter={userFilter}
                            list={users}
                            handleActionClick={handleUserActionClick}
                        />
                        {userPagination && (
                            <Pagination {...userPagination} onChange={handlePaginationChange} />
                        )}
                    </div>
                )}
                {userFilter === 'Policyholders' && (
                    <div>
                        <PolicyholderList
                            list={policyholders}
                            handleActionClick={handleUserActionClick}
                        />
                        {policyholderPagination && (
                            <Pagination
                                {...policyholderPagination}
                                onChange={handlePaginationChange}
                            />
                        )}
                    </div>
                )}
            </div>
            {showAddUserModal && (
                <AddUserModal
                    show={showAddUserModal}
                    onSuccess={() => handleAddSuccess()}
                    onClose={() => setShowAddUserModal(false)}
                />
            )}
            {showDetailsModal && selectedUser && (
                <UserDetailsModal
                    show={showDetailsModal}
                    onEditSuccess={() => handleEditSuccess()}
                    handleDelete={() => handleDelete()}
                    onClose={() => setShowDetailsModal(false)}
                    user={selectedUser as UserDto}
                    deleteDialogDisabled={deleteDialogDisabled}
                />
            )}
            {showEditModal && selectedUser && (
                <EditUserModal
                    show={showEditModal}
                    onSuccess={() => handleEditSuccess()}
                    onClose={() => setShowEditModal(false)}
                    user={selectedUser as UserDto}
                />
            )}
            {showPolicyholderModal && selectedUser && (
                <PolicyholderDetailsModal
                    show={showPolicyholderModal}
                    onClose={() => setShowpolicyholderModal(false)}
                    onToggleStatusSuccess={onPolicyholderToggleSuccess}
                    policyholder={selectedUser as PolicyHolderDto}
                />
            )}
            <Dialog
                onCancel={() => setShowDeleteDialog(false)}
                onOk={() => handleDelete()}
                show={showDeleteDialog}
                disabled={deleteDialogDisabled}
            />
        </>
    );
};
export default Users;
