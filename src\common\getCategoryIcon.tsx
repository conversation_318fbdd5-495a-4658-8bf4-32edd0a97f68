import React from 'react';
import { ExpenseCategory } from '../types/index';
import { ReactComponent as IconCar } from '../assets/icons/icon-car.svg';
import { ReactComponent as IconHouse } from '../assets/icons/icon-home.svg';
import { ReactComponent as IconLightbulb } from '../assets/icons/icon-lightbulb.svg';
import { ReactComponent as IconMeal } from '../assets/icons/restaurant-outline.svg';
import { ReactComponent as IconWallet } from '../assets/icons/icon-wallet.svg';

export enum IconSize {
    TINY = 'icon-tiny',
    SMALL = 'icon-small',
    REGULAR = 'icon-regular',
}

export const getCategoryIcon = (
    category = ExpenseCategory.UTILITIES,
    size = IconSize.REGULAR,
): React.ReactNode => {
    if (category === ExpenseCategory.FOOD_AND_MEALS) {
        return <IconMeal className={size} />;
    }
    if (category === ExpenseCategory.HOUSING_AND_HOTELS) {
        return <IconHouse className={size} />;
    }
    if (category === ExpenseCategory.MISCELLANEOUS) {
        return <IconWallet className={size} />;
    }
    if (category === ExpenseCategory.TRANSPORTATION) {
        return <IconCar className={size} />;
    }
    return <IconLightbulb className={size} />;
};
