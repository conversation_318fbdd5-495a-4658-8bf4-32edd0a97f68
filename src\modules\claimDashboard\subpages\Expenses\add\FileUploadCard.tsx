import classNames from 'classnames';
import { ReactComponent as IconClose } from '../../../../../assets/icons/close.svg';
import { formatFileSize } from '../../../../../utils/filesizeFormat';
import ProgressBar from '../../../../app/shared/ProgressBar';

interface Props {
    presignedUrl: string;
    file: File;
    progress: number;
    onRemove: (presignedUrl: string) => void;
    error?: boolean;
}

const FileUploadCard: React.FC<Props> = ({ presignedUrl, file, progress, onRemove, error }) => (
    <div className={classNames('upload-file-card margin-bottom-8', { error: !!error })}>
        <div className="flex-space">
            <span className="filename">{file.name}</span>
            <IconClose className="cursor" onClick={() => onRemove(presignedUrl)} />
        </div>
        <div className="flex-space ">
            <span className="filesize">{formatFileSize(file.size)}</span>
            {progress && <span className="progress">{Math.ceil(progress)} %</span>}
        </div>
        <ProgressBar barClassName="upload-progress-bar" percent={progress} />
    </div>
);
export default FileUploadCard;
