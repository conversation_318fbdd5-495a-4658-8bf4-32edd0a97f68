import { action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapReport, mapReports } from './mappers';
import ReportModel from './type/ReportModel';

const reportModel: ReportModel = {
    // state
    list: transition.reset([]),
    pagination: null,

    // actions
    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    load: action((state, reports) => {
        state.list = transition.loaded(reports);
    }),

    setPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    // thunks
    get: thunk(async (actions, dto) => {
        actions.loading();
        const {
            data: { data, meta },
        } = await api.get('/reports', {
            params: dto,
        });

        actions.load(mapReports(data));
        actions.setPagination(mapPagination(meta));
    }),

    getReport: thunk(async (actions, reportId) => {
        const {
            data: { data },
        } = await api.get(`/reports/${reportId}`);

        return mapReport(data);
    }),

    getReportRequest: thunk(async (actions, dto) => {
        const response = await api.post('/reports/request-report', dto);
        if (!response.data.data) {
            return null;
        }
        return mapReport(response.data.data);
    }),

    getReportPreview: thunk(async (actions, dto) => {
        const response = await api.post('/reports/preview', dto);
        if (!response.data.data) {
            return null;
        }
        return mapReport(response.data.data);
    }),

    add: thunk(async (actions, dto) => {
        const res = await api.post('/reports', dto);
        return res.data.data ? mapReport(res.data.data) : null;
    }),

    delete: thunk(async (actions, reportId) => {
        await api.delete(`/reports/${reportId}`);
    }),

    checkIfPdfReady: thunk(async (actions, reportId) => {
        const res = await api.get(`/reports/${reportId}/ready-for-download`);
        return res.data.data;
    }),

    getPDF: thunk(async (actions, reportId) => {
        const res = await api.get(`/reports/${reportId}/pdf`);
        return res.data.data.url;
    }),

    getCSV: thunk(async (actions, reportId) => {
        const res = await api.get(`/reports/${reportId}/csv`);
        return res.data;
    }),

    sendToAdjuster: thunk(async (actions, reportId) => {
        await api.post(`/reports/${reportId}/send-adjuster`);
    }),

    withdrawAnalytics: thunk(async (actions, reportId) => {
        await api.get(`/reports/${reportId}/withdrawal-analytics`);
    }),

    sendAnalytics: thunk(async (actions, reportId) => {
        await api.get(`/reports/${reportId}/send-analytics-salesforce`);
    }),

    approve: thunk(async (actions, { id: reportId, dto }) => {
        await api.post(`/reports/${reportId}`, dto);
    }),

    reject: thunk(async (actions, { id: reportId, dto }) => {
        await api.post(`/reports/${reportId}`, dto);
    }),
};
export default reportModel;
