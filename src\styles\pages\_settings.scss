.settings-page {
    hr {
        margin-top: 0;
        margin-bottom: 0;
    }

    &-main {
        @include flex(flex, column, nowrap, flex-start, stretch);
        gap: 24px;

        .info {
            &-title {
                font-size: 21px;
                font-weight: $font-weight-semi-bold;
                line-height: 24px;
                margin-bottom: 8px;
            }

            &-subtitle {
                color: $color-gray-3;
                font-size: 14px;
                font-weight: $font-weight-light;
                line-height: 20px;
            }
        }
    }

    &-modal {
        &-details {
            .header {
                padding-top: 24px;

                &-edit {
                    @include flex(flex, row, nowrap, space-between, center);
                    align-self: flex-start;
                    border: 1px solid $color-gray-3;
                    color: $color-gray-3;
                    font-size: 16px;
                    font-weight: $font-weight-semi-bold;
                    gap: 4px;
                    line-height: 20px;
                    padding: 8px 16px;
                }

                &-name {
                    font-size: 24px;
                    font-weight: $font-weight-bold;
                    line-height: 20px;
                }

                &-role {
                    color: $color-gray-2;
                    font-size: 14px;
                    font-weight: $font-weight-light;
                    line-height: 20px;
                }

                &-status {
                    font-size: 14px;
                    font-weight: $font-weight-regular;
                    line-height: 18px;
                    padding: 0px !important;
                    text-align: left;
                    text-transform: capitalize;
                }
            }

            .body {
                @include flex(flex, column, nowrap, flex-start, stretch);
                gap: 24px;

                .group {
                    .label {
                        font-size: 16px;
                        font-weight: $font-weight-semi-bold;
                        line-height: 20px;
                        margin-bottom: 8px;
                    }
                    .value {
                        font-size: 16px;
                        font-weight: $font-weight-light;
                        line-height: 20px;
                    }
                }

                hr {
                    margin-top: 0;
                    margin-bottom: 0;
                }

                .timestamps {
                    @include flex(flex, row, wrap, space-between, flex-start);
                    gap: 24px;

                    .timestamp {
                        @include flex(flex, column, nowrap, flex-start, flex-start);
                        color: $color-gray-2;
                        flex: 1;
                        font-size: 14px;
                        font-weight: 300;
                        gap: 8px;
                        line-height: 20px;
                    }
                }
            }
        }
    }

    &-search {
        max-width: 400px;
    }

    &-subheader {
        @include flex(flex, row, nowrap, space-between, center);

        .title {
            font-size: 24px;
            font-weight: $font-weight-bold;
            line-height: 48px;
            margin-right: 32px;
        }
    }
}
