type Transition<T> = {
    loading: boolean;
    loaded: boolean;
    value: T;
    pageCount?: number;
};

export type ResourcePagination = {
    currentPage: number;
    perPage: number;
    total: number;
};

export interface Resource<T> {
    value: T;
    pagination?: ResourcePagination;
    loading: boolean;
    loaded: boolean;
}

export const transition = {
    reset<T>(value: T, pagination?: ResourcePagination): Resource<T> {
        return {
            loading: false,
            loaded: false,
            value,
            pagination,
        };
    },
    loading<T>(value: T): Resource<T> {
        return {
            loading: true,
            loaded: false,
            value,
        };
    },
    loaded<T>(value: T, pagination?: ResourcePagination): Resource<T> {
        return {
            loading: false,
            loaded: true,
            value,
            pagination,
        };
    },
    clone<T, TResult>(transition: Transition<T>, value: TResult): Transition<TResult> {
        return {
            ...transition,
            value,
        };
    },
};
