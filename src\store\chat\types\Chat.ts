import { MessageDto } from '../dto/MessageDto';
import { SenderMetadataDto } from '../dto/MessageMetadataDto';

export enum ChatStatus {
    CONNECTING = 'Connecting',
    CONNECTED = 'Connected',
    RECONNECTING = 'Reconnection',
    STOPPED = 'Stopped',
}

export enum InitStatus {
    UNINITALIZED = 'UNINITALIZED',
    INITIALIZING = 'INITIALIZING',
    INITIALIZED = 'INITIALIZED',
}

export enum ChatMessageType {
    GROUP = 'GROUP',
    TIMESTAMP = 'TIMESTAMP',
}

export interface ChatMessage {
    type: ChatMessageType;
    id: string;
    data: ChatMessageGroupData | ChatMessageTimestampData;
    createdAt: string;
}

export interface ChatMessageGroupData {
    sender: SenderMetadataDto;
    messages: MessageDto[];
}

export type ChatMessageTimestampData = null;
