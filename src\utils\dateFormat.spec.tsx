import {
    API_DATE_TIME_FORMAT,
    convertLocalDateToUTC,
    convertUtcToSpecifiedTimeZone,
    createUTCDate,
} from './dateFormat';
import { format } from 'date-fns';

describe('convertLocalDateToUTC', () => {
    it('throws an error for invalid time zone abbreviation', () => {
        const date = '2023-01-31 04:00:00';
        const timeZone = 'INVALID_TZ';
        expect(() => {
            convertLocalDateToUTC(date, timeZone).toISOString();
        }).toThrow('Invalid time zone abbreviation: INVALID_TZ');
    });

    it('converts a PST date to UTC correctly', () => {
        const dateInput = '2023-01-31T04:00:00';
        const timeZoneAbbreviation = 'PST';
        const expectedUtcDate = '2023-01-31T12:00:00.000Z'; // This is the expected result in UTC for the given PST date
        const result = convertLocalDateToUTC(dateInput, timeZoneAbbreviation).toISOString();
        expect(result).toBe(expectedUtcDate);
    });

    it('converts a EST date to UTC correctly', () => {
        const dateInput = '2022-03-01T12:00:00';
        const timeZoneAbbreviation = 'EST';
        const expectedOutput = '2022-03-01T17:00:00.000Z';

        const result = convertLocalDateToUTC(dateInput, timeZoneAbbreviation).toISOString();

        expect(result).toBe(expectedOutput);
    });

    it('converts a MST date to UTC correctly', () => {
        const dateInput = '2022-01-01T12:00:00';
        const timeZoneAbbreviation = 'MST';
        const expectedOutput = '2022-01-01T19:00:00.000Z';

        const result = convertLocalDateToUTC(dateInput, timeZoneAbbreviation).toISOString();

        expect(result).toBe(expectedOutput);
    });

    it('converts a CST date to UTC correctly', () => {
        const dateInput = '2022-01-01T12:00:00';
        const timeZoneAbbreviation = 'CST';
        const expectedOutput = '2022-01-01T18:00:00.000Z';

        const result = convertLocalDateToUTC(dateInput, timeZoneAbbreviation).toISOString();

        expect(result).toBe(expectedOutput);
    });

    it('converts a HAST date to UTC correctly', () => {
        const dateInput = '2022-05-05T00:00:00';
        const timeZoneAbbreviation = 'HAST';
        const expectedOutput = '2022-05-05T10:00:00.000Z';

        const dateToUTC = convertLocalDateToUTC(dateInput, timeZoneAbbreviation);
        const result = dateToUTC.toISOString();

        expect(result).toBe(expectedOutput);
    });

    it('converts a AKST date to UTC correctly', () => {
        const dateInput = '2022-05-05T00:00:00';
        const timeZoneAbbreviation = 'AKST';
        const expectedOutput = '2022-05-05T08:00:00.000Z';

        const dateToUTC = convertLocalDateToUTC(dateInput, timeZoneAbbreviation);
        const result = dateToUTC.toISOString();

        expect(result).toBe(expectedOutput);
    });
});

describe('convertUtcToSpecifiedTimeZone', () => {
    it('throws an error for invalid time zone abbreviation', () => {
        const date = '2022-03-01T06:00:00';
        const timeZone = 'INVALID_TZ';
        expect(() => {
            convertUtcToSpecifiedTimeZone(date, timeZone).toISOString();
        }).toThrow('Invalid time zone abbreviation: INVALID_TZ');
    });

    it('converts a UTC date to PST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'PST';
        const expectedUtcDate = '2022-12-12 02:00:00'; // This is the expected result in UTC for the given PST date
        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );
        expect(result).toBe(expectedUtcDate);
    });

    it('converts a UTC date to EST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'EST';
        const expectedOutput = '2022-12-12 05:00:00';

        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );

        expect(result).toBe(expectedOutput);
    });

    it('converts a UTC date to MST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'MST';
        const expectedOutput = '2022-12-12 03:00:00';

        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );

        expect(result).toBe(expectedOutput);
    });

    it('converts a UTC date to CST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'CST';
        const expectedOutput = '2022-12-12 04:00:00';

        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );

        expect(result).toBe(expectedOutput);
    });

    it('converts a UTC date to HAST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'HAST';
        const expectedOutput = '2022-12-12 00:00:00';

        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );

        expect(result).toBe(expectedOutput);
    });

    it('converts a UTC date to AKST correctly', () => {
        const dateInput = '2022-12-12T10:00:00';
        const timeZoneAbbreviation = 'AKST';
        const expectedOutput = '2022-12-12 01:00:00';

        const result = format(
            convertUtcToSpecifiedTimeZone(dateInput, timeZoneAbbreviation),
            API_DATE_TIME_FORMAT,
        );

        expect(result).toBe(expectedOutput);
    });
});

describe('createUTCDate', () => {
    it('should correctly convert UTC date string to Date object', () => {
        const utcDateString = '2022-12-31T23:59:59.999Z';
        const expectedDate = new Date(Date.UTC(2022, 11, 31, 23, 59, 59, 999));
        expect(createUTCDate(utcDateString)).toEqual(expectedDate);
    });

    it('should handle date string without milliseconds', () => {
        const utcDateString = '2022-12-31T23:59:59Z';
        const expectedDate = new Date(Date.UTC(2022, 11, 31, 23, 59, 59));
        expect(createUTCDate(utcDateString)).toEqual(expectedDate);
    });

    it('should throw an error for invalid date string', () => {
        const invalidDateString = 'invalid-date-string';
        expect(() => createUTCDate(invalidDateString)).toThrow();
    });

    it('should handle date string without time', () => {
        const utcDateString = '2022-12-31';
        const expectedDate = new Date(Date.UTC(2022, 11, 31));
        expect(createUTCDate(utcDateString)).toEqual(expectedDate);
    });
});
