import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { canAccess } from '../../../../common/canAccess';
import { useStoreActions, useStoreState } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import {
    InvoiceFilterParam,
    InvoiceFilterableFields,
} from '../../../../store/invoice/type/Invoice';
import { UserRole } from '../../../../store/user/types/User';
import { DEFAULT_PER_PAGE, PaginationDirection, makeFilterParam } from '../../../../utils/paginate';
import FlashMessages from '../../../app/FlashMessages';
import Dialog from '../../../app/shared/Dialog';
import FilterButton from '../../../app/shared/FilterButton';
import Pagination from '../../../app/shared/Pagination';
import DetailsModal from './DetailsModal';
import FilterModal from './FilterModal';
import Header from './Header';
import List from './List';
import useInvoiceActions from './useInvoiceActions';

interface Props {
    claim: ClaimDto;
}

const Invoices: React.FC<Props> = ({ claim }) => {
    const params = useParams();
    const navigate = useNavigate();

    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [filterParams, setFilterParams] = useState<InvoiceFilterParam[]>([]);
    const [showFilterModal, setShowFilterModal] = useState(false);

    const { list, pagination } = useStoreState((state) => state.invoice);
    const { get, checkIfPdfReady } = useStoreActions((actions) => actions.invoice);
    const { getClaim } = useStoreActions((actions) => actions.claim);
    const { latestClaim } = useStoreState((state) => state.claim);

    const handleGetInvoices = useCallback(() => {
        const params = [
            ...filterParams,
            makeFilterParam<InvoiceFilterableFields>('claim_id', '=', claim.id),
        ];
        canAccess([UserRole.ADJUSTER]) &&
            params.push(makeFilterParam<InvoiceFilterableFields>('status', '!=', 'created'));

        get({
            page: currentPage,
            limit: DEFAULT_PER_PAGE,
            order_by: 'created_at',
            direction: PaginationDirection.DESC,
            params,
        });
    }, [claim.id, currentPage, filterParams, get]);

    const handleGetClaim = useCallback(() => {
        if (!claim.id) return;
        getClaim(claim.id);
    }, [getClaim, claim.id]);

    useEffect(() => {
        const pollStatus = async (itemId) => {
            try {
                const response = await checkIfPdfReady(itemId);
                if (response.is_ready) {
                    handleGetInvoices();
                }
            } catch (error) {
                console.error(`Error checking status:`, error);
            }
        };

        const pendingItems = list.value?.filter((item) => item.status === 'preparing');
        const timers = pendingItems.map((item) => setInterval(() => pollStatus(item.id), 5000));

        return () => {
            timers.forEach((timer) => clearInterval(timer));
        };
    }, [list, checkIfPdfReady, handleGetInvoices]);

    const {
        selectedInvoice,
        showDetailsModal,
        setShowDetailsModal,
        onActionClick,
        actionLoading,
        showDeleteDialog,
        onDeleteCancel,
        onDeleteOk,
    } = useInvoiceActions(handleGetInvoices);

    useEffect(() => {
        handleGetInvoices();
        handleGetClaim();
    }, [handleGetInvoices, handleGetClaim]);

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);
        navigate(`/claim/${claim.id}/invoices/page/${page}`);
    };

    const handleAddSuccess = async () => {
        FlashMessages.success('Your invoice is being prepared.');
        setCurrentPage(1);
        setFilterParams([]);
        navigate(`/claim/${claim.id}/invoices`);
    };

    return (
        <div className="claim-invoices box">
            <Header claim={claim} onAddSuccess={() => handleAddSuccess()} />

            <FilterButton
                onClick={() => setShowFilterModal(true)}
                onClear={() => {
                    setCurrentPage(1);
                    setFilterParams([]);
                    navigate(`/claim/${claim.id}/invoices`);
                }}
                filterCount={filterParams.length}
                className="margin-bottom-24 claim-reports-filter-btn"
            />
            <List
                claimArchived={!!claim && !!claim.archivedAt}
                list={list}
                onActionClick={onActionClick}
                actionLoading={actionLoading}
                claim={claim}
            />
            {pagination && <Pagination {...pagination} onChange={handlePaginationChange} />}
            {showDetailsModal && selectedInvoice && claim && (
                <DetailsModal
                    onActionClick={onActionClick}
                    claim={latestClaim || claim}
                    show={showDetailsModal}
                    onClose={() => setShowDetailsModal(false)}
                    onSuccess={() => {
                        setShowDetailsModal(false);
                        handleGetInvoices();
                    }}
                    invoice={selectedInvoice}
                />
            )}
            {showFilterModal && claim && (
                <FilterModal
                    show={showFilterModal}
                    onClose={() => setShowFilterModal(false)}
                    claim={latestClaim || claim}
                    onFilterChange={(params) => {
                        setFilterParams(params);
                        setCurrentPage(1);
                        navigate(`/claim/${claim.id}/invoices`);
                    }}
                    filterParams={filterParams}
                />
            )}
            <Dialog
                show={showDeleteDialog}
                onCancel={onDeleteCancel}
                onOk={onDeleteOk}
                disabled={!!actionLoading}
            />
        </div>
    );
};

export default Invoices;
