import { <PERSON><PERSON>, Col, Row } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { ClaimDto, ExpenseRangesDto } from '../../../../store/claim/dto/ClaimDto';
import { ExpenseFilterParam } from '../../../../store/expense/types/Expense';
import Dialog from '../../../app/shared/Dialog';
import FilterButton from '../../../app/shared/FilterButton';
import Search from '../../../app/shared/Search';
import FilterModal from './FilterModal';
import { ExpenseStatusFilterType } from './utils';
import { useStoreActions } from '../../../../store';

interface Props {
    searchTerm: string;
    onSearch: (searchTerm: string) => void;
    onFilterChange: (filterParams: ExpenseFilterParam[]) => void;
    statusFilter: ExpenseStatusFilterType;
    filterParams: ExpenseFilterParam[];
    claim: ClaimDto;
    selectedIds: string[];
    onClearSelected: VoidFunction;
    onArchive: VoidFunction;
}

const SearchAndFilter: React.FC<Props> = ({
    searchTerm,
    onSearch,
    statusFilter,
    claim,
    onFilterChange,
    filterParams,
    selectedIds,
    onClearSelected,
    onArchive,
}) => {
    const [showFilterModal, setShowFilterModal] = useState(false);
    const [showArchiveDialog, setShowArchiveDialog] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [expenseRanges, setExpenseRanges] = useState<ExpenseRangesDto>();

    const { getExpenseRanges } = useStoreActions((actions) => actions.claim);

    const filterCount = useMemo(() => filterParams.length, [filterParams]);

    useEffect(() => {
        getExpenseRanges(claim.id).then(setExpenseRanges);
    }, [claim.id, getExpenseRanges]);

    const onDialogOk = async () => {
        setIsLoading(true);
        try {
            await onArchive();
        } finally {
            setShowArchiveDialog(false);
            setIsLoading(false);
        }
    };

    return (
        <Row className="claim-expenses-search">
            <Col span={14}>
                <Row gutter={[16, 0]}>
                    <Col span={16}>
                        <Search
                            className="expenses-search margin-right-16"
                            value={searchTerm}
                            onSearch={onSearch}
                        />
                    </Col>
                    <Col span={8}>
                        {!selectedIds.length && (
                            <FilterButton
                                onClick={() => setShowFilterModal(true)}
                                onClear={() => onFilterChange([])}
                                filterCount={filterCount}
                            />
                        )}
                    </Col>
                </Row>
            </Col>
            <Col span={10} className="flex-end">
                {!!selectedIds.length && (
                    <>
                        <div
                            onClick={() => onClearSelected()}
                            className="btn-clear margin-right-24"
                        >
                            Clear {selectedIds.length} selected
                        </div>
                        <Button
                            size="large"
                            className="btn-ghost"
                            onClick={() => setShowArchiveDialog(true)}
                        >
                            Archive
                        </Button>
                    </>
                )}
            </Col>
            {showFilterModal && (
                <FilterModal
                    expenseRanges={expenseRanges}
                    statusFilter={statusFilter}
                    filterParams={filterParams}
                    onFilterChange={onFilterChange}
                    show={showFilterModal}
                    onClose={() => setShowFilterModal(false)}
                />
            )}
            <Dialog
                show={showArchiveDialog}
                onCancel={() => setShowArchiveDialog(false)}
                onOk={() => onDialogOk()}
                disabled={isLoading}
            />
        </Row>
    );
};
export default SearchAndFilter;
