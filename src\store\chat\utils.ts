import { Message } from 'amazon-chime-sdk-js';
import { differenceInSeconds, format, isThisWeek, isToday, isYesterday } from 'date-fns';
import config from '../../common/config';
import { CHAT_DATE_TIME, DAY_TIME_THIS_WEEK, TIME_ONLY } from '../../utils/dateFormat';
import UserDto from '../user/dto/UserDto';
import { MessageDto } from './dto/MessageDto';
import { ChatMessage, ChatMessageGroupData, ChatMessageType } from './types/Chat';

export const CREDENTIALS_REFRESH_BUFFER = 60 * 1000 * 5; // 5 minutes
export const MAX_IMAGE_SIZE = 10 * 1024 * 1024;
export const MAX_MESSAGE_LENGTH = 500;

export const isChannelMessage = (message: Message, channelArn: string): boolean => {
    if (message.type !== 'CREATE_CHANNEL_MESSAGE') {
        return false;
    }

    const messageChannelArn = JSON.parse(message.payload)['ChannelArn'];

    return messageChannelArn === channelArn;
};

const shouldAddTimestamp = (
    prevMessage: MessageDto | ChatMessage,
    nextMessage: MessageDto | ChatMessage,
): boolean => {
    const nextDate = new Date(nextMessage.createdAt);
    const prevDate = new Date(prevMessage.createdAt);

    if (differenceInSeconds(nextDate, prevDate) > config.maxMessageTimeDiff) {
        return true;
    }

    return false;
};

export const formatMessageTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);

    if (isToday(date)) {
        return format(date, TIME_ONLY);
    }

    if (isYesterday(date)) {
        return `Yesterday at ${format(date, TIME_ONLY)}`;
    }

    if (isThisWeek(date)) {
        return format(date, DAY_TIME_THIS_WEEK);
    }

    return format(date, CHAT_DATE_TIME);
};

export const makeTimestampMessage = (timestamp: string): ChatMessage => ({
    id: timestamp,
    createdAt: formatMessageTimestamp(timestamp),
    type: ChatMessageType.TIMESTAMP,
    data: null,
});

export const makeGroupMessage = (dto: MessageDto): ChatMessage => ({
    id: dto.id,
    createdAt: dto.createdAt,
    type: ChatMessageType.GROUP,
    data: {
        sender: dto.meta.sender,
        messages: [dto],
    },
});

export const addMessageToGroup = (group: ChatMessage, dto: MessageDto) => {
    group.createdAt = dto.createdAt;
    (group.data as ChatMessageGroupData).messages.push(dto);
};

export function groupMessages(messages: MessageDto[]) {
    return messages.reduce((acc: ChatMessage[], currentMessage) => {
        const lastMessage = acc[acc.length - 1];

        if (!lastMessage || shouldAddTimestamp(lastMessage, currentMessage)) {
            acc.push(makeTimestampMessage(currentMessage.createdAt));
            acc.push(makeGroupMessage(currentMessage));
            return acc;
        }

        if (
            lastMessage.type === ChatMessageType.GROUP &&
            lastMessage.data?.sender.userId !== currentMessage.meta.sender.userId
        ) {
            acc.push(makeGroupMessage(currentMessage));
            return acc;
        }

        addMessageToGroup(lastMessage, currentMessage);
        return acc;
    }, [] as ChatMessage[]);
}

export const isUserMessage = (message: MessageDto, user: UserDto | null | undefined) =>
    user && message.meta.sender.userId === user.id;

export const CHAT_PING_INTERVAL = 1;
