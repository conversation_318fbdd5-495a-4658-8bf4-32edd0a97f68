import { Checkbox } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { Resource } from '../../../types';
import { AnyObject } from 'antd/es/_util/type';
import { ReactComponent as IconSpinner } from '../../../assets/icons/spinner.svg';

interface Props<T> {
    list: Resource<T[]>;
    columns: ColumnsType<T>;
    loading?: boolean;
    rowSelection?: TableRowSelection<T>;
    onSelectAll?: (selectAll: boolean) => void;
    onToggleSort?: (sortField: string, sortOrder: 'ascend' | 'descend') => void;
}

function DataGrid<T extends AnyObject>({
    list,
    columns,
    loading = false,
    rowSelection,
    onSelectAll,
    onToggleSort,
}: Props<T>) {
    const handleTableChange = (pagination, filters, sorter) => {
        if (onToggleSort) {
            onToggleSort(sorter.columnKey as string, sorter.order as 'ascend' | 'descend');
        }
    };

    if (rowSelection) {
        rowSelection.columnTitle = (
            <div className="flex-space select-box">
                <Checkbox
                    checked={
                        !!rowSelection.selectedRowKeys?.length &&
                        rowSelection.selectedRowKeys?.length === list.value.length
                    }
                    onChange={(e) => onSelectAll && onSelectAll(e.target.checked)}
                />
                #
            </div>
        );
        rowSelection.columnWidth = 40;
        rowSelection.hideSelectAll = false;
        rowSelection.type = 'checkbox';
        rowSelection.renderCell = (checked, record, index, originNode) => {
            return (
                <div className="flex-space select-box">
                    {originNode} {index + 1}
                </div>
            );
        };
    }

    return (
        <Table
            rowKey={(row) => (row as any).id}
            columns={columns}
            dataSource={list.value}
            pagination={false}
            loading={
                loading || list.loading ? { indicator: <IconSpinner />, size: 'large' } : false
            }
            rowSelection={rowSelection}
            onChange={handleTableChange}
        />
    );
}

export default DataGrid;
