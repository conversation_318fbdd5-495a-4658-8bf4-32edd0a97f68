import { plainToInstance } from 'class-transformer';
import ProfileDto from './dto/ProfileDto';
import RawProfile from './types/RawProfile';
import { fromRawRole } from '../user/mappers';

export const mapProfile = ({
    profile_photo,
    job_title,
    created_at,
    role,
    preferred_contact_method,
    company,
    ...rest
}: RawProfile): ProfileDto =>
    plainToInstance(ProfileDto, {
        ...rest,
        profilePhoto: profile_photo,
        jobTitle: job_title || undefined,
        company: company || undefined,
        createdAt: created_at,
        role: fromRawRole[role],
        contactMethod: preferred_contact_method || undefined,
    });
