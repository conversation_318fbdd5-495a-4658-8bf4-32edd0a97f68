---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: oope-admin
  namespace: dev
  labels:
    app: oope-admin
    environment: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: oope-admin
  minReadySeconds: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        app: oope-admin
    spec:
      restartPolicy: Always
      terminationGracePeriodSeconds: 15
      containers:
        - name: oope-admin
          image: 738408760783.dkr.ecr.us-east-1.amazonaws.com/oope-admin-app:dev
          imagePullPolicy: Always
          ports:
            - containerPort: 80
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 10
            failureThreshold: 5
            periodSeconds: 10
          startupProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 60
            failureThreshold: 3
            periodSeconds: 5
          readinessProbe:
            httpGet:
              path: /
              port: 80
            failureThreshold: 3
            initialDelaySeconds: 65
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: oope-admin
  namespace: dev
  labels:
    app: oope-admin
    environment: dev
spec:
  type: NodePort
  ports:
    - name: '80'
      port: 80
      targetPort: 80
  selector:
    app: oope-admin
