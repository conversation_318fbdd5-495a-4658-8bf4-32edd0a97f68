{"parser": "@typescript-eslint/parser", "extends": ["plugin:eslint-comments/recommended", "prettier", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"project": "./tsconfig.json"}, "env": {"browser": true, "jest": true, "es6": true}, "plugins": ["prettier", "@typescript-eslint", "react"], "settings": {"react": {"version": "detect"}}, "rules": {"react/function-component-definition": "off", "@typescript-eslint/indent": "off", "@typescript-eslint/explicit-function-return-type": "off", "react/prop-types": "off", "react/button-has-type": "off", "react/react-in-jsx-scope": "off", "import/no-extraneous-dependencies": "off", "prettier/prettier": ["error"], "indent": "off", "react/jsx-props-no-spreading": "off", "no-param-reassign": 0, "@typescript-eslint/no-shadow": "off", "react-hooks/exhaustive-deps": "error", "react/display-name": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-empty-function": "off"}, "overrides": [{"files": ["vite.config.ts"], "parserOptions": {"project": "./tsconfig.json"}}], "ignorePatterns": ["babel.config.js", "node_modules/(?!(antd)/)"]}