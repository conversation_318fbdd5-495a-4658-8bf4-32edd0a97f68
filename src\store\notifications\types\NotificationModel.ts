import { Action, Computed, Thunk } from 'easy-peasy';
import { Resource, ResourcePagination } from '../../app/types';
import { NotificationDto, NotificationPaginateDto } from '../dto/NotificationDto';

export default interface NotificationModel {
    // state
    list: Resource<NotificationDto[]>;
    pagination: ResourcePagination | null;
    unreadCount: number;

    // computed
    hasMore: Computed<NotificationModel, boolean>;

    // actions
    load: Action<NotificationModel, NotificationDto[]>;
    loading: Action<NotificationModel>;
    setPagination: Action<NotificationModel, ResourcePagination>;
    setUnreadCount: Action<NotificationModel, number>;
    setAllRead: Action<NotificationModel>;
    setNotificationRead: Action<NotificationModel, string>;
    unloadList: Action<NotificationModel>;
    unload: Action<NotificationModel>;

    // thunks
    get: Thunk<NotificationModel, NotificationPaginateDto>;
    checkUnread: Thunk<NotificationModel>;
    markRead: Thunk<NotificationModel, string>;
    readAll: Thunk<NotificationModel>;
}
