import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useEffect, useState } from 'react';
import { useStoreActions } from '../../../../store';
import FlashMessages from '../../../app/FlashMessages';
import { downloadCSVFile, downloadPDFFile, InvoiceActions } from './utils';
import { InvoiceDto } from '../../../../store/invoice/dto/InvoiceDto';
import { useParams } from 'react-router-dom';

const useInvoiceActions = (handleGetInvoices: any) => {
    const { invoiceId } = useParams();

    const [selectedInvoice, setSelectedInvoice] = useState<InvoiceDto>();
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [isLoading, setIsLoading] = useState<string | boolean>(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const {
        getCSV,
        delete: deleteInvoice,
        sendToAdjuster,
        getInvoice,
        uploadedtoQbs,
    } = useStoreActions((actions) => actions.invoice);

    useEffect(() => {
        if (!invoiceId) {
            return;
        }

        getInvoice(invoiceId).then((invoice) => {
            setSelectedInvoice(invoice);
            setShowDetailsModal(true);
        });
    }, [getInvoice, invoiceId]);

    const handleDownloadPdf = async (invoice: InvoiceDto) => {
        setIsLoading(invoice.id);
        try {
            if (!invoice.invoice_url) {
                getInvoice(invoice.id).then((r) => {
                    if (!r.invoice_url) {
                        FlashMessages.warn(
                            'PDF file is not ready yet, please try again in a few minutes.',
                        );
                        return;
                    }
                    downloadPDFFile(r.invoice_url);
                });
                return;
            }
            downloadPDFFile(invoice.invoice_url);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Fetch PDF');
        } finally {
            setIsLoading(false);
        }
    };

    const handleDownloadCSV = async (invoice: InvoiceDto) => {
        setIsLoading(invoice.id);
        try {
            const csvString = await getCSV(invoice.id);
            downloadCSVFile(csvString);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Fetch CSV');
        } finally {
            setIsLoading(false);
        }
    };

    const handleDelete = async (invoice: InvoiceDto | undefined) => {
        setIsLoading(invoice?.id || true);
        try {
            if (!invoice) {
                throw new Error('No invoice to delete');
            }
            await deleteInvoice(invoice.id);
            await handleGetInvoices();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed Delete Invoice');
        } finally {
            setSelectedInvoice(undefined);
            setShowDeleteDialog(false);
            setIsLoading(false);
        }
    };

    const handleUploadedToQbs = async (invoice: InvoiceDto | undefined) => {
        setIsLoading(invoice?.id || true);
        try {
            if (!invoice) {
                throw new Error('No invoice to delete');
            }
            await uploadedtoQbs(invoice.id);
            await handleGetInvoices();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed Delete Invoice');
        } finally {
            setSelectedInvoice(undefined);
            setShowDeleteDialog(false);
            setIsLoading(false);
        }
    };

    const handleSendEmail = async (invoice: InvoiceDto) => {
        setIsLoading(invoice.id);
        try {
            await sendToAdjuster(invoice.id);
            FlashMessages.success('Sent to Adjuster');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    const handleActionClick = (item: MenuItemType | undefined, invoice: InvoiceDto) => {
        if (!item) {
            return;
        }

        switch (item.key) {
            case InvoiceActions.VIEW:
                setSelectedInvoice(invoice);
                setShowDetailsModal(true);
                return;
            case InvoiceActions.DOWNLOAD_PDF:
                handleDownloadPdf(invoice);
                break;
            case InvoiceActions.UPLOADED_TO_QBS:
                handleUploadedToQbs(invoice);
                break;
            case InvoiceActions.DOWNLOAD_CSV:
                handleDownloadCSV(invoice);
                break;
            case InvoiceActions.DELETE:
                setSelectedInvoice(invoice);
                setShowDeleteDialog(true);
                break;
            case InvoiceActions.EMAIL:
                handleSendEmail(invoice);
                break;
        }
    };

    return {
        selectedInvoice,
        showDetailsModal,
        setShowDetailsModal: (show: boolean) => {
            setShowDetailsModal(show);
            !show && setSelectedInvoice(undefined);
        },
        onActionClick: handleActionClick,
        actionLoading: isLoading,
        showDeleteDialog,
        onDeleteCancel: () => {
            setSelectedInvoice(undefined);
            setShowDeleteDialog(false);
        },
        onDeleteOk: () => handleDelete(selectedInvoice),
    };
};
export default useInvoiceActions;
