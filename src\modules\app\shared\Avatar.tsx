import { Avatar as AntAvatar } from 'antd';
import classNames from 'classnames';
import { useMemo } from 'react';
import Spinner from './Spinner';

interface Props {
    name?: string;
    photoUrl?: string;
    size?: 'small' | 'medium' | 'large' | 'huge' | 'table';
    className?: string;
    loading?: boolean;
}

const mappedSize = {
    table: 32,
    small: 40,
    medium: 56,
    large: 88,
    huge: 250,
};

const Avatar: React.FC<Props> = ({
    photoUrl,
    size = 'small',
    name = '',
    className,
    loading = false,
}) => {
    const initials = useMemo(() => {
        if (!name) return '';
        const words = name.split(' ').map((word) => word.trim());

        const firstInitial = words[0][0].toUpperCase();
        const lastInitial = words.length > 1 ? words[words.length - 1][0].toUpperCase() : '';
        return firstInitial + lastInitial;
    }, [name]);

    return (
        <AntAvatar
            className={classNames('avatar', size, className)}
            shape="circle"
            src={photoUrl}
            size={mappedSize[size]}
        >
            {loading ? <Spinner /> : <span>{initials}</span>}
        </AntAvatar>
    );
};
export default Avatar;
