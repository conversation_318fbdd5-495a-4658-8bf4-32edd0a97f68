import { Button, Col, Row, Space } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ReactComponent as IconPencil } from '../../assets/icons/pencil-sharp.svg';
import withAuth from '../../hooks/withAuth';
import { useStoreActions, useStoreState } from '../../store';
import { ClaimFilterableField, ClaimsOrderBy } from '../../store/claim/types/Claim';
import { UserRole } from '../../store/user/types/User';
import { PaginationDirection, makeFilterParam } from '../../utils/paginate';
import { formatPhoneNumber } from '../../utils/phoneFormat';
import { capitalizeString } from '../../utils/strings';
import FlashMessages from '../app/FlashMessages';
import CenterLayout from '../app/layouts/AccountLayout';
import Avatar from '../app/shared/Avatar';
import Pagination from '../app/shared/Pagination';
import Spinner from '../app/shared/Spinner';
import ConnectedClaimsList from './ConnectedClaimsList';
import EditAccountModal from './EditModal';
import EditProfilePhotoModal from './EditProfilePhotoModal';
import { canAccess } from '../../common/canAccess';

const MyAccountPage: React.FC = () => {
    const { pageNumber } = useParams();
    const navigate = useNavigate();

    const [currentPage, setCurrentPage] = useState(parseInt(pageNumber || '1'));
    const [showEditModal, setShowEditModal] = useState(false);
    const [showEditPhotoModal, setSHowEditPhotoModal] = useState(false);

    const { forgotPassword } = useStoreActions((actions) => actions.auth);

    const { data: profile } = useStoreState((state) => state.profile);
    const { get } = useStoreActions((actions) => actions.profile);

    const { list: claims, pagination } = useStoreState((state) => state.claim);
    const { get: geClaimsList } = useStoreActions((actions) => actions.claim);

    const getClaims = useCallback(
        (
            page = 1,
            limit = 6,
            order_by = ClaimsOrderBy.ID,
            direction = PaginationDirection.DESC,
        ) => {
            if (!profile) return;

            geClaimsList({
                page,
                limit,
                order_by,
                direction,
                params: [
                    makeFilterParam<ClaimFilterableField>(
                        profile.role === UserRole.ADJUSTER ? 'user_id' : 'admin_id',
                        '=',
                        profile.id,
                    ),
                    makeFilterParam<ClaimFilterableField>('archived_at', 'is null'),
                ],
            });
        },
        [geClaimsList, profile],
    );

    useEffect(() => {
        getClaims(currentPage);
    }, [currentPage, geClaimsList, getClaims]);

    useEffect(() => {
        get();
    }, [get]);

    const onResetPassword = async () => {
        if (!profile) {
            return;
        }
        await forgotPassword(profile.email);
        navigate('/reset-password-message');
    };

    const handlePaginationChange = (pageNumber) => {
        setCurrentPage(pageNumber);
        navigate(`/account/page/${pageNumber}`);
    };

    return (
        <CenterLayout>
            <div className="account">
                <div className=" box account-heading">
                    <div className="account-heading-avatar">
                        <Avatar
                            size="large"
                            name={profile?.name}
                            photoUrl={profile?.profilePhoto}
                            loading={!profile}
                        />
                        <div className="account-heading-avatar-info">
                            {profile && (
                                <>
                                    <span className="account-heading-avatar-info-name">
                                        {profile.name}
                                    </span>
                                    <span className="account-heading-avatar-info-role">
                                        {profile.role}
                                    </span>
                                </>
                            )}
                        </div>
                    </div>

                    <div className="account-heading-edit">
                        <Button
                            size="large"
                            className="btn-secondary outline"
                            onClick={() => setSHowEditPhotoModal(true)}
                        >
                            <span>Edit Image</span>
                            <IconPencil />
                        </Button>
                    </div>
                </div>
                <div className="box account-personal">
                    {profile ? (
                        <>
                            <Row
                                className="account-personal-title"
                                justify="space-between"
                                align="middle"
                            >
                                <div className="heading subheading">Personal Information</div>
                                <Button
                                    size="large"
                                    className="btn-secondary outline"
                                    onClick={() => setShowEditModal(true)}
                                >
                                    <Space>
                                        <span>Edit</span>
                                        <IconPencil />
                                    </Space>
                                </Button>
                            </Row>
                            <Row
                                gutter={[40, 24]}
                                className="account-personal-row"
                                justify="space-between"
                                align="middle"
                            >
                                <Col span={12}>
                                    <Space
                                        direction="vertical"
                                        align="start"
                                        className="account-personal-group"
                                    >
                                        <div className="account-personal-group-title">
                                            Full Name
                                        </div>
                                        <div className="account-personal-group-value">
                                            {profile.name}
                                        </div>
                                    </Space>
                                </Col>
                                <Col span={12}>
                                    <Space
                                        direction="vertical"
                                        align="start"
                                        className="account-personal-group"
                                    >
                                        <div className="account-personal-group-title">
                                            Email Address
                                        </div>
                                        <div className="account-personal-group-value">
                                            {profile.email}
                                        </div>
                                    </Space>
                                </Col>
                            </Row>
                            <Row
                                gutter={[40, 24]}
                                className="account-personal-row"
                                justify="space-between"
                                align="middle"
                            >
                                <Col span={12}>
                                    <Space
                                        direction="vertical"
                                        align="start"
                                        className="account-personal-group"
                                    >
                                        <div className="account-personal-group-title">
                                            Phone Number
                                        </div>
                                        <div className="account-personal-group-value">
                                            {formatPhoneNumber(profile.phone.toString())}
                                        </div>
                                    </Space>
                                </Col>
                                <Col span={12}>
                                    <Space
                                        direction="vertical"
                                        align="start"
                                        className="account-personal-group"
                                    >
                                        <div className="account-personal-group-title">
                                            Method of Contact
                                        </div>
                                        <div className="account-personal-group-value">
                                            {capitalizeString(profile?.contactMethod || '')}
                                        </div>
                                    </Space>
                                </Col>
                            </Row>

                            {canAccess([UserRole.ADJUSTER]) && (
                                <Row
                                    gutter={[40, 24]}
                                    className="account-personal-row"
                                    justify="space-between"
                                    align="middle"
                                >
                                    <Col span={12}>
                                        <Space
                                            direction="vertical"
                                            align="start"
                                            className="account-personal-group"
                                        >
                                            <div className="account-personal-group-title">
                                                Company
                                            </div>
                                            <div className="account-personal-group-value">
                                                {formatPhoneNumber(profile.company)}
                                            </div>
                                        </Space>
                                    </Col>
                                    <Col span={12}>
                                        <Space
                                            direction="vertical"
                                            align="start"
                                            className="account-personal-group"
                                        >
                                            <div className="account-personal-group-title">
                                                Title
                                            </div>
                                            <div className="account-personal-group-value">
                                                {capitalizeString(profile.jobTitle)}
                                            </div>
                                        </Space>
                                    </Col>
                                </Row>
                            )}
                        </>
                    ) : (
                        <Spinner type="skeleton" />
                    )}
                </div>
                <div className="box account-claims">
                    <Row>
                        <div className="heading subheading">Connected Policyholder Claims</div>
                    </Row>
                    <ConnectedClaimsList list={claims} />
                    {pagination && <Pagination {...pagination} onChange={handlePaginationChange} />}
                </div>
                <Button className="btn-ghost" onClick={() => onResetPassword()}>
                    Reset Password
                </Button>
            </div>
            {showEditModal && (
                <EditAccountModal
                    onClose={() => setShowEditModal(false)}
                    onSuccess={() => {
                        FlashMessages.success('Profile edited successfully');
                        setShowEditModal(false);
                    }}
                    show={showEditModal}
                />
            )}
            {showEditPhotoModal && (
                <EditProfilePhotoModal
                    onClose={() => setSHowEditPhotoModal(false)}
                    onSuccess={(action) => {
                        const message =
                            action === 'update' ? 'Profile photo changed' : 'Profile photo removed';
                        FlashMessages.success(message);
                        setSHowEditPhotoModal(false);
                    }}
                    show={showEditPhotoModal}
                />
            )}
        </CenterLayout>
    );
};

export default withAuth(MyAccountPage, [UserRole.ADJUSTER, UserRole.ADMIN, UserRole.SUPER_ADMIN]);
