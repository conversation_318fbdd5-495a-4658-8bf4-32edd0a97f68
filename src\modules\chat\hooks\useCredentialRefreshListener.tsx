import { useEffect } from 'react';
import { useStoreActions } from '../../../store';
import { NOTIFICATION_INTERVAL_PERIOD } from '../../notifications/utills';

const useCredentialRefreshListener = () => {
    const { needsCredentialRefresh, unload } = useStoreActions((actions) => actions.chat);

    useEffect(() => {
        const interval = setInterval(() => {
            needsCredentialRefresh() && unload();
        }, NOTIFICATION_INTERVAL_PERIOD);

        return () => {
            clearInterval(interval);
        };
    }, [needsCredentialRefresh, unload]);
};
export default useCredentialRefreshListener;
