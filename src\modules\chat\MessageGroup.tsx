import classNames from 'classnames';
import { useStoreState } from '../../store';
import { ChatMessageGroupData } from '../../store/chat/types/Chat';
import { formatMessageTimestamp } from '../../store/chat/utils';
import GroupMessage from './GroupMessage';

interface Props {
    group: ChatMessageGroupData;
}

const MessageGroup: React.FC<Props> = ({ group }) => {
    const { user } = useStoreState((state) => state.auth);

    const lastMessageTimestamp = group.messages[group.messages.length - 1].createdAt;
    const isUser = group.sender.userId === user?.id;

    if (!user) return null;

    return (
        <div className="chat-list-group">
            {group.messages.map((message, index) => {
                const isLastMessage = index === group.messages.length - 1;
                return (
                    <GroupMessage
                        key={message.id}
                        message={message}
                        isLastMessage={isLastMessage}
                        isUser={isUser}
                    />
                );
            })}
            <div
                className={classNames('chat-list-group-timestamp margin-bottom-8', {
                    'is-user': isUser,
                })}
            >
                {formatMessageTimestamp(lastMessageTimestamp)}
            </div>
        </div>
    );
};

export default MessageGroup;
