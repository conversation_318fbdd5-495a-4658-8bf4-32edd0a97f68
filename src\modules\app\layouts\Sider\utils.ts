import { MenuProps } from 'antd';

export const mainNavItems: MenuProps['items'] = [
    {
        key: 'dashboard',
        label: 'Dashboard',
    },
    {
        key: 'expenses',
        label: 'Expenses',
    },
    {
        key: 'reports',
        label: 'Reports',
    },
    {
        key: 'invoices',
        label: 'Invoices',
    },
];

export const communicationNavItems: MenuProps['items'] = [
    {
        key: 'chat',
        label: 'Chat',
    },
];

export const allNavItems = [...mainNavItems, ...communicationNavItems];

export const getInitialNavKey = (
    navItems: MenuProps['items'] = [],
    pathName: string | undefined,
) => {
    if (!pathName) return '';
    return (
        (navItems
            .map((navItem) => navItem?.key)
            .find((key) => pathName.includes(key as string)) as string) || 'dashboard'
    );
};
