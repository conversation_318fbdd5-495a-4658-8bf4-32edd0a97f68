import { Layout } from 'antd';
import { Header } from './Header';
import MyAccountSubHeader from './Header/SubHeader/MyAccountSubHeader';

const { Content } = Layout;

interface Props {
    children: React.ReactNode;
}

const CenterLayout: React.FC<Props> = ({ children }) => {
    return (
        <Layout className="center-layout">
            <Header SubHeaderComponent={<MyAccountSubHeader />} />
            <Content className="center-layout-content">{children}</Content>
        </Layout>
    );
};
export default CenterLayout;
