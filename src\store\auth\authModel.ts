import { addSeconds } from 'date-fns';
import { Actions, action, computed, thunk } from 'easy-peasy';
import { api, publicApi } from '../../common/api';
import { mapUser } from '../user/mappers';
import { mapAuthLogin } from './mappers';
import AuthModel from './types/AuthModel';
import AppModel from '../app/types/AppModel';

const authModel: AuthModel = {
    // state
    accessToken: null,
    refreshToken: null,
    expiresAt: null,
    _retry: false,
    user: null,

    // computed
    isAuthenticated: computed((state) => state.accessToken !== null),
    userRole: computed((state) => state?.user?.role || null),

    // actions
    load: action((state, payload) => {
        state.accessToken = payload.accessToken;
        state.refreshToken = payload.refreshToken;
        state.expiresAt = addSeconds(new Date(), payload.expiresIn);
    }),

    loadAuthUser: action((state, payload) => {
        state.user = payload;
    }),

    unload: action((state) => {
        state.accessToken = null;
        state.refreshToken = null;
        state.user = null;
        state.expiresAt = null;
    }),

    retry: action((state, value) => {
        state._retry = value;
    }),

    // thunks
    requestLogin: thunk(async (actions, payload) => {
        return publicApi.post('/auth/login', payload);
    }),

    login: thunk(async (actions, payload) => {
        const res = await publicApi.post('/auth/verify-2fa', payload);

        actions.retry(false);
        actions.load(mapAuthLogin(res.data.data));
        actions.loadAuthUser(mapUser(res.data.data.user));
    }),

    logout: thunk(async (actions, _, { getStoreActions }) => {
        await api.post('/auth/logout');
        actions.unload();
        (getStoreActions() as Actions<AppModel>).profile.unload();
    }),

    refresh: thunk(async (actions, payload, { getState }) => {
        const { accessToken } = getState();
        actions.retry(true);
        publicApi
            .post('/auth/refresh', undefined, {
                headers: {
                    Authorization: `Bearer ${accessToken}`,
                },
            })
            .then((res) => {
                actions.retry(false);
                actions.load(mapAuthLogin(res.data.data));
                actions.loadAuthUser(mapUser(res.data.data.user));
            });
    }),

    activate: thunk(async (actions, { token, dto }) => {
        publicApi.post(`/auth/${token}/activate`, dto);
    }),

    forgotPassword: thunk(async (actions, email) => {
        await publicApi.post(`/auth/password/forgot`, { email, guard: 'api-user' });
    }),

    resetPassword: thunk(async (actions, dto) => {
        await publicApi.post('/auth/password/reset', dto);
    }),
};

export default authModel;
