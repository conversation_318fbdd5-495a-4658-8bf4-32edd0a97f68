import { MenuProps } from 'antd';
import { plainToInstance } from 'class-transformer';
import { ReportFilterDto } from '../../../../store/report/dto/ReportDto';
import { RawReportType, ReportFilterParam, ReportType } from '../../../../store/report/type/Report';
import { getReadableExpenseCategory } from '../Expenses/utils';
import { ExpenseCategory } from '../../../../types';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';

export enum ReportActions {
    DOWNLOAD_PDF = 'Download PDF',
    DOWNLOAD_ADJUSTER = 'Download Adjuster PDF',
    WITHDRAW_ANALYTICS = 'Withdraw Analytics',
    SEND_ANALYTICS = 'Send Analytics to Salesforce',
    DOWNLOAD_POLICY_HOLDER = 'Download Policyholder PDF',
    DOWNLOAD_CSV = 'Download CSV',
    DELETE = 'Delete Report',
    EMAIL = 'Email to Adjuster',
    VIEW = 'View',
}

export const adminActionMenuItems: MenuProps['items'] = [
    {
        label: ReportActions.VIEW,
        key: ReportActions.VIEW,
    },
    {
        label: ReportActions.DOWNLOAD_PDF,
        key: ReportActions.DOWNLOAD_PDF,
    },
    {
        label: ReportActions.DOWNLOAD_CSV,
        key: ReportActions.DOWNLOAD_CSV,
    },
    {
        label: ReportActions.DELETE,
        key: ReportActions.DELETE,
    },
    {
        type: 'divider',
    },
    {
        label: ReportActions.EMAIL,
        key: ReportActions.EMAIL,
    },
];

export const adminReportRequestActionMenuItems: MenuProps['items'] = [
    {
        label: ReportActions.VIEW,
        key: ReportActions.VIEW,
    },
    {
        label: ReportActions.DOWNLOAD_ADJUSTER,
        key: ReportActions.DOWNLOAD_ADJUSTER,
    },
    {
        label: ReportActions.DOWNLOAD_POLICY_HOLDER,
        key: ReportActions.DOWNLOAD_PDF,
    },
    {
        label: ReportActions.DOWNLOAD_CSV,
        key: ReportActions.DOWNLOAD_CSV,
    },
    {
        label: ReportActions.WITHDRAW_ANALYTICS,
        key: ReportActions.WITHDRAW_ANALYTICS,
    },
    {
        label: ReportActions.DELETE,
        key: ReportActions.DELETE,
    },
    {
        type: 'divider',
    },
    {
        label: ReportActions.EMAIL,
        key: ReportActions.EMAIL,
    },
];

export const adminReportRequestActionAnalticsMenuItems: MenuProps['items'] = [
    {
        label: ReportActions.VIEW,
        key: ReportActions.VIEW,
    },
    {
        label: ReportActions.DOWNLOAD_ADJUSTER,
        key: ReportActions.DOWNLOAD_ADJUSTER,
    },
    {
        label: ReportActions.DOWNLOAD_POLICY_HOLDER,
        key: ReportActions.DOWNLOAD_PDF,
    },
    {
        label: ReportActions.DOWNLOAD_CSV,
        key: ReportActions.DOWNLOAD_CSV,
    },
    {
        label: ReportActions.SEND_ANALYTICS,
        key: ReportActions.SEND_ANALYTICS,
    },
    {
        label: ReportActions.DELETE,
        key: ReportActions.DELETE,
    },
    {
        type: 'divider',
    },
    {
        label: ReportActions.EMAIL,
        key: ReportActions.EMAIL,
    },
];

export const adjusterActionMenuItems: MenuProps['items'] = [
    {
        label: ReportActions.VIEW,
        key: ReportActions.VIEW,
    },
    {
        label: ReportActions.DOWNLOAD_PDF,
        key: ReportActions.DOWNLOAD_PDF,
    },
    {
        label: ReportActions.DOWNLOAD_CSV,
        key: ReportActions.DOWNLOAD_CSV,
    },
];

export const downloadPDFFile = (url: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'report.pdf');
    link.setAttribute('target', '_blank');
    link.click();
    link.remove();
};

export const downloadCSVFile = (csvString: string) => {
    const blob = new Blob([csvString], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'report.csv');
    link.click();
    URL.revokeObjectURL(url);
};

export const mapFilterParams = (filterParams: ReportFilterParam[]): ReportFilterDto => {
    const status = (filterParams.find((param) => param.field === 'status')?.value ||
        []) as string[];

    const type = filterParams.find((param) => param.field === 'type')?.value;
    const dateRange = (
        filterParams.find((param) => param.field === 'created_at')?.value as string[] | undefined
    )?.map((date) => new Date(date));

    return plainToInstance(ReportFilterDto, {
        status,
        type,
        dateRange,
    });
};

export const TypeOptions: SelectBoxOption[] = Object.keys(ReportType).map((key) => ({
    label: ReportType[key],
    value: RawReportType[key],
}));

export const ReportStatusOptions: SelectBoxOption[] = [
    {
        label: 'Ready',
        value: 'created',
    },
    {
        label: 'Pending',
        value: 'pending',
    },
    {
        label: 'Approved',
        value: 'approved',
    },
    {
        label: 'Rejected',
        value: 'rejected',
    },
];

export const ExpenseCategoryOptions: SelectBoxOption[] = Object.values(ExpenseCategory).map(
    (category) => ({
        label: getReadableExpenseCategory(category),
        value: category as string,
    }),
);
