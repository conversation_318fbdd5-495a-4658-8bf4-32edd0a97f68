.btn-filter {
    @include flex(flex, row, nowrap, space-between, center);
    gap: 16px;
    height: 100%;

    &.active {
        border: none !important;

        .filter-count {
            @include flex(flex, row, nowrap, center, center);
            background-color: white;
            border-radius: 50%;
            font-size: 11px;
            height: 16px;
            line-height: 11px;
            text-align: center;
            width: 16px;
        }
    }

    &-container {
        @include flex(flex, row, nowrap, flex-start, center);
        height: 100%;

        .btn-clear {
            cursor: pointer;
            font-size: 16px;
            font-weight: $font-weight-semi-bold;
            line-height: 20px;
            text-decoration-line: underline;
        }
    }
}
