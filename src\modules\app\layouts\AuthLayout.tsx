import { Col, Layout, Row } from 'antd';

const { Content } = Layout;

interface Props {
    children: React.ReactNode;
}

const AuthLayout: React.FC<Props> = ({ children }) => {
    return (
        <Layout className="dashboard-auth">
            <Content>
                <Row className="dashboard-auth-container" justify="center" align="middle">
                    <Col className="dashboard-auth-container-main">{children}</Col>
                </Row>
            </Content>
        </Layout>
    );
};

export default AuthLayout;
