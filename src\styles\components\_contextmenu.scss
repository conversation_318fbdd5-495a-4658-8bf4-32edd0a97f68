.contextmenu {
    &-overlay {
        border-radius: 0;

        ul {
            background-color: $color-background !important;
            border: 1px solid transparentize($color-tertiary, 0.8);
            border-radius: 0 !important;
            box-shadow: none !important;
            padding: 8px !important;

            li {
                border-radius: 0;
                font-weight: $font-weight-medium !important;
                line-height: 18px !important;
                padding: 8px !important;
                transition: all 0.3s ease !important;

                &:hover {
                    background-color: $color-secondary-transparent !important;
                    border-radius: 0 !important;
                }

                &.ant-dropdown-menu-item-divider {
                    padding: 0px !important;
                }
            }
        }
    }

    &.disabled {
        opacity: .7;
        pointer-events: none;
    }

    &-tooltip {
        background-color: $color-text !important;
    }
}

.contextmenu-icon-close,
.contextmenu-icon-menu {
    opacity: 1;
    transition: opacity 1s ease;
}
