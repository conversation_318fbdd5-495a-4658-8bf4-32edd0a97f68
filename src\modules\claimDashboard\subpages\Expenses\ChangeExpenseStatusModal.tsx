import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Flex, Row } from 'antd';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { ChangeExpenseStatusDto, ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import { formatCurrency } from '../../../../utils/currencyFormat';
import { convertUtcToSpecifiedTimeZone, DATE_TIME } from '../../../../utils/dateFormat';
import FlashMessages from '../../../app/FlashMessages';
import Avatar from '../../../app/shared/Avatar';
import Modal from '../../../app/shared/Modal';
import { toRawStatusMap } from '../../../../store/expense/mappers';
import Form from '../../../app/shared/form/Form';
import TextField from '../../../app/shared/form/TextField';
import ReceiptPreview from './ReceiptPreview';
import {
    AuditedType,
    ExpenseStatus,
    getInitialReviewersComments,
    getReviewerCommentByStatus,
    getReviewerCommentsByStatusToSelectBox,
    getReviewerCommentValueByStatus,
    isOtherField,
} from './utils';
import classNames from 'classnames';
import { format } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import React, { useEffect, useState } from 'react';
import { ReviewersCommentsType } from '../../../../common/ReviewersCommentsType';
import SelectBox from '../../../app/shared/form/SelectBox';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';
import ModalNavigationWrapper from './ModalNavigationWrapper';

interface Props {
    claim: ClaimDto;
    disableNav?: boolean;
    expense: ExpenseDto;
    onClose: VoidFunction;
    onNextExpense?: VoidFunction;
    onPrevExpense?: VoidFunction;
    onSuccess: VoidFunction;
    show: boolean;
    status?: RawExpenseStatus;
}

const ChangeExpenseStatusModal: React.FC<Props> = ({
    claim,
    disableNav,
    expense,
    onClose,
    onNextExpense,
    onPrevExpense,
    onSuccess,
    show,
    status,
}) => {
    const { update, view } = useStoreActions((actions) => actions.expense);
    const [canChange, setCanChange] = useState(false);
    const [reviewersComments, setReviewersComments] = useState<SelectBoxOption[]>(
        getInitialReviewersComments(expense, status),
    );
    const [auditedType, setAuditedType] = useState<AuditedType | undefined>();

    const methods = useForm<ChangeExpenseStatusDto>({
        resolver: classValidatorResolver(ChangeExpenseStatusDto),
        defaultValues: {
            status: status,
            reviewer_comment:
                isOtherField(expense.reviewer_comment_type) && expense.reviewerComment
                    ? expense.reviewerComment
                    : getReviewerCommentByStatus(status, expense, expense.reviewer_comment_type),
            reviewer_comment_type: getReviewerCommentByStatus(
                status,
                expense,
                expense.reviewer_comment_type,
            ),
            approved_amount: expense.approvedAmount
                ? expense.approvedAmount.toFixed(2)
                : ((!expense.declinedAmount && expense.submittedAmount) || 0).toFixed(2),
            declined_amount: expense.declinedAmount ? expense.declinedAmount.toFixed(2) : '0.00',
        },
    });

    const reviewerCommentType = methods.watch('reviewer_comment_type');

    // Check if the target status is the same as the current expense status
    const isSameStatus = status === toRawStatusMap[expense.status];

    const onSubmit = async (fields) => {
        if (!isOtherField(reviewerCommentType) && status) {
            fields.reviewer_comment = getReviewerCommentValueByStatus(
                status,
                reviewerCommentType,
                auditedType,
            );
        }
        if (status !== RawExpenseStatus.AUDITED) {
            delete fields.approved_amount;
        }
        if (
            fields.status === RawExpenseStatus.AUDITED &&
            +(+fields.approved_amount + +fields.declined_amount).toFixed(2) !==
                expense?.submittedAmount
        ) {
            methods.setError('approved_amount', {
                message: 'Ensure that both fields are filled out',
            });
            methods.setError('declined_amount', {
                message: 'Ensure that both fields are filled out',
            });
            return;
        }
        try {
            delete fields.declined_amount;
            await update({ id: expense.id, dto: fields });
            if (status === RawExpenseStatus.AUDITED) {
                FlashMessages.success('Exchange audited');
            }
            if (status === RawExpenseStatus.IN_REVIEW) {
                FlashMessages.success('Exchange in review');
            }
            onSuccess();
            onClose();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to update');
        }
    };

    const handleApproveAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        methods.clearErrors();
        const amountToNumber = parseFloat(value);

        if (amountToNumber <= 0) {
            methods.setValue('approved_amount', '0.00');
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }
        if (amountToNumber >= expense.submittedAmount) {
            methods.setValue('approved_amount', expense.submittedAmount.toFixed(2));
            methods.setValue('declined_amount', '0.00');
            setReviewersComments(getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.AUDITED));
            handleReviewersCommentsChange();
            setCanChange(false);
            return;
        }
        methods.setValue('approved_amount', value);
        methods.setValue('declined_amount', (expense.submittedAmount - amountToNumber).toFixed(2));

        if (
            isOtherField(reviewerCommentType) &&
            expense.reviewer_comment_type === methods.getValues('reviewer_comment_type')
        ) {
            return;
        }

        if (!canChange) {
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
        }
        setCanChange(true);
    };

    const handleDeclinedAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        methods.clearErrors();
        const amountToNumber = parseFloat(value);
        if (amountToNumber >= expense.submittedAmount) {
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            methods.setValue('approved_amount', '0.00');
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }
        if (amountToNumber <= 0) {
            methods.setValue('declined_amount', '0.00');
            methods.setValue('approved_amount', expense.submittedAmount.toFixed(2));
            setReviewersComments(getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.AUDITED));
            handleReviewersCommentsChange();
            setCanChange(false);
            return;
        }

        methods.setValue('declined_amount', value);
        methods.setValue('approved_amount', (expense.submittedAmount - amountToNumber).toFixed(2));

        if (
            isOtherField(reviewerCommentType) &&
            expense.reviewer_comment_type === methods.getValues('reviewer_comment_type')
        ) {
            return;
        }

        if (!canChange) {
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setCanChange(true);
        }
    };

    const handleReviewersCommentsChange = (
        auditedType?: AuditedType,
        commentType?: ReviewersCommentsType,
    ) => {
        setAuditedType(auditedType);
        methods.setValue(
            'reviewer_comment_type',
            getReviewerCommentByStatus(RawExpenseStatus.AUDITED, expense, commentType, auditedType),
        );
    };

    // change reviewer comment after reviewer comment type is changed or auditedType
    useEffect(() => {
        if (
            (isOtherField(reviewerCommentType) &&
                expense.reviewer_comment_type !== reviewerCommentType) ||
            expense.reviewerComment === undefined
        ) {
            methods.setValue('reviewer_comment', ' ');
            return;
        }
        methods.clearErrors('approved_amount');
        methods.clearErrors('declined_amount');
    }, [reviewerCommentType, auditedType]); // eslint-disable-line react-hooks/exhaustive-deps

    const ModalHeader = () => (
        <div className="claim-expenses-details-header">
            <div className="title">
                <Flex align="center" justify="space-between" gap={8}>
                    <div className="vendor">{expense.vendor}</div>
                    <div
                        className={classNames({
                            'margin-right-40': true,
                            'font-small': true,
                            pill: true,
                            info: expense.status === ExpenseStatus.SUBMITTED,
                            warning: expense.status === ExpenseStatus.IN_REVIEW,
                            success: expense.status === ExpenseStatus.AUDITED,
                        })}
                    >
                        {expense.status}
                    </div>
                </Flex>
            </div>
            <Row gutter={[16, 24]}>
                <Col span={8} className="info-group">
                    <div className="color-grey-3 font-regular margin-bottom-8">Submitted:</div>
                    <div className="font-medium">
                        <b>{formatCurrency(expense.submittedAmount || 0)}</b>
                    </div>
                </Col>
                <Col span={8} className="info-group">
                    <div className="color-grey-3 font-regular margin-bottom-8">Approved:</div>
                    <div className="font-medium">
                        <b>{formatCurrency(expense.approvedAmount || 0)}</b>
                    </div>
                </Col>
                <Col span={8} className="info-group">
                    <div className="color-grey-3 font-regular margin-bottom-8">Declined:</div>
                    <div className="font-medium">
                        <b>{formatCurrency(expense.declinedAmount || 0)}</b>
                    </div>
                </Col>
            </Row>
        </div>
    );

    useEffect(() => {
        view(expense.id);
    }, [expense.id, view]);

    return (
        <Modal
            show={show}
            onClose={onClose}
            title={<ModalHeader />}
            className="claim-expenses-details"
            sideContent={
                <ModalNavigationWrapper
                    onPrev={() => !!onPrevExpense && onPrevExpense()}
                    onNext={() => !!onNextExpense && onNextExpense()}
                    expense={expense}
                    disableNav={disableNav}
                >
                    <ReceiptPreview photoUrl={expense.imagePath} />
                </ModalNavigationWrapper>
            }
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                {status === RawExpenseStatus.AUDITED && (
                    <Row gutter={[16, 24]}>
                        <Col span={12}>
                            <TextField
                                name="approved_amount"
                                label="Approved"
                                type="number"
                                step={0.01}
                                max={expense.submittedAmount}
                                min={0}
                                contentBefore="$"
                                disabled={isSameStatus}
                                onChange={(value) => handleApproveAmount(value)}
                            />
                        </Col>
                        <Col span={12}>
                            <TextField
                                name="declined_amount"
                                label="Declined"
                                type="number"
                                step={0.01}
                                max={expense.submittedAmount}
                                min={0}
                                contentBefore="$"
                                disabled={isSameStatus}
                                onChange={(value) => handleDeclinedAmount(value)}
                            />
                        </Col>
                    </Row>
                )}
                {status && (
                    <SelectBox
                        name="reviewer_comment_type"
                        options={reviewersComments}
                        disabled={isSameStatus}
                        onOptionChange={(option) =>
                            methods.setValue(
                                'reviewer_comment_type',
                                option.value as ReviewersCommentsType,
                            )
                        }
                    />
                )}
                {isOtherField(
                    methods.getValues('reviewer_comment_type') as ReviewersCommentsType,
                ) && (
                    <TextField
                        name="reviewer_comment"
                        label="Reviewer Comment"
                        type="textarea"
                        disabled={isSameStatus}
                    />
                )}
                {!isSameStatus && (
                    <Button
                        className="btn-primary margin-bottom-8"
                        size="large"
                        type="primary"
                        htmlType="submit"
                        loading={methods.formState.isSubmitting}
                        block
                    >
                        Save Change
                    </Button>
                )}
                {!isSameStatus && (
                    <Button
                        size="large"
                        type="link"
                        htmlType="button"
                        onClick={() => onClose()}
                        disabled={methods.formState.isSubmitting}
                        block
                    >
                        Cancel
                    </Button>
                )}
            </Form>
            <hr />
            {expense.createdBy && (
                <div className="submitter">
                    <div className="label">Submitted by:</div>
                    <div className="info-container">
                        <Avatar
                            photoUrl={expense.createdBy.profilePhoto}
                            name={expense.createdBy.name}
                            size="medium"
                            className="margin-right-12"
                        />
                        <div className="info">
                            <div className="name">{expense.createdBy.name}</div>
                            <div className="email">{expense.createdBy.email}</div>
                        </div>
                    </div>
                </div>
            )}
            <hr />
            <div className="date">
                Submitted:{' '}
                {format(convertUtcToSpecifiedTimeZone(expense.date, claim.timezone), DATE_TIME, {
                    locale: enUS,
                })}
            </div>
        </Modal>
    );
};
export default ChangeExpenseStatusModal;
