import { Input } from 'antd';
import classNames from 'classnames';
import { format } from 'date-fns';
import React, { useMemo, useState } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Controller, useFormContext } from 'react-hook-form';
import OutsideClickListener from '../OutsideClickListener';

interface Props {
    name: string;
    className?: string;
    dateFormat?: string;
    disabled?: boolean;
    label?: string;
    onChange?: (dateRange: object) => void;
    placeholder?: string;
    value?: string;
    min?: Date;
    max?: Date;
}

const DatePickerRange: React.FC<Props> = ({
    className,
    dateFormat = 'MMM dd yy',
    disabled,
    label,
    onChange,
    placeholder = 'MM dd yy ➜ MM dd yy',
    name,
    min,
    max,
}: Props) => {
    const { control, setValue, watch, getFieldState } = useFormContext();
    const [isOpen, setIsOpen] = useState(false);

    const dateRange = watch(name);

    const handleChange = (dates, e) => {
        if (e && typeof e.preventDefault === 'function') {
            e.preventDefault();
        }

        setValue(name, dates);
        !!dates[1] && !!onChange && onChange(dates);
    };

    const inputPlaceholder = useMemo(() => {
        const placeholderText = 'mm / dd / yyyy';
        const startDate = !!dateRange?.[0]
            ? format(dateRange[0], 'MM / dd / yyyy')
            : placeholderText;
        const endDate = !!dateRange?.[1] ? format(dateRange[1], 'MM / dd / yyyy') : placeholderText;

        return `${startDate} - ${endDate}`;
    }, [dateRange]);

    const fieldState = getFieldState(name);

    return (
        <div
            className={classNames(
                {
                    'datepicker-range': true,
                    datepicker: true,
                },
                className,
            )}
        >
            {label && <label>{label}</label>}

            <label htmlFor="datepicker-range" className="datepicker-range-inner">
                <div
                    className="text-field-container cursor"
                    onClick={() => setIsOpen((open) => !open)}
                >
                    <div className="text-field-container-inner">
                        <Input
                            className="datepicker-input"
                            placeholder={inputPlaceholder}
                            readOnly
                        />
                    </div>
                </div>

                <div className="datepicker-range-content">
                    <OutsideClickListener onOutsideClick={() => setIsOpen(false)}>
                        <DatePicker
                            open={isOpen}
                            id="datepicker-range"
                            disabled={disabled}
                            selectsRange={true}
                            startDate={dateRange?.[0]}
                            endDate={dateRange?.[1]}
                            dateFormat={dateFormat}
                            onChange={handleChange}
                            maxDate={max}
                            minDate={min}
                            value={dateRange}
                            placeholderText={placeholder}
                            selected={dateRange?.[1] || new Date()}
                        />
                    </OutsideClickListener>
                    <Controller
                        render={({ fieldState }) => {
                            return (
                                <>
                                    <input hidden={true} className="display-none" />
                                    {!!fieldState.error && (
                                        <div
                                            className={classNames(
                                                {
                                                    note: true,
                                                },
                                                [fieldState.error.type],
                                            )}
                                        >
                                            {fieldState.error.message}
                                        </div>
                                    )}
                                </>
                            );
                        }}
                        name="startDate"
                        control={control}
                    />
                    <Controller
                        render={({ fieldState }) => {
                            return (
                                <>
                                    <input hidden={true} className="display-none" />
                                    {!!fieldState.error && (
                                        <div
                                            className={classNames(
                                                {
                                                    note: true,
                                                },
                                                [fieldState.error.type],
                                            )}
                                        >
                                            {fieldState.error.message}
                                        </div>
                                    )}
                                </>
                            );
                        }}
                        name="endDate"
                        control={control}
                    />
                    {!!fieldState.error && (
                        <div
                            className={classNames(
                                {
                                    note: true,
                                    'flex-start': true,
                                },
                                [fieldState?.error?.type],
                            )}
                        >
                            {fieldState?.error?.message}
                        </div>
                    )}
                </div>
            </label>
        </div>
    );
};

export default DatePickerRange;
