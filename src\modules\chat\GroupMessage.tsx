import classNames from 'classnames';
import { MessageDto } from '../../store/chat/dto/MessageDto';
import { parseUrls } from '../../utils/chatMessages';
import Avatar from '../app/shared/Avatar';
import { useStoreActions } from '../../store';
import { useEffect, useState } from 'react';
import { Tooltip } from 'antd';

interface Props {
    message: MessageDto;
    isLastMessage: boolean;
    isUser: boolean;
}

const GroupMessage: React.FC<Props> = ({ message, isLastMessage, isUser }) => {
    const parsedMessage = parseUrls(message.text);
    const [photoUrl, setPhotoUrl] = useState<string | null>();

    const { getProfilePhoto } = useStoreActions((actions) => actions.chat);

    useEffect(() => {
        !isUser &&
            isLastMessage &&
            getProfilePhoto(message.meta.sender).then((photoUrl) => {
                setPhotoUrl(photoUrl);
            });
    }, [getProfilePhoto, isLastMessage, isUser, message.meta.sender, message.text]);

    return (
        <div
            className={classNames('chat-list-group-message', {
                'is-user': isUser,
            })}
            id={`message-${message.id}`}
        >
            <Tooltip
                title={message.meta.sender.userName}
                overlayClassName="sender-tooltip"
                placement="topLeft"
                // open
            >
                {!isUser && (
                    <div className="sender-avatar">
                        {isLastMessage && (
                            <Avatar
                                size="small"
                                photoUrl={photoUrl || undefined}
                                name={message.meta.sender.userName}
                            />
                        )}
                    </div>
                )}
            </Tooltip>

            <div className="content">
                <div className="content-text" dangerouslySetInnerHTML={{ __html: parsedMessage }} />
                {message.meta.attachment?.url && (
                    <div className="content-attachment-container">
                        <img
                            className="content-attachment responsive-img"
                            src={message.meta.attachment?.url}
                            height={message.meta.attachment.dimensions.height}
                            width={message.meta.attachment.dimensions.width}
                            loading="lazy"
                        />
                    </div>
                )}
            </div>
        </div>
    );
};

export default GroupMessage;
