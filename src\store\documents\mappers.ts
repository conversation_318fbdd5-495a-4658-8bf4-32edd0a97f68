import { plainToInstance } from 'class-transformer';
import { DocumentDto } from './dto/DocumentDto';
import { RawDocument } from './types/Documents';

export const mapDocument = ({ presigned_url, id, image_path }: RawDocument): DocumentDto =>
    plainToInstance(DocumentDto, {
        id,
        presignedUrl: presigned_url,
        imagePath: image_path,
    });

export const mapDocuments = (raws: RawDocument[]): DocumentDto[] =>
    raws.map((doc) => mapDocument(doc));
