import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useEffect, useState } from 'react';
import { useStoreActions } from '../../../../store';
import FlashMessages from '../../../app/FlashMessages';
import { downloadCSVFile, downloadPDFFile, ReportActions } from './utils';
import { ReportDto } from '../../../../store/report/dto/ReportDto';
import { useParams } from 'react-router-dom';

const useReportActions = (handleGetReports: any) => {
    const { reportId } = useParams();

    const [selectedReport, setSelectedReport] = useState<ReportDto>();
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [isLoading, setIsLoading] = useState<string | boolean>(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);

    const {
        getCSV,
        delete: deleteReport,
        sendToAdjuster,
        sendAnalytics,
        withdrawAnalytics,
        getReport,
    } = useStoreActions((actions) => actions.report);

    useEffect(() => {
        if (!reportId) {
            return;
        }

        getReport(reportId).then((report) => {
            setSelectedReport(report);
            setShowDetailsModal(true);
        });
    }, [getReport, reportId]);

    const handleDownloadPdf = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            if (!report.report_url) {
                getReport(report.id).then((r) => {
                    if (!r.report_url) {
                        FlashMessages.warn(
                            'PDF file is not ready yet, please try again in a few minutes.',
                        );
                        return;
                    }
                    downloadPDFFile(r.report_url);
                });
                return;
            }
            downloadPDFFile(report.report_url);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Fetch PDF');
        } finally {
            setIsLoading(false);
        }
    };
    const handleAdjusterDownloadPdf = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            if (!report.adjuster_report_url) {
                getReport(report.id).then((r) => {
                    if (!r.adjuster_report_url) {
                        FlashMessages.warn(
                            'PDF file is not ready yet, please try again in a few minutes.',
                        );
                        return;
                    }
                    downloadPDFFile(r.adjuster_report_url);
                });
                return;
            }
            downloadPDFFile(report.adjuster_report_url);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Fetch PDF');
        } finally {
            setIsLoading(false);
        }
    };

    const handleDownloadCSV = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            const csvString = await getCSV(report.id);
            downloadCSVFile(csvString);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Fetch CSV');
        } finally {
            setIsLoading(false);
        }
    };

    const handleDelete = async (report: ReportDto | undefined) => {
        setIsLoading(report?.id || true);
        try {
            if (!report) {
                throw new Error('No report to delete');
            }
            await deleteReport(report.id);
            await handleGetReports();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed Delete Report');
        } finally {
            setSelectedReport(undefined);
            setShowDeleteDialog(false);
            setIsLoading(false);
        }
    };

    const handleSendEmail = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            await sendToAdjuster(report.id);
            FlashMessages.success('Sent to Adjuster');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    const handleSendAnaltycs = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            await sendAnalytics(report.id);
            await handleGetReports();
            FlashMessages.success('');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    const handleWithdrawAnalytics = async (report: ReportDto) => {
        setIsLoading(report.id);
        try {
            await withdrawAnalytics(report.id);
            await handleGetReports();
            FlashMessages.success('');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    const handleActionClick = (item: MenuItemType | undefined, report: ReportDto) => {
        if (!item) {
            return;
        }

        switch (item.key) {
            case ReportActions.VIEW:
                setSelectedReport(report);
                setShowDetailsModal(true);
                return;
            case ReportActions.DOWNLOAD_PDF:
                handleDownloadPdf(report);
                break;
            case ReportActions.DOWNLOAD_ADJUSTER:
                handleAdjusterDownloadPdf(report);
                break;
            case ReportActions.SEND_ANALYTICS:
                handleSendAnaltycs(report);
                break;
            case ReportActions.WITHDRAW_ANALYTICS:
                handleWithdrawAnalytics(report);
                break;
            case ReportActions.DOWNLOAD_CSV:
                handleDownloadCSV(report);
                break;
            case ReportActions.DELETE:
                setSelectedReport(report);
                setShowDeleteDialog(true);
                break;
            case ReportActions.EMAIL:
                handleSendEmail(report);
                break;
        }
    };

    return {
        selectedReport,
        showDetailsModal,
        setShowDetailsModal: (show: boolean) => {
            setShowDetailsModal(show);
            !show && setSelectedReport(undefined);
        },
        onActionClick: handleActionClick,
        actionLoading: isLoading,
        showDeleteDialog,
        onDeleteCancel: () => {
            setSelectedReport(undefined);
            setShowDeleteDialog(false);
        },
        onDeleteOk: () => handleDelete(selectedReport),
    };
};
export default useReportActions;
