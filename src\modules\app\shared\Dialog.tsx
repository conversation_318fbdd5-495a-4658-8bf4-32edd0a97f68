import { <PERSON><PERSON>, Col, Row } from 'antd';
import classNames from 'classnames';
import React, { ReactNode } from 'react';

interface Props {
    onOk: VoidFunction;
    onCancel: VoidFunction;
    show: boolean;
    title?: ReactNode;
    titleClass?: string;
    subtitle?: ReactNode;
    subtitleClass?: string;
    okLabel?: string;
    cancelLabel?: string;
    className?: string;
    disabled?: boolean;
}

const Dialog: React.FC<Props> = ({
    onOk,
    onCancel,
    show,
    title = 'Are you sure?',
    subtitle = ' If you click "Yes", you will complete the action. Click "No" if you do not wish to continue.',
    okLabel = 'Yes',
    cancelLabel = 'No',
    className,
    titleClass,
    subtitleClass,
    disabled = false,
}) => {
    return (
        <div className={classNames('dialog-overlay', { open: show }, className)}>
            <div className={classNames('dialog', { open: show })}>
                <div className={classNames('dialog-title margin-bottom-16', titleClass)}>
                    {title}
                </div>
                <div className={classNames('dialog-subtitle', subtitleClass)}>{subtitle}</div>
                <hr />
                <Row>
                    <Col span={12}>
                        <Button
                            onClick={() => show && onOk()}
                            type="primary"
                            size="large"
                            className="btn-primary "
                            loading={disabled}
                            block
                        >
                            {okLabel}
                        </Button>
                    </Col>
                    <Col span={12}>
                        <Button
                            onClick={() => show && onCancel()}
                            type="link"
                            size="large"
                            disabled={disabled}
                            block
                        >
                            {cancelLabel}
                        </Button>
                    </Col>
                </Row>
            </div>
        </div>
    );
};

export default Dialog;
