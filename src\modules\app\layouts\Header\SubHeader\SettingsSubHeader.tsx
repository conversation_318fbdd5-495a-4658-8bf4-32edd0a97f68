import { Button } from 'antd';
import classNames from 'classnames';

interface Props {
    onToggleUsers: (boolean) => void;
    isUsers?: boolean;
}

const SettingsSubHeader: React.FC<Props> = ({ isUsers, onToggleUsers }) => {
    return (
        <div className="header-sub settings-page-subheader">
            <div className="flex-start">
                <div className="title">General Settings</div>
                <Button
                    size="large"
                    type={isUsers ? 'primary' : 'link'}
                    onClick={() => onToggleUsers(true)}
                    className={classNames('margin-right-8', {
                        'btn-secondary btn-tab': isUsers,
                    })}
                >
                    <b>Users</b>
                </Button>
                <Button
                    size="large"
                    className={!isUsers ? 'btn-secondary btn-tab' : undefined}
                    type={!isUsers ? 'primary' : 'link'}
                    onClick={() => onToggleUsers(false)}
                >
                    <b>Archived Claims</b>
                </Button>
            </div>
        </div>
    );
};

export default SettingsSubHeader;
