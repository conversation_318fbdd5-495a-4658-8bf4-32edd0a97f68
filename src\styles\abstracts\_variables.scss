// Font
$font-family-heading: 'League Spartan', sans-serif;
$font-family-primary: 'Mulish', sans-serif;
$font-size: 16px;
$font-size-device: 14px;
$font-weight-thin: 200;
$font-weight-light: 300;
$font-weight-regular: 400;
$font-weight-medium: 500;
$font-weight-semi-bold: 600;
$font-weight-bold: 700;
$font-weight-black: 900;
$line-height: 18px;

// Colors
$color-gray-2: #4f4f4f;
$color-gray-3: #828282;
$color-gray-4: #bdbdbd;
$color-gray-5: #e0e0e0;
$color-gray-6: #f2f2f2;

$color-primary: #f4d03f;
$color-secondary: #6bcaba;
$color-secondary-transparent: transparentize($color-secondary, 0.6);
$color-secondary-darker: #6d8b85;
$color-tertiary: #6787b7;
$color-tertiary-lighter: #f0faf8;
$color-info: #8391f7;
$color-success: #69ce91;
$color-warning: #f3a175;
$color-warning-transparent: transparentize($color-warning, 0.6);
$color-error: #f37575;

$color-background: #eff2f7;
$color-background-secondary: #fff;
$color-input-background: #f0f3f3;

$color-text: #003049;
$color-heading: #003049;
$color-link-default: #003049;
$color-link-visited: #003049;

$color-background-menu-item-selected: black;

// Margins
$margin: 40px;
$padding: 40px;

// Icons path
$icon-path: '/assets/icons/';

// Image path
$image-path: '/assets/images/';

// Retina image suffix
$image-suffix-2x: '-2x';
$image-suffix-3x: '-3x';

// Font path
$font-path: '/assets/fonts/';

// Grid
$grid-columns: 12;
$grid-column-gutter: 24px;

// Modal
$modal-sidebar-width: 500px;

// break points
$xs: '480px';
$sm: '576px';
$md: '768px';
$lg: '992px';
$xl: '1400px';
$xxl: '1600px';
