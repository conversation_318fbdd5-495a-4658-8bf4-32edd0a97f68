.sender-tooltip {
    .ant-tooltip-arrow {
        &::after {
            background-color: $color-secondary !important;
        }
    }
    .ant-tooltip-inner {
        background-color: $color-secondary !important;
        color: $color-text !important;
    }
}

.chat {
    @include flex(flex, column, nowrap, flex-start, flex-start);
    height: 100%;
    position: relative;
    width: 100%;

    &-container {
        height: 100%;
        max-width: 768px;
        margin: 0 auto;
        scrollbar-width: none !important;

        ::-webkit-scrollbar {
            display: none !important;
        }

        @include breakpoint(md) {
            width: 100%;
        }
    }

    &-input-container {
        @include flex(flex, column, nowrap, center, center);
        background-color: $color-background;
        bottom: 0;
        opacity: 1;
        padding-top: 24px;
        position: absolute;
        transition: all 0.3s ease;
        width: 100%;

        &.disabled {
            opacity: 0 !important;
        }

        &-inner {
            margin-bottom: 8px;
            position: relative;
            transition: all 0.3s ease;
            width: 100%;

            .chat-input-counter {
                position: absolute;
                right: 4px;
                bottom: -18px;
            }

            .input {
                @include flex(flex, row, nowrap, center, center);
                background-color: white;
                gap: 16px;
                padding: 16px;
                transition: all 0.3s ease;

                textarea {
                    border: none;
                    box-sizing: border-box;
                    flex-grow: 1;
                    font-size: 16px;
                    height: 21px;
                    line-height: 21px;
                    min-height: 21px;
                    max-height: 200px;
                    padding: 0;
                    resize: none;
                    transition: all 0.3s ease;
                }

                .btn {
                    border: none !important;
                    cursor: pointer;
                    height: unset;
                    line-height: 21px;
                    padding: 0 !important;
                    opacity: 1;
                    transition: all 0.3s ease;

                    &:hover {
                        background-color: white !important;
                    }

                    &-image {
                        @include flex(flex, row, nowrap, center, center);

                        &.has-image {
                            svg {
                                path {
                                    stroke: $color-secondary;
                                }
                            }
                        }

                        label {
                            margin: 0;
                        }
                    }

                    &-send {
                        cursor: pointer;

                        &.disabled {
                            background: none;

                            path {
                                stroke: $color-gray-4;
                            }
                        }

                        path {
                            stroke: $color-secondary;
                        }
                    }
                }
            }
        }
    }

    &-list {
        @include flex(flex, column, nowrap, flex-start, stretch);
        padding-bottom: 100px;
        overflow: auto;
        opacity: 1;
        transition: all 0.3s ease;
        width: 100%;

        &.is-loading {
            opacity: 0;

            .chat-list-group-message {
                opacity: 0;
            }
        }

        &-group {
            &-message {
                @include flex(flex, row, nowrap, flex-start, stretch);
                margin-bottom: 8px;
                opacity: 1;
                transition: all 0.3s ease;

                &.is-user {
                    @include flex(flex, row, nowrap, flex-end, stretch);
                }

                .sender-avatar {
                    margin-right: 16px;
                    width: 40px;
                    align-self: flex-end;
                }

                .content {
                    @include flex(flex, column, nowrap, flex-start, flex-start);
                    background-color: $color_background_secondary;
                    max-width: 450px;
                    padding: 16px;
                    white-space: pre-wrap;
                    word-break: break-all;
                    word-wrap: break-word;

                    &-attachment {
                        margin-top: 8px;
                    }
                }
            }

            &-timestamp {
                color: $color-gray-3;
                font-size: 12px;
                line-height: $line-height;

                &.is-user {
                    text-align: right;
                }
            }
        }

        &-timestamp {
            color: $color-gray-3;
            font-size: 12px;
            line-height: 18px;
            margin-bottom: 24px;
            text-align: center;
        }
    }

    &-loader {
        @include flex(flex, row, nowrap, center, center);
        width: 100%;
    }
}
