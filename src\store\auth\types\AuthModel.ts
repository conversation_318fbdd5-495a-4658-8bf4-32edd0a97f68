import { Action, Computed, Thunk } from 'easy-peasy';

import { AuthData } from './LoginData';
import UserDto from '../../user/dto/UserDto';
import { LoginDto } from '../dto/LoginFormDto';
import { SetupPasswordDto } from '../dto/SetupPasswordDto';
import { UserRole } from '../../user/types/User';
import ResetPasswordDto from '../dto/ResetPasswordDto';

export default interface AuthModel {
    // state
    accessToken: string | null;
    refreshToken: string | null;
    expiresAt: Date | null;
    _retry: boolean;
    user?: UserDto | null;

    // computed
    isAuthenticated: Computed<AuthModel, boolean>;
    userRole: Computed<AuthModel, UserRole | null>;

    // actions
    load: Action<AuthModel, AuthData>;
    loadAuthUser: Action<AuthModel, UserDto>;
    unload: Action<AuthModel>;
    retry: Action<AuthModel, boolean>;

    // thunks
    requestLogin: Thunk<AuthModel, LoginDto>;
    activate: Thunk<AuthModel, { token: string; dto: SetupPasswordDto }>;
    login: Thunk<AuthModel, LoginDto>;
    refresh: Thunk<AuthModel>;
    logout: Thunk<AuthModel>;
    forgotPassword: Thunk<AuthModel, string>;
    resetPassword: Thunk<AuthModel, ResetPasswordDto>;
}
