import { Input } from 'antd';
import classNames from 'classnames';
import { Controller, useFormContext } from 'react-hook-form';
import { cleanFormattedPhoneNumber } from '../../../../utils/phoneFormat';

interface Props {
    name: string;
    label?: string;
    value?: string;
    placeholder?: string;
    disabled?: boolean;
    className?: string;
    readonly?: boolean;
    required?: boolean;
}

const PhoneField: React.FC<Props> = ({
    name,
    label,
    value,
    placeholder,
    readonly,
    disabled,
    className,
    required,
}) => {
    const { control, getValues, setValue } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState, formState }) => (
                <div className={classNames({ ['text-field-container']: true, className })}>
                    {label && <label htmlFor={name}>{label}</label>}
                    <Input
                        {...field}
                        disabled={disabled || formState.isSubmitting}
                        placeholder={placeholder}
                        readOnly={readonly}
                        value={value || getValues(name)}
                        onChange={(e) => setValue(name, cleanFormattedPhoneNumber(e.target.value))}
                        status={fieldState.error ? 'error' : undefined}
                        required={required}
                    />
                    {!!fieldState.error && (
                        <div
                            className={classNames(
                                {
                                    note: true,
                                },
                                [fieldState.error.type],
                            )}
                        >
                            {fieldState.error.message}
                        </div>
                    )}
                </div>
            )}
        />
    );
};
export default PhoneField;
