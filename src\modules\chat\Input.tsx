import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import classNames from 'classnames';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ReactComponent as IconSend } from '../../assets/icons/send-outline.svg';
import { useStoreActions, useStoreState } from '../../store';
import { SendMessageDto } from '../../store/chat/dto/MessageDto';
import { AttachmentMetaDto } from '../../store/chat/dto/MessageMetadataDto';
import { ImageDimensions } from '../../utils/imageUpload';
import FlashMessages from '../app/FlashMessages';
import ProgressBar from '../app/shared/ProgressBar';
import Form from '../app/shared/form/Form';
import UploadImageButton from './UploadImageButton';
import { useChat } from './context/ChatContext';
import { isOpen } from '../../common/claim';
import { MAX_MESSAGE_LENGTH } from '../../store/chat/utils';

interface Props {
    disabled?: boolean;
}

const Input: React.FC<Props> = ({ disabled }) => {
    const [uploadError, setUploadError] = useState(false);
    const [placeholder, setPlaceholder] = useState<string>();

    const textAreaRef = useRef<HTMLTextAreaElement | null>(null);

    const { user } = useStoreState((state) => state.auth);

    const { sendMessage, claim } = useChat();

    const { uploadProgress } = useStoreState((state) => state.documents);
    const { resetUploadProgress } = useStoreActions((state) => state.documents);

    const methods = useForm<SendMessageDto>({
        resolver: classValidatorResolver(SendMessageDto),
        defaultValues: {
            text: '',
            meta: {
                sender: {
                    userId: user?.id,
                    userName: user?.name,
                    profilePhotoUrl: user?.profilePhoto,
                    role: user?.role,
                },
            },
        },
    });

    const attachment = methods.watch('meta.attachment');
    const text = methods.watch('text');

    useEffect(() => {
        if (!text) {
            handleResizeTextarea(true);
        }
    }, [text]);

    const canSend = useMemo(() => user && (text.trim() || attachment), [user, text, attachment]);

    const onSubmit = async (fields) => {
        if (!fields.text && attachment) {
            fields.text = `${fields.meta.sender.userName} sent a photo`;
        }
        await sendMessage(fields);
        methods.reset();

        setUploadError(false);
        resetUploadProgress();
        handleResizeTextarea(true);
        setPlaceholder(undefined);
        methods.setFocus('text');
        setTimeout(() => textAreaRef.current?.focus(), 100);
    };

    const handlePressEnter = (event) => {
        if (!canSend) {
            return;
        }
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            methods.handleSubmit(onSubmit)();
        }
    };

    const handleResizeTextarea = (reset = false) => {
        if (!textAreaRef.current) return;

        const textarea = textAreaRef.current;

        textarea.style.height = '21px';
        if (!reset) {
            textarea.style.height = `${textarea.scrollHeight}px`;
        }
    };

    const onUploadSuccess = (imagePath: string, dimensions: ImageDimensions) => {
        setUploadError(false);

        const attachment: AttachmentMetaDto = {
            url: imagePath,
            dimensions,
        };

        setPlaceholder('Image is ready to send..');

        methods.setValue('meta.attachment', attachment);
    };
    const onUploadError = (message?: string) => {
        FlashMessages.error(message || 'Failed to prepare image');
        setUploadError(true);
    };

    const { ref, ...rest } = methods.register('text');

    return (
        <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
            <div
                className={classNames('chat-input-container ', {
                    disabled: disabled,
                })}
            >
                {!claim.archivedAt && isOpen(claim) ? (
                    <div className="chat-input-container-inner">
                        <div
                            className={classNames('input', {
                                disabled: claim.archivedAt || !isOpen(claim) || disabled,
                            })}
                        >
                            <div className="chat-input-counter">
                                {text.length} / {MAX_MESSAGE_LENGTH}
                            </div>
                            <>
                                <textarea
                                    {...rest}
                                    name="text"
                                    ref={(e) => {
                                        if (textAreaRef) textAreaRef.current = e;
                                        ref(e);
                                    }}
                                    onChange={(e) => {
                                        methods.setValue('text', e.target.value);
                                        handleResizeTextarea();
                                    }}
                                    onKeyDown={handlePressEnter}
                                    disabled={methods.formState.isSubmitting || disabled}
                                    placeholder={placeholder}
                                    maxLength={MAX_MESSAGE_LENGTH}
                                />
                                <UploadImageButton
                                    onSuccess={onUploadSuccess}
                                    onError={onUploadError}
                                    disabled={methods.formState.isSubmitting || disabled}
                                />
                                <Button
                                    type="link"
                                    className={classNames('btn btn-send', { disabled: !canSend })}
                                    disabled={
                                        !canSend || methods.formState.isSubmitting || disabled
                                    }
                                    htmlType="submit"
                                >
                                    <IconSend />
                                </Button>
                            </>
                        </div>
                        <ProgressBar percent={uploadProgress} error={uploadError} />
                    </div>
                ) : (
                    <span className="margin-bottom-32">
                        Chat is Disabled, You Can See Prevous Messages
                    </span>
                )}
            </div>
        </Form>
    );
};

export default Input;
