import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../store';
import { UserCreateDto } from '../../store/user/dto/UserDto';
import { RawUserRole } from '../../store/user/types/User';
import FlashMessages from '../app/FlashMessages';
import Modal from '../app/shared/Modal';
import Form from '../app/shared/form/Form';
import SelectBox from '../app/shared/form/SelectBox';
import TextField from '../app/shared/form/TextField';
import { PreferedContactMethods, RoleOptions, Timezones } from './utils';

interface Props {
    onSuccess: VoidFunction;
    onClose: VoidFunction;
    show: boolean;
}

const UserFormModal: React.FC<Props> = ({ onClose, show, onSuccess }) => {
    const [isDisabled, setIsDisabled] = useState(false);

    const { create } = useStoreActions((actions) => actions.user);

    const methods = useForm<UserCreateDto>({
        resolver: classValidatorResolver(UserCreateDto),
        defaultValues: {
            phone: '',
            timezone: (Timezones[0].value as string).toString(),
        },
    });

    const onSubmit = async (fields) => {
        setIsDisabled(true);
        try {
            await create(fields);
            onSuccess();
        } catch (err: any) {
            FlashMessages.error('Failed to add user');
            console.error(err.message);
            onClose();
        } finally {
            setIsDisabled(false);
        }
    };

    const role = methods.watch('role');

    return (
        <Modal
            title="Personal Information"
            subtitle='Complete the account details and click "Add User" to create a new account.'
            show={show}
            onClose={() => onClose()}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <SelectBox label="Type" name="role" options={RoleOptions} />
                <TextField disabled={isDisabled} name="name" label="Full Name" />
                <TextField disabled={isDisabled} type="email" name="email" label="Email" />
                <TextField disabled={isDisabled} type="text" name="phone" label="Phone Number" />
                <SelectBox
                    options={PreferedContactMethods}
                    disabled={isDisabled}
                    name="preferred_contact_method"
                    label="Preferred Method of Contact"
                />
                {role === RawUserRole.ADJUSTER && (
                    <>
                        <TextField disabled={isDisabled} name="company" label="Company" />
                        <TextField disabled={isDisabled} name="job_title" label="Title" />
                    </>
                )}
                <SelectBox
                    options={Timezones}
                    disabled={isDisabled}
                    name="timezone"
                    label="Time zone"
                />
                <Button
                    loading={methods.formState.isSubmitting}
                    className="btn-primary"
                    htmlType="submit"
                    size="large"
                >
                    Add User
                </Button>
            </Form>
        </Modal>
    );
};
export default UserFormModal;
