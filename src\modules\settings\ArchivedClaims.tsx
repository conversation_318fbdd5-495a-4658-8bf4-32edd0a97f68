import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useStoreActions, useStoreState } from '../../store';
import { PaginationDirection } from '../../utils/paginate';
import ArchivedClaimList from './ArchivedClaimsList';
import Pagination from '../app/shared/Pagination';
import Search from '../app/shared/Search';
import { Col, Row } from 'antd';
import { ClaimsOrderBy } from '../../store/claim/types/Claim';

export const ArchivedClaims: React.FC = () => {
    const params = useParams();
    const navigate = useNavigate();

    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [searchTerm, setSearchTerm] = useState('');
    const [sortDirection, setSortDirection] = useState(PaginationDirection.DESC);

    const { list, pagination } = useStoreState((state) => state.claim);
    const { get, unload } = useStoreActions((actions) => actions.claim);

    const handleGetClaims = useCallback(() => {
        get({
            direction: sortDirection,
            search_term: searchTerm,
            page: currentPage,
            order_by: ClaimsOrderBy.ARCHIVED_AT,
            params: [
                {
                    field: 'archived_at',
                    operator: 'is not null',
                    value: '',
                },
            ],
        });
    }, [currentPage, get, searchTerm, sortDirection]);

    useEffect(() => {
        handleGetClaims();

        return () => {
            unload();
        };
    }, [handleGetClaims, unload]);

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);

        navigate(`/settings/archived-claims/page/${page}`);
    };

    const onSearch = (searchTerm: string) => {
        setCurrentPage(1);
        setSearchTerm(searchTerm);
        navigate(`/settings/archived-claims`);
    };

    return (
        <>
            <div className="box settings-page-main">
                <div className="flex-space">
                    <div className="info">
                        <div className="info-title">Archived Claims</div>
                        <div className="info-subtitle">
                            View and search the list of archived claims.
                        </div>
                    </div>
                </div>
                <Row>
                    <Col span={8}>
                        <Search onSearch={onSearch} />
                    </Col>
                </Row>
                <ArchivedClaimList
                    list={list}
                    sortDirection={sortDirection}
                    onToggleSortDirection={() =>
                        setSortDirection((dir) =>
                            dir === PaginationDirection.DESC
                                ? PaginationDirection.ASC
                                : PaginationDirection.DESC,
                        )
                    }
                />
                {pagination && <Pagination {...pagination} onChange={handlePaginationChange} />}
            </div>
        </>
    );
};
