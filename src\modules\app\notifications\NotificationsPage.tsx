import { Typography } from 'antd';

import MainLayout from '../layouts/MainLayout';
import withAuth from '../../../hooks/withAuth';
import NotificationsSubHeader from '../layouts/Header/SubHeader/NotificationsSubHeader';

const { Title } = Typography;

const NotificationsPage: React.FC = () => {
    return (
        <MainLayout SubHeaderComponent={<NotificationsSubHeader />}>
            <Title>Notifications Page</Title>
        </MainLayout>
    );
};

export default withAuth(NotificationsPage);
