import { action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { PaginationDirection, mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapClaim, mapClaims, mapExpenseRanges, mapMetadata } from './mappers';
import ClaimModel from './types/ClaimModel';

const claimModel: ClaimModel = {
    // state
    claim: undefined,
    latestClaim: undefined,
    list: transition.reset([]),
    pagination: null,

    // actions
    load: action((state, claims) => {
        state.list = transition.loaded(claims);
    }),

    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    loadPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    setClaim: action((state, claim) => {
        state.claim = claim;
    }),

    unloadClaim: action((state) => {
        state.claim = undefined;
    }),

    // thunks
    get: thunk(async (actions, query) => {
        query.direction = query?.direction || PaginationDirection.DESC;

        actions.loading();
        const {
            data: { data, meta },
        } = await api.get('/claims', {
            params: query,
        });

        actions.load(mapClaims(data));
        actions.loadPagination(mapPagination(meta));
    }),

    getClaim: thunk(async (actions, claimId) => {
        const {
            data: { data },
        } = await api.get(`/claims/${claimId}`);
        actions.setClaim(mapClaim(data));
        return mapClaim(data);
    }),

    add: thunk(async (actions, id) => {
        await api.post(`/claims`, { id });
    }),

    inviteAll: thunk(async (actions, { id, dto }) => {
        await api.post(`/claims/${id}/invite/all`, dto);
    }),

    invitePolicyholder: thunk(async (actions, id) => {
        await api.get(`/claims/${id}/invite/policyholder`);
    }),

    close: thunk(async (actions, id) => {
        await api.post(`/claims/${id}/close`);
    }),

    reopen: thunk(async (actions, id) => {
        await api.post(`/claims/${id}/reopen`);
    }),

    archive: thunk(async (actions, id) => {
        await api.post(`/claims/archive`, {
            ids: [id],
        });
    }),

    getReportHtml: thunk(async (actions, id) => {
        const res = await api.get(`/reports/${id}/html`);

        return res.data.data.html;
    }),

    getInvoiceHtml: thunk(async (actions, id) => {
        const res = await api.get(`/invoices/${id}/html`);

        return res.data.data.html;
    }),

    sendReminder: thunk(async (actions, id) => {
        return api.get(`/claims/${id}/reminder`);
    }),

    getMetadata: thunk(async (actions, claimId) => {
        const res = await api.get(`/claims/${claimId}/metadata`);
        return mapMetadata(res.data.data);
    }),

    getExpenseRanges: thunk(async (actions, claimId) => {
        const {
            data: { data },
        } = await api.get(`/claims/${claimId}/ranges`);

        return mapExpenseRanges(data);
    }),
};
export default claimModel;
