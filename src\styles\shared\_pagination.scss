.pagination {
    @include flex(flex, row, nowrap, center, center);
    color: $color-secondary-darker;
    font-size: 14px;
    font-weight: $font-weight-semi-bold;
    gap: 8px;
    line-height: 18px;

    .ant-pagination-item-active {
        border-bottom: 1px solid $color-text !important;

        a {
            color: $color-text !important;
        }
    }

    &-container {
        padding: 16px 40px;
        background-color: white;

        &.simple {
            padding-right: 0;
            padding-left: 0;
        }
    }

    li {
        border: 0 !important;
        border-radius: 0 !important;
        margin: 0 !important;

        a {
            color: $color-secondary-darker !important;
            text-decoration: none !important;
        }
    }

    &-next,
    &-prev {
        color: $color-gray-3;
        cursor: pointer;
        font-weight: $font-weight-semi-bold;
        gap: 8px;
    }

    &-prev {
        svg {
            transform: rotate(180deg);
        }
    }
}
