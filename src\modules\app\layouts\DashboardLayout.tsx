import { Layout } from 'antd';
import { Header } from './Header';
import { Sider } from './Sider';
import classNames from 'classnames';
import { canAccess } from '../../../common/canAccess';
import { UserRole } from '../../../store/user/types/User';
import { useLocation } from 'react-router-dom';
import { useEffect, useRef } from 'react';

const { Content } = Layout;

interface Props {
    children: React.ReactNode;
    SubHeaderComponent?: React.ReactNode;
}

const DashboardLayout: React.FC<Props> = ({ children, SubHeaderComponent }) => {
    const hasSider = canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
    const contentRef = useRef<HTMLDivElement>(null);

    const { pathname } = useLocation();

    useEffect(() => {
        contentRef.current?.scrollTo(0, 0);
    }, [pathname]);

    return (
        <Layout className="dashboard-layout">
            <Header SubHeaderComponent={SubHeaderComponent} />
            <Layout hasSider className="dashboard-layout-inner">
                {hasSider && <Sider />}

                <Layout
                    ref={contentRef}
                    className={classNames('dashboard-layout-content-wrapper', {
                        'has-side': hasSider,
                    })}
                >
                    <Content className="dashboard-layout-content">{children}</Content>
                </Layout>
            </Layout>
        </Layout>
    );
};

export default DashboardLayout;
