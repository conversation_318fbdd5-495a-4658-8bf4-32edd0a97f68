import { Action, Thunk } from 'easy-peasy';
import { Resource, ResourcePagination } from '../../app/types';
import PolicyHolderDto from '../dto/PolicyHolderDto';

export default interface PolicyHolderModel {
    // state
    list: Resource<PolicyHolderDto[]>;
    pagination: ResourcePagination | null;

    // actions
    load: Action<PolicyHolderModel, PolicyHolderDto[]>;
    loading: Action<PolicyHolderModel>;
    setPagination: Action<PolicyHolderModel, ResourcePagination | null>;
    unload: Action<PolicyHolderModel>;

    // thunk
    getPolicyHolder: Thunk<PolicyHolderModel, string>;
    get: Thunk<PolicyHolderModel, any>;
    deactivate: Thunk<PolicyHolderModel, string>;
    activate: Thunk<PolicyHolderModel, string>;
}
