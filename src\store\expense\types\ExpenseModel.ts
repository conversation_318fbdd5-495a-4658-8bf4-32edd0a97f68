import { Action, Thunk } from 'easy-peasy';
import { Resource } from '../../../types';
import { ResourcePagination } from '../../app/types';
import { ExpenseAddDto, ExpenseDto, ExpensePaginateDto, ExpenseUpdateDto } from '../dto/ExpenseDto';

export default interface ExpenseModel {
    // state
    list: Resource<ExpenseDto[]>;
    pagination: ResourcePagination | null;

    // actions
    load: Action<ExpenseModel, ExpenseDto[]>;
    loading: Action<ExpenseModel>;
    unload: Action<ExpenseModel>;
    setPagination: Action<ExpenseModel, ResourcePagination | null>;

    // thunks
    get: Thunk<ExpenseModel, { claimId: string; dto: ExpensePaginateDto }>;
    getExpense: Thunk<ExpenseModel, string>;
    add: Thunk<ExpenseModel, ExpenseAddDto>;
    update: Thunk<ExpenseModel, { id: string; dto: Partial<ExpenseUpdateDto> }>;
    view: Thunk<ExpenseModel, string>;
    archive: Thunk<ExpenseModel, string[]>;
    restore: Thunk<ExpenseModel, string[]>;
    requestReceipt: Thunk<ExpenseModel, string>;
}
