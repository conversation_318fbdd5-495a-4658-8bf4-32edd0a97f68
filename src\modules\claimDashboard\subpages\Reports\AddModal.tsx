import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Row } from 'antd';
import { plainToInstance } from 'class-transformer';
import { endOfDay, endOfToday, format, parseISO, startOfDay } from 'date-fns';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { toRawStatusMap } from '../../../../store/expense/mappers';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import {
    ReportCreateDto,
    ReportCreateFormDto,
    ReportDto,
} from '../../../../store/report/dto/ReportDto';
import { ExpenseCategory } from '../../../../types';
import { API_DATE_TIME_FORMAT, convertLocalDateToUTC } from '../../../../utils/dateFormat';
import FlashMessages from '../../../app/FlashMessages';
import Modal from '../../../app/shared/Modal';
import Spinner from '../../../app/shared/Spinner';
import TransformContainer from '../../../app/shared/TransformContainer';
import DatePickerRange from '../../../app/shared/form/DatePickerRange';
import Form from '../../../app/shared/form/Form';
import MultiSelectBox, { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';
import { ExpenseStatus } from '../Expenses/utils';
import ReportDetails from './Details';
import { ExpenseCategoryOptions } from './utils';

const ExpenseStatusOptions: SelectBoxOption[] = Object.values(ExpenseStatus).map((status) => ({
    label: status,
    value: toRawStatusMap[status],
}));

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
    claim: ClaimDto;
}

const AddModal: React.FC<Props> = ({ show, onClose, onSuccess, claim }) => {
    const [isFirstStep, setIsFirstStep] = useState<boolean>(true);
    const [creatingReport, setCreatingReport] = useState<boolean>(false);
    const [reportHtml, setReportHtml] = useState<string>();
    const [tempReport, setTempReport] = useState<ReportDto>();
    const [sendAdjuster, setSendAdjuster] = useState(false);
    const [tempFormFields, setTempFormFields] = useState<ReportCreateDto>();

    const { add, sendToAdjuster, getReportPreview } = useStoreActions((actions) => actions.report);

    // Default values
    const defaultStartDate = startOfDay(parseISO(claim.lossDate));
    const defaultEndDate = endOfToday();
    const methods = useForm<ReportCreateFormDto>({
        resolver: classValidatorResolver(ReportCreateFormDto),
        defaultValues: {
            claim_id: claim.id,
            dateRange: [defaultStartDate, defaultEndDate],
        },
    });

    const firstStepSubtitle = `When you've selected your desired filters, click "Generate Report" to create a new report. The report will show only data matching your selected criteria.`;
    const secondStepSubtitle =
        'Here is your report! Click "Create Report" to store it in the Reports page as a manual report. If you would like to send this to the assigned adjuster, check the box next to the name. Click "Generate New" to make a new report or to back out click "Discard"!';

    const onSubmit = async (fields) => {
        try {
            const startDate = format(startOfDay(fields.dateRange[0]), API_DATE_TIME_FORMAT);
            const endDate = format(endOfDay(fields.dateRange[1]), API_DATE_TIME_FORMAT);
            const dto = plainToInstance(ReportCreateDto, {
                start_date: startDate,
                end_date: endDate,
                status:
                    fields.status ||
                    (ExpenseStatusOptions.map((opt) => opt.value) as RawExpenseStatus[]),
                category:
                    fields.category ||
                    (ExpenseCategoryOptions.map((opt) => opt.value) as ExpenseCategory[]),
                claim_id: claim.id,
            });
            setTempFormFields(dto);
            const reportPreview: ReportDto = await getReportPreview(dto);

            if (reportPreview === null) {
                FlashMessages.error('Criteria Not Met for Any Expenses');
                return;
            }
            setReportHtml(reportPreview.html);
            setIsFirstStep(false);
            setTempReport(reportPreview);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Generate Report');
        }
    };

    const { setValue } = useForm();

    const handleDateChange = (dateRange) => {
        // Since dateRange is an array of [startDate, endDate]
        const [start, end] = dateRange;
        const startDate = convertLocalDateToUTC(startOfDay(start).toISOString(), claim.timezone);
        const endDate = convertLocalDateToUTC(endOfDay(end).toISOString(), claim.timezone);
        setValue('dateRange', [startDate, endDate]);
    };

    const onCreate = async (data?: ReportCreateDto) => {
        if (!data) {
            return;
        }
        setCreatingReport(true);
        const newReport = await add(data);
        setCreatingReport(false);
        if (sendAdjuster) {
            try {
                newReport?.id && (await sendToAdjuster(newReport.id));
            } catch {
                FlashMessages.error('Failed to Send Report');
            }
        }

        onSuccess();
    };

    const onGenerateNew = async () => {
        if (!tempReport) {
            return;
        }
        try {
            setIsFirstStep(true);
            setTempReport(undefined);
        } catch (err: any) {
            FlashMessages.error('Failed to Reset');
            console.error(err.message);
        }
    };

    const onDiscard = async () => {
        onClose();
    };

    return (
        <Modal
            onClose={onClose}
            show={show}
            title="Create Report"
            className="claim-reports-add"
            subtitle={isFirstStep ? firstStepSubtitle : secondStepSubtitle}
            sideContent={
                isFirstStep ? undefined : (
                    <TransformContainer>
                        {!reportHtml && <Spinner type="skeleton" />}
                        {reportHtml && (
                            <div
                                className="claim-reports-preview"
                                dangerouslySetInnerHTML={{ __html: reportHtml }}
                            />
                        )}
                    </TransformContainer>
                )
            }
            sideContentProps={{ size: 'large' }}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                {isFirstStep && (
                    <Row>
                        <Col span={24}>
                            <DatePickerRange
                                name="dateRange"
                                label="Date Range"
                                // min={createDateWithBrowserTimezone(claim.lossDate)} // Local time for display
                                // max={new Date()} // Local time for display
                                onChange={handleDateChange} // Handle the change event
                            />
                        </Col>
                        <Col span={24}>
                            <MultiSelectBox
                                label="Category"
                                placeholder="All"
                                options={ExpenseCategoryOptions}
                                name="category"
                            />
                        </Col>

                        <Col span={24}>
                            <MultiSelectBox
                                label="Expense Status"
                                placeholder="All"
                                options={ExpenseStatusOptions}
                                name="status"
                            />
                        </Col>
                        <Col span={24}>
                            <Button
                                className="btn-primary"
                                size="large"
                                htmlType="submit"
                                loading={methods.formState.isSubmitting}
                            >
                                Generate Report
                            </Button>
                        </Col>
                    </Row>
                )}
                {!isFirstStep && tempReport && (
                    <>
                        <ReportDetails
                            report={tempReport}
                            claim={claim}
                            onToggleSend={() => setSendAdjuster(!sendAdjuster)}
                            canSend
                        />

                        <div className="actions">
                            <Button
                                className="btn-primary"
                                size="large"
                                type="primary"
                                loading={creatingReport}
                                onClick={() => onCreate(tempFormFields)}
                                block
                            >
                                Create Report
                            </Button>
                            <Button
                                className="btn-secondary"
                                size="large"
                                disabled={methods.formState.isSubmitting}
                                onClick={() => onGenerateNew()}
                                block
                            >
                                Generate New
                            </Button>
                            <Button
                                className="btn-text"
                                size="large"
                                type="link"
                                disabled={methods.formState.isSubmitting}
                                onClick={() => onDiscard()}
                                block
                            >
                                Discard
                            </Button>
                        </div>
                    </>
                )}
            </Form>
        </Modal>
    );
};
export default AddModal;
