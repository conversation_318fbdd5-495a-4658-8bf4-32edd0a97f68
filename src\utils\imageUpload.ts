export interface ImageDimensions {
    width: number;
    height: number;
}

export const getImageDimensions = (file: File): Promise<ImageDimensions> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
            const img = new Image();

            img.onload = () => {
                const dimensions: ImageDimensions = {
                    width: img.width,
                    height: img.height,
                };

                resolve(dimensions);
            };

            img.onerror = () => {
                reject(new Error('Failed to load the image.'));
            };

            img.src = event.target?.result as string;
        };

        reader.readAsDataURL(file);
    });
};
