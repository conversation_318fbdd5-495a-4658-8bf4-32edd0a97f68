import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ReportDto } from '../../../../store/report/dto/ReportDto';
import { reportStatusMap } from '../../../../store/report/type/Report';
import { UserRole } from '../../../../store/user/types/User';
import { Resource } from '../../../../types';
import ContextMenu from '../../../app/shared/ContextMenu';
import DataGrid from '../../../app/shared/DataGrid';
import Spinner from '../../../app/shared/Spinner';
import { ReportActions, adjusterActionMenuItems, adminActionMenuItems } from './utils';
import { format } from 'date-fns';
import { convertUtcToSpecifiedTimeZone } from '../../../../utils/dateFormat';

interface Props {
    list: Resource<ReportDto[]>;
    onActionClick: (item: MenuItemType | undefined, report: ReportDto) => void;
    actionLoading: boolean | string;
    claimArchived: boolean;
    claim: ClaimDto;
    notAdminRequestReportManualList: Resource<ReportDto[]>; // Updated to match the casing
}

export const List: React.FC<Props> = ({
    onActionClick,
    actionLoading: actionsLoading,
    claimArchived,
    claim,
    notAdminRequestReportManualList,
}) => {
    const columns: ColumnsType<ReportDto> = [
        {
            title: 'Date Range',
            render: (report: ReportDto) => (
                <div
                    className="flex-between cursor"
                    onClick={() => {
                        onActionClick(
                            { label: ReportActions.VIEW, key: ReportActions.VIEW },
                            report,
                        );
                    }}
                >
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.startDate, claim.timezone),
                                'dd MMM',
                            )}
                        </b>
                    </span>
                    <span>
                        <b>-</b>
                    </span>
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.endDate, claim.timezone),
                                'dd MMM',
                            )}
                        </b>
                    </span>
                </div>
            ),
        },
        {
            title: 'Number of All Expenses',
            render: ({ expensesCount }: ReportDto) => <b>{expensesCount}</b>,
        },
        {
            title: 'Type',
            render: ({ type }: ReportDto) => <span>{type}</span>,
        },
        {
            title: 'Status',
            align: 'center',
            width: 120,
            render: ({ status }: ReportDto) => {
                return (
                    <div
                        className={classNames(
                            'pill',
                            { primary: status === 'approved' },
                            { info: status === 'created' },
                            { warning: status === 'pending' },
                            { warning: status === 'preparing' },
                            { animated: status === 'preparing' },
                            { error: status === 'rejected' },
                        )}
                    >
                        {reportStatusMap[status]}
                    </div>
                );
            },
        },
        {
            title: 'Actions',
            width: 90,
            render: (report: ReportDto) =>
                actionsLoading === report.id ? (
                    <div className="flex-center">
                        <Spinner />
                    </div>
                ) : (
                    <div className="flex-center">
                        {!claimArchived && (
                            <ContextMenu
                                className={classNames({ disabled: report.status === 'preparing' })}
                                items={
                                    canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])
                                        ? adminActionMenuItems
                                        : adjusterActionMenuItems
                                }
                                onItemClick={(item) => onActionClick(item, report)}
                            />
                        )}
                    </div>
                ),
        },
    ];
    return <DataGrid<ReportDto> list={notAdminRequestReportManualList} columns={columns} />;
};
export default List;
