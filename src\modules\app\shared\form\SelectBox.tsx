import classNames from 'classnames';
import React, { ReactNode } from 'react';
import { useFormContext } from 'react-hook-form';
import Select, { components } from 'react-select';

export interface SelectBoxOption {
    label: string;
    value: string | number | undefined;
}

export interface SelectBoxProps {
    disabled?: boolean;
    name: string;
    label?: string;
    options: SelectBoxOption[];
    formatOptionLabel?: (label, value) => ReactNode;
    onOptionChange?: (option: SelectBoxOption) => void;
    placeholder?: string;
}

const SelectBox: React.FC<SelectBoxProps> = ({
    disabled,
    name,
    label,
    options,
    placeholder = 'Select',
}) => {
    const methods = useFormContext();

    const selectedOption: string | undefined = methods.watch(name);

    const fieldState = methods.getFieldState(name);

    return (
        <div className="selectbox-container">
            {label && <label htmlFor={name}>{label}</label>}
            <Select
                placeholder={placeholder}
                value={options.find((option) => option.value === selectedOption) || null}
                className="selectbox"
                isSearchable={false}
                options={options}
                onChange={(selected) => {
                    methods.setValue(name, selected?.value);
                    methods.trigger(name);
                }}
                hideSelectedOptions={false}
                tabSelectsValue={false}
                isClearable={false}
                isDisabled={disabled}
                onBlur={() => {
                    methods.trigger(name);
                }}
                components={{ Option }}
                classNames={{
                    control: () => 'selectbox-control',
                    valueContainer: () => 'selectbox-value-container',
                    dropdownIndicator: () => 'selectbox-indicator',
                    indicatorSeparator: () => 'selectbox-separator',
                    placeholder: () => 'selectbox-placeholder',
                    option: (state) =>
                        classNames({
                            'selectbox-option': true,
                            selected: state.isSelected,
                        }),
                    menu: () => 'selectbox-menu',
                    menuList: () => 'selectbox-menu-list',
                    multiValue: () => 'selectbox-multivalue',
                    multiValueLabel: () => 'selectbox-multivalue-label',
                }}
                maxMenuHeight={600}
            />
            {!!fieldState.error && (
                <div
                    className={classNames(
                        {
                            note: true,
                        },
                        [fieldState.error.type],
                    )}
                >
                    {fieldState.error.message}
                </div>
            )}
        </div>
    );
};

const Option = (props: any) => {
    return (
        <components.Option {...props}>
            <div className="selectbox-option-inner">
                <div>{props.label}</div>
            </div>
        </components.Option>
    );
};

export default SelectBox;
