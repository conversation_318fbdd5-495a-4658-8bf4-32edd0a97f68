import { <PERSON><PERSON>rray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import PaginationDto from '../../../common/PaginationDto';
import { RawExpenseStatus } from '../../expense/types/Expense';
import { ReportFilterParam, type ReportStatus, type ReportType } from '../type/Report';
import { SelectBoxOption } from '../../../modules/app/shared/form/MultiSelectBox';
import { ExpenseCategory } from '../../../types';

export class ReportDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    claimId!: string;

    @IsNotEmpty()
    @IsString()
    startDate!: string;

    @IsNotEmpty()
    @IsString()
    endDate!: string;

    @IsNotEmpty()
    expensesCount!: any;

    @IsNotEmpty()
    type!: ReportType;

    @IsNotEmpty()
    @IsString()
    status!: ReportStatus;

    @IsOptional()
    @IsNumber()
    submitted?: number;

    @IsOptional()
    @IsNumber()
    audited?: number;

    @IsOptional()
    @IsNumber()
    costSavings?: number;

    @IsOptional()
    @IsNumber()
    recommendedReimbursement?: number;

    @IsOptional()
    @IsNumber()
    inReview?: number;

    @IsOptional()
    @IsNumber()
    approved?: number;

    @IsOptional()
    @IsNumber()
    declined?: number;

    @IsNotEmpty()
    @IsArray()
    categories!: ExpenseCategory[];

    html?: string;

    @IsNotEmpty()
    @IsString()
    report_url!: string;

    @IsOptional()
    @IsString()
    adjuster_report_url!: string;

    @IsNotEmpty()
    @IsString()
    metadata!: ReportMetadataDto;
}
export class RequestReportDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    claimId!: string;

    @IsNotEmpty()
    @IsString()
    startDate!: string;

    @IsOptional()
    @IsString()
    adjuster_report_url!: string;

    @IsNotEmpty()
    @IsString()
    endDate!: string;

    @IsOptional()
    @IsString()
    approved_start_date!: string;

    @IsOptional()
    @IsString()
    approved_end_date_dateonly!: string;

    @IsOptional()
    @IsString()
    approved_start_date_dateonly!: string;

    @IsOptional()
    @IsString()
    approved_end_date!: string;

    @IsOptional()
    @IsString()
    ready_for_download_at!: string;

    @IsNotEmpty()
    expensesCount!: any;

    @IsNotEmpty()
    report_name!: any;

    @IsNotEmpty()
    type!: ReportType;

    @IsNotEmpty()
    send_analytics_to_salesforce!: ReportType;

    @IsNotEmpty()
    @IsString()
    status!: ReportStatus;

    @IsOptional()
    @IsNumber()
    submitted?: number;

    @IsOptional()
    @IsNumber()
    audited?: number;

    @IsOptional()
    @IsNumber()
    costSavings?: number;

    @IsOptional()
    @IsNumber()
    recommendedReimbursement?: number;

    @IsOptional()
    @IsNumber()
    inReview?: number;

    @IsOptional()
    @IsNumber()
    approved?: number;

    @IsOptional()
    @IsNumber()
    declined?: number;

    @IsNotEmpty()
    @IsArray()
    categories!: ExpenseCategory[];

    html?: string;

    @IsNotEmpty()
    @IsString()
    report_url!: string;

    @IsNotEmpty()
    @IsString()
    metadata!: ReportMetadataDto;
}

export class ReportMetadataDto {
    @IsOptional()
    @IsString()
    cost_savings?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_audited_amount?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_audited_count?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_in_review_amount?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_in_review_count?: number;

    @IsOptional()
    @IsNumber()
    expenses_by_status_submitted_count?: number;

    @IsOptional()
    @IsString()
    expenses_by_status_submitted_amount?: number;

    @IsOptional()
    @IsString()
    recommended_reimbursement?: number;
}

export class ReportCreateFormDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    dateRange!: [any, any];

    @IsOptional()
    @IsArray()
    status?: RawExpenseStatus[];

    @IsOptional()
    @IsArray()
    category?: ExpenseCategory[];
}
export class ReportRequestFormDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    dateRange!: [any, any];

    @IsOptional()
    @IsArray()
    status?: RawExpenseStatus[];

    @IsOptional()
    @IsArray()
    category?: ExpenseCategory[];

    @IsNotEmpty()
    approved_receipt_end_date!: any;

    @IsNotEmpty()
    approved_receipt_start_date!: any;

    @IsOptional()
    @IsString()
    previous_report_sent!: string;

    @IsOptional()
    @IsString()
    additional_comments!: string;
}

export class ReportCreateDto {
    @IsNotEmpty()
    @IsString()
    claim_id!: string;

    @IsNotEmpty()
    @IsString()
    start_date!: string;

    @IsNotEmpty()
    @IsString()
    end_date!: string;

    @IsNotEmpty()
    @IsArray()
    status!: RawExpenseStatus[];

    @IsNotEmpty()
    @IsEnum(ExpenseCategory)
    category!: ExpenseCategory;
}

export class ReportPaginateDto extends PaginationDto {
    @IsOptional()
    params?: ReportFilterParam[];
}

export class ReportFilterDto {
    @IsOptional()
    @IsArray()
    type?: string;

    @IsOptional()
    @IsArray()
    status?: SelectBoxOption[];

    @IsOptional()
    dateRange?: [any, any];
}

export class ReportApproveDto {
    @IsOptional()
    @IsString()
    note?: string;

    @IsOptional()
    @IsString()
    status!: ReportStatus;
}

export class ReportRejectDto {
    @IsNotEmpty()
    @IsString()
    note!: string;

    @IsOptional()
    @IsString()
    status!: ReportStatus;
}
