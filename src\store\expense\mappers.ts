import { plainToInstance } from 'class-transformer';
import { ExpenseStatus } from '../../modules/claimDashboard/subpages/Expenses/utils';
import { ExpenseDto } from './dto/ExpenseDto';
import {
    ExpenseCreatedBy,
    RawExpense,
    RawExpenseCreatedBy,
    RawExpenseStatus,
} from './types/Expense';

export const mapExpense = ({
    created_at,
    archived_at,
    claim_id,
    image_path,
    submitted_amount,
    approved_amount,
    declined_amount,
    status,
    reviewer_comment,
    created_by,
    ...rest
}: RawExpense): ExpenseDto =>
    plainToInstance(ExpenseDto, {
        ...rest,
        approvedAmount: approved_amount ? parseFloat(approved_amount) : undefined,
        submittedAmount: submitted_amount ? parseFloat(submitted_amount) : undefined,
        declinedAmount: declined_amount ? parseFloat(declined_amount) : undefined,
        createdAt: created_at,
        archivedAt: archived_at,
        imagePath: image_path,
        claimId: claim_id,
        status: fromRawStatusMap[status],
        reviewerComment: reviewer_comment || undefined,
        createdBy: created_by ? mapExpenseCreatedBy(created_by) : undefined,
    });

export const mapExpenses = (raws: RawExpense[]) => raws.map((raw) => mapExpense(raw));

export const fromRawStatusMap = {
    [RawExpenseStatus.AUDITED]: ExpenseStatus.AUDITED,
    [RawExpenseStatus.IN_REVIEW]: ExpenseStatus.IN_REVIEW,
    [RawExpenseStatus.SUBMITTED]: ExpenseStatus.SUBMITTED,
};

export const toRawStatusMap = {
    [ExpenseStatus.AUDITED]: RawExpenseStatus.AUDITED,
    [ExpenseStatus.IN_REVIEW]: RawExpenseStatus.IN_REVIEW,
    [ExpenseStatus.SUBMITTED]: RawExpenseStatus.SUBMITTED,
};

export const mapExpenseCreatedBy = (raw: RawExpenseCreatedBy): ExpenseCreatedBy => ({
    name: raw.full_name || raw.name || '',
    email: raw.email,
    profilePhoto: raw.profile_photo || undefined,
});
