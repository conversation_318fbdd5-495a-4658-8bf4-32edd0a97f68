import { useCallback, useEffect } from 'react';
import { useStoreActions, useStoreState } from '../../store';
import Modal from '../app/shared/Modal';
import NotificationCard from './NotificationCard';
import { PaginationDirection } from '../../utils/paginate';
import useInfiniteScroll from '../../hooks/useInfiniteScroll';
import Spinner from '../app/shared/Spinner';
import { useNavigate } from 'react-router-dom';
import { NotificationDto } from '../../store/notifications/dto/NotificationDto';
import { makeNotificationPath } from '../../store/notifications/utils';
import classNames from 'classnames';

interface Props {
    show: boolean;
    onClose: VoidFunction;
}

const NotificationsModal: React.FC<Props> = ({ show, onClose }) => {
    const navigate = useNavigate();

    const { list, hasMore, unreadCount } = useStoreState((state) => state.notification);
    const { markRead, readAll, get, unloadList } = useStoreActions(
        (actions) => actions.notification,
    );

    const handleGetNotifications = useCallback(() => {
        get({
            direction: PaginationDirection.DESC,
            order_by: 'created_at',
        });
    }, [get]);

    useEffect(() => {
        handleGetNotifications();
    }, [handleGetNotifications]);

    const [loaderRef] = useInfiniteScroll({
        loading: list.loading,
        onLoadMore: () => {
            handleGetNotifications();
        },
        hasMore: hasMore,
    });

    useEffect(() => {
        return () => {
            unloadList();
        };
    }, [unloadList]);

    const Header = (
        <div className="flex-space">
            <div>Notifications</div>
            {!!list.value.length && !!unreadCount && (
                <div
                    className={classNames('notifications-mark-read', {
                        disabled: !unreadCount || !list.value.length,
                    })}
                    onClick={() => list.value.length && unreadCount && readAll()}
                >
                    Mark all as read
                </div>
            )}
        </div>
    );

    const onNotificationClick = async (notification: NotificationDto) => {
        const path = makeNotificationPath(notification);

        !notification.readAt && (await markRead(notification.id));

        onClose();

        navigate(path);
    };

    return (
        <Modal show={show} onClose={onClose} title={Header}>
            {list.loading && !list.value.length && <Spinner type="overlay" />}
            {!list.loading && !list.value.length && (
                <div className="flex-center margin-top-32">There are no notifications.</div>
            )}
            {!!list.value.length &&
                list.value.map((notification) => (
                    <NotificationCard
                        onClick={onNotificationClick}
                        key={notification.id}
                        notification={notification}
                    />
                ))}
            {hasMore && (
                <div className="flex-center margin-top-16" ref={loaderRef}>
                    <Spinner />
                </div>
            )}
        </Modal>
    );
};
export default NotificationsModal;
