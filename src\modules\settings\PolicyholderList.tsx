import { Space } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { useMemo } from 'react';
import PolicyHolderDto from '../../store/policyHolder/dto/PolicyHolderDto';
import { PolicyholderStatus } from '../../store/policyHolder/types/PolicyHolder';
import { Resource } from '../../types';
import Avatar from '../app/shared/Avatar';
import DataGrid from '../app/shared/DataGrid';
import { UserActions } from './utils';
import { capitalizeString } from '../../utils/strings';

interface Props {
    list: Resource<PolicyHolderDto[]>;
    handleActionClick: (item: MenuItemType | undefined, user: PolicyHolderDto) => void;
}

const PolicyholderList: React.FC<Props> = ({ list, handleActionClick }) => {
    const columns: ColumnsType<PolicyHolderDto> = useMemo(() => {
        return [
            {
                key: 'id',
                title: 'Full Name',
                render: (policyholder: PolicyHolderDto) => (
                    <Space
                        className="cursor"
                        onClick={() =>
                            handleActionClick(
                                {
                                    label: 'View Profile',
                                    key: UserActions.VIEW_HOLDER,
                                },
                                policyholder,
                            )
                        }
                    >
                        <Avatar photoUrl={policyholder.profilePhoto} name={policyholder.name} />
                        <b>{policyholder.name}</b>
                    </Space>
                ),
            },
            {
                key: 'claimNumber',
                title: 'Claim Number',
                render: ({ claimNumber }: PolicyHolderDto) => <span>{claimNumber}</span>,
            },
            {
                key: 'contactMethod',
                title: 'Method of Contact',
                render: ({ contactMethod }) => (
                    <span className="contact-method">{contactMethod}</span>
                ),
            },
            { key: 'email', title: 'Email', render: ({ email }) => <span>{email}</span> },
            {
                key: 'phone',
                title: 'Phone Number',
                render: ({ phone }) => <span>{phone}</span>,
            },
            {
                align: 'center',
                key: 'status',
                title: 'Status',
                clasName: 'flex-center',
                render: ({ status }) => (
                    <div
                        className={classNames(
                            {
                                'pill primary': status === PolicyholderStatus.ACTIVE,
                            },
                            {
                                'pill inactive': status === PolicyholderStatus.INACTIVE,
                            },
                        )}
                    >
                        {capitalizeString(status)}
                    </div>
                ),
            },
        ];
    }, [handleActionClick]);

    return <DataGrid<PolicyHolderDto> columns={columns} list={list} />;
};
export default PolicyholderList;
