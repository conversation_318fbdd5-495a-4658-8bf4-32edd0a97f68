import { Navigate } from 'react-router-dom';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { UserRole } from '../../../../store/user/types/User';
import LatestExpenses from './LatestExpenses';
import LatestReports from './LatestReports';
import { QuickActions } from './QuickActions';

interface Props {
    claim: ClaimDto;
}

export const ClaimDashboard: React.FC<Props> = ({ claim }) => {
    if (!canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])) {
        return <Navigate to="/" />;
    }

    return (
        <div className="claim-dashboard">
            <QuickActions claim={claim} />
            <LatestExpenses claim={claim} />
            <LatestReports claim={claim} />
        </div>
    );
};
