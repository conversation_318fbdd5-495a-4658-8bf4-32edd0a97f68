import { FilterParamsOperator } from '../../../types';

export interface RawReport {
    id: string;
    claim_id: string;
    start_date: string;
    end_date: string;
    expenses_count: string;
    type: string;
    status: ReportStatus;
    submitted_count?: number;
    audited_count?: number;
    recommended_reimbursement?: number;
    cost_savings?: number;
    in_review_count?: number;
    approved_count?: number;
    declined_count?: number;
    expenses_category: string[];
    report_url?: string;
    html: string;
    metadata: RawReportMetadata;
}

export interface RawReportMetadata {
    cost_savings?: string;
    expenses_by_status_audited_amount?: string;
    expenses_by_status_audited_count?: number;
    expenses_by_status_in_review_amount?: string;
    expenses_by_status_in_review_count?: number;
    expenses_by_status_submitted_amount?: string;
    expenses_by_status_submitted_count?: number;
    recommended_reimbursement?: string;
}

export enum RawReportType {
    AUTOMATIC = 'adminAutomatic',
    MANUAL = 'adminManual',
}

export enum ReportType {
    AUTOMATIC = 'Automatic',
    MANUAL = 'Manual',
}

export type ReportStatus =
    | 'created'
    | 'pending'
    | 'Referenced'
    | 'approved'
    | 'rejected'
    | 'preparing';

export const reportStatusMap = {
    created: 'Ready',
    pending: 'Pending',
    approved: 'Approved',
    Referenced: 'Referenced',
    rejected: 'Rejected',
    preparing: 'Preparing',
};

export type ReportFilterableFields =
    | 'claim_id'
    | 'start_date'
    | 'end_date'
    | 'expenses_count'
    | 'type'
    | 'status'
    | 'category'
    | 'created_at';

export type ReportOrderBy =
    | 'claim_id'
    | 'start_date'
    | 'end_date'
    | 'expenses_count'
    | 'type'
    | 'status'
    | 'category';

export interface ReportFilterParam {
    field: ReportFilterableFields;
    operator: FilterParamsOperator;
    value: number | string | string[] | number[] | Date[];
}
