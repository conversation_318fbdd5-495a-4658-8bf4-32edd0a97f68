import classNames from 'classnames';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { ReactComponent as IconClose } from '../../../assets/icons/icon-close.svg';
import { ReactComponent as IconSearch } from '../../../assets/icons/search-outline.svg';
import Form from './form/Form';
import TextField from './form/TextField';

interface Props {
    onSearch: (searchTerm: string) => void;
    className?: string;
    placholder?: string;
    initialValue?: string;
    value?: string;
}

const Search: React.FC<Props> = ({
    onSearch,
    className,
    placholder = 'Search...',
    initialValue,
    value,
}) => {
    const methods = useForm({ defaultValues: { search: initialValue || '' } });

    const handleSubmit = (fields) => {
        onSearch(fields.search);
    };

    useEffect(() => {
        methods.setValue('search', value || '');
    }, [value, methods]);

    return (
        <Form methods={methods} onSubmit={methods.handleSubmit(handleSubmit)}>
            <div className={classNames('search', className)}>
                <TextField
                    className="search-input-container"
                    name="search"
                    placeholder={placholder}
                    contentAfter={
                        !methods.watch('search') ? (
                            <IconSearch className="search-icon" />
                        ) : (
                            <IconClose
                                className="search-icon"
                                onClick={() => {
                                    methods.reset();
                                    methods.handleSubmit(handleSubmit)();
                                }}
                            />
                        )
                    }
                />
            </div>
        </Form>
    );
};
export default Search;
