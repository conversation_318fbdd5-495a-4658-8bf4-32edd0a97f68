import { action, computed, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { DEFAULT_PER_PAGE, mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapUsers } from './mappers';
import UserModel from './types/UserModel';

const userModel: UserModel = {
    // state
    list: transition.reset([]),
    pagination: null,

    // computed
    hasMore: computed((state) => state.list.value.length < (state.pagination?.total || 0)),

    // actions
    load: action((state, users) => {
        state.list = transition.loaded(users);
    }),

    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    setPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    // thunks
    get: thunk(async (actions, payload) => {
        payload.page = payload.page || 1;
        payload.limit = payload.limit || DEFAULT_PER_PAGE;

        actions.loading();
        const {
            data: { data, meta },
        } = await api.get('/users', { params: payload });

        actions.load(mapUsers(data));
        actions.setPagination(mapPagination(meta));
    }),

    getAndAppend: thunk(async (actions, payload, { getState }) => {
        payload.page = payload.page || getState().pagination?.currentPage || 1;
        payload.limit = payload.limit || DEFAULT_PER_PAGE;

        actions.loading();

        const {
            data: { data, meta },
        } = await api.get('/users', {
            params: {
                ...payload,
            },
        });

        const pagination = mapPagination(meta);

        // fix for debounce issue for user search box
        if (!getState().list.loading) {
            actions.setPagination(null);
            actions.load(mapUsers(data));
            return;
        }
        actions.load(getState().list.value.concat(mapUsers(data)));

        pagination.currentPage = (getState().pagination?.currentPage || 1) + 1;
        actions.setPagination(pagination);
    }),

    create: thunk(async (actions, dto) => {
        await api.post('/users', { ...dto, document_id: null });
    }),

    update: thunk(async (actions, { id, dto }) => {
        await api.post(`/users/${id}`, dto);
    }),

    deleteUser: thunk(async (actions, id) => {
        await api.delete(`/users/${id}`);
    }),
};
export default userModel;
