import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import withAuth from '../../hooks/withAuth';
import { useStoreActions, useStoreState } from '../../store';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { ClaimsOrderBy } from '../../store/claim/types/Claim';
import { UserRole } from '../../store/user/types/User';
import { DEFAULT_PER_PAGE, PaginationDirection } from '../../utils/paginate';
import FlashMessages from '../app/FlashMessages';
import ClaimLibrarySubHeader from '../app/layouts/Header/SubHeader/ClaimLibrarySubHeader';
import MainLayout from '../app/layouts/MainLayout';
import Dialog from '../app/shared/Dialog';
import Pagination from '../app/shared/Pagination';
import ClaimList from './ClaimList';
import { ClaimActions, PathnamePrefixes } from './utils';
import { canAccess } from '../../common/canAccess';

const ClaimsLibraryPage = () => {
    const params = useParams();
    const navigate = useNavigate();
    // const { pathname } = useLocation();

    const [currentPage, setCurrentPage] = useState(
        (params.pageNumber && +params.pageNumber > 0 && +params.pageNumber) || 1,
    );
    const [searchTerm, setSearchTerm] = useState('');
    // const [myClaims, setMyClaims] = useState(
    //     pathname.includes(PathnamePrefixes.MY_CLAIMS) ? true : false,
    // );
    const [selectedTab, setSelectedTab] = useState('my_claims');

    const [selectedClaim, setSelectedClaim] = useState<ClaimDto>();
    const [showCloseDialog, setShowCloseDialog] = useState(false);
    const [loading, setLoading] = useState(false);
    const [closeDialogDisabled, setCloseDialogDisabled] = useState(false);
    const [sortDirection, setSortDirection] = useState(PaginationDirection.DESC);
    const [sortField, setSortField] = useState('Activity');
    const [showReminderDialog, setShowReminderDialog] = useState<string | false>(false);
    const [reminderDialogDisabled, setReminderDialogDisabled] = useState(false);

    const { get, close, reopen, archive, sendReminder } = useStoreActions(
        (actions) => actions.claim,
    );
    const { list, pagination } = useStoreState((state) => state.claim);

    const getClaims = useCallback(
        (
            page = 1,
            search = '',
            // myClaims = true,
            selectedTab = 'my_claims',
            limit = DEFAULT_PER_PAGE,
        ) => {
            const mapperOrderBy = {
                Activity: 'updated_at',
                'Normal Daily Expense': 'normal_daily_food_expenditure',
                'ALE Limits': 'ale_limits',
                Carrier: 'insurance_company',
                status: 'status',
                claimNumber: 'insured_claim_number',
                id: 'ph_full_name',
                'Submitted Receipts': 'submitted',
                'In Review Receipts': 'in_review',
                'Audited Receipts': 'audited',
                'OOPE Rep': 'OOPERep',
            };
            if (sortDirection && sortField) {
                get({
                    // my_claims: myClaims,
                    type: selectedTab,
                    search_term: search ? search : undefined,
                    page,
                    limit,
                    order_by: canAccess([UserRole.ADJUSTER])
                        ? ClaimsOrderBy.LOSS_DATE
                        : mapperOrderBy[sortField],
                    direction: sortDirection,
                    params: [
                        {
                            field: 'archived_at',
                            operator: 'is null',
                        },
                    ],
                });
            }
        },
        [get, sortDirection, sortField],
    );

    useEffect(() => {
        getClaims(currentPage, searchTerm, selectedTab);
    }, [currentPage, getClaims, selectedTab, searchTerm]);

    const handlePaginationChange = (page: number) => {
        setCurrentPage(page);
        const claimFilterPrefix =
            selectedTab === 'my_claims'
                ? PathnamePrefixes.MY_CLAIMS
                : selectedTab === 'all_claims'
                ? PathnamePrefixes.ALL_CLAIMS
                : PathnamePrefixes.RECENT;
        navigate(`/${claimFilterPrefix}/page/${page}`);
    };

    const onSearch = (searchTerm: string) => {
        setCurrentPage(1);
        const claimFilterPrefix =
            selectedTab === 'my_claims'
                ? PathnamePrefixes.MY_CLAIMS
                : selectedTab === 'all_claims'
                ? PathnamePrefixes.ALL_CLAIMS
                : PathnamePrefixes.RECENT;
        navigate(`/${claimFilterPrefix}`);
        setSearchTerm(searchTerm);
    };

    // const onToggleClaims = (myClaims: boolean) => {
    //     setCurrentPage(1);
    //     setSortDirection(PaginationDirection.DESC);
    //     setMyClaims(myClaims);
    //     setSearchTerm('');
    //     const claimFilterPrefix =
    //         selectedTab === 'my_claims'
    //             ? PathnamePrefixes.MY_CLAIMS
    //             : selectedTab === 'all_claims'
    //             ? PathnamePrefixes.ALL_CLAIMS
    //             : PathnamePrefixes.RECENT;
    //     navigate(`/${claimFilterPrefix}`);
    // };

    const onSelectTab = (claim: string) => {
        setCurrentPage(1);
        setSortDirection(PaginationDirection.DESC);
        // setMyClaims(myClaims);
        setSelectedTab(claim);
        setSearchTerm('');
        const claimFilterPrefix =
            selectedTab === 'my_claims'
                ? PathnamePrefixes.MY_CLAIMS
                : selectedTab === 'all_claims'
                ? PathnamePrefixes.ALL_CLAIMS
                : PathnamePrefixes.RECENT;
        navigate(`/${claimFilterPrefix}`);
    };

    const handleReopenClaim = useCallback(
        async (claim: ClaimDto) => {
            setLoading(true);
            try {
                await reopen(claim.id);
                FlashMessages.success('Claim Reopened');
                getClaims(currentPage, searchTerm, selectedTab);
            } catch (err: any) {
                console.error(err.message);
                FlashMessages.error('Failed to reopen claim');
            } finally {
                setLoading(false);
            }
        },
        [reopen, getClaims, currentPage, searchTerm, selectedTab],
    );

    const handleArchiveClaim = useCallback(
        async (claim: ClaimDto) => {
            setLoading(true);
            try {
                await archive(claim.id);
                FlashMessages.success('Claim Archived');
                getClaims(currentPage, searchTerm, selectedTab);
            } catch (err: any) {
                console.error(err.message);
                FlashMessages.error('Failed to archive claim');
            } finally {
                setLoading(false);
            }
        },
        [archive, getClaims, currentPage, searchTerm, selectedTab],
    );

    const handleActionClick = (item: MenuItemType | undefined, claim: ClaimDto) => {
        if (!item) {
            return;
        }

        switch (item.key) {
            case ClaimActions.VIEW_CLAIM:
                navigate(
                    canAccess([UserRole.ADJUSTER])
                        ? `/claim/${claim.id}/reports`
                        : `/claim/${claim.id}`,
                );
                break;
            case ClaimActions.CLOSE_CLAIM:
                setSelectedClaim(claim);
                setShowCloseDialog(true);
                break;
            case ClaimActions.SEND_MESSAGE:
                navigate(`/claim/${claim.id}/chat`);
                break;
            case ClaimActions.REOPEN_CLAIM:
                handleReopenClaim(claim);
                break;
            case ClaimActions.ARCHIVE_CLAIM:
                handleArchiveClaim(claim);
                break;
            case ClaimActions.ADD_EXPENSE:
                navigate(`/claim/${claim.id}/expenses/new`);
                break;
            case ClaimActions.EXPENSE_REMINDER:
                setShowReminderDialog(claim.id);
                break;
        }
    };

    const handleClose = async () => {
        setCloseDialogDisabled(true);
        try {
            if (!selectedClaim) throw new Error('No claim selected to close');
            await close(selectedClaim.id);
            FlashMessages.success('Claim closed');
            getClaims(currentPage, searchTerm, selectedTab);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to close claim');
        } finally {
            setShowCloseDialog(false);
            setCloseDialogDisabled(false);
        }
    };

    const handleSendReminder = async () => {
        if (!showReminderDialog) {
            return;
        }
        setReminderDialogDisabled(true);
        try {
            await sendReminder(showReminderDialog);
            FlashMessages.success('Reminder Sent.');
        } catch (err: any) {
            console.error(err);
            FlashMessages.error(err.message);
        } finally {
            setShowReminderDialog(false);
            setReminderDialogDisabled(false);
        }
    };

    return (
        <MainLayout
            SubHeaderComponent={
                <ClaimLibrarySubHeader
                    searchTerm={searchTerm}
                    onSearch={onSearch}
                    // myClaims={myClaims}
                    // onToggleClaims={onToggleClaims}
                    selectedTab={selectedTab}
                    onSelectTab={onSelectTab}
                    onAddSuccess={() => getClaims()}
                />
            }
        >
            <ClaimList
                sortDirection={sortDirection}
                onToggleSort={(sortField: string) => {
                    setSortField(sortField);
                    setSortDirection((dir) =>
                        dir === PaginationDirection.ASC
                            ? PaginationDirection.DESC
                            : PaginationDirection.ASC,
                    );
                }}
                sortField={sortField}
                loading={loading}
                list={list}
                handleActionClick={handleActionClick}
            />
            {pagination && (
                <Pagination
                    className="claim-libary-pagination"
                    {...pagination}
                    onChange={handlePaginationChange}
                />
            )}
            <Dialog
                onCancel={() => setShowCloseDialog(false)}
                onOk={() => handleClose()}
                show={showCloseDialog}
                disabled={closeDialogDisabled}
            />
            <Dialog
                onCancel={() => setShowReminderDialog(false)}
                onOk={() => handleSendReminder()}
                show={!!showReminderDialog}
                disabled={reminderDialogDisabled}
            />
        </MainLayout>
    );
};

export default withAuth(ClaimsLibraryPage, [
    UserRole.ADJUSTER,
    UserRole.ADMIN,
    UserRole.SUPER_ADMIN,
]);
