import { ColumnProps, ColumnType } from 'antd/es/table';
import { PaginationDirection } from '../utils/paginate';
import classNames from 'classnames';
import { ReactComponent as IconSort } from '../assets/icons/arrow-forward-sharp.svg';

export function getSortableColumn<T>(
    props: ColumnProps<T>,
    direction?: PaginationDirection,
): ColumnType<T> {
    if (!direction) {
        return props;
    }

    return {
        ...props,
        sortIcon: () => <IconSort className={classNames(`sort-icon cursor ${direction}`)} />,
        sorter: () => 0,
        showSorterTooltip: false,
    };
}
