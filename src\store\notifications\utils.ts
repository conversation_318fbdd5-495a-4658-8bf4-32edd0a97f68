import { NotificationDto } from './dto/NotificationDto';
import { NotificationType } from './types/Notifications';

export const makeNotificationPath = (notification: NotificationDto) => {
    const { data, type } = notification;
    const { claimId } = data;

    switch (type) {
        case NotificationType.CLAIM_INVITATION:
        case NotificationType.ADJUSTER_CHANGED:
            return `/claim/${claimId}`;
        case NotificationType.REPORT_READY:
        case NotificationType.REPORT_NOT_READY:
        case NotificationType.REPORT_APPROVED:
        case NotificationType.REPORT_REJECTED:
            return `/claim/${claimId}/reports/${data.reportId}`;
        case NotificationType.POLICYHOLDER_CONTACT_METHOD_CHANGED:
            return `/claim/${claimId}/information`;
        case NotificationType.UREAD_CHAT:
            return `/claim/${claimId}/chat`;
    }
};
