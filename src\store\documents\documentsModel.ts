import axios from 'axios';
import { action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { mapDocuments } from './mappers';
import { RawDocument } from './types/Documents';
import DocumentsModel from './types/DocumentsModel';

const documentsModel: DocumentsModel = {
    // state
    uploadProgress: 0,
    uploadMultipleProgress: {},

    //actions
    setUploadProgress: action((state, payload) => {
        state.uploadProgress = payload;
    }),

    resetUploadProgress: action((state) => {
        state.uploadProgress = 0;
    }),

    setUploadMultipleProgress: action((state, { url, progress }) => {
        state.uploadMultipleProgress[url] = progress;
    }),

    resetUploadMultipleProgress: action((state) => {
        state.uploadMultipleProgress = {};
    }),

    //thunks
    getDocuments: thunk(async (_, payload) => {
        payload.count = payload.count || 1;
        const response = await api.post<{ data: RawDocument[] }>('/documents', {
            path: payload.path,
            count: payload.count,
        });
        return mapDocuments(response.data.data);
    }),

    getDocument: thunk(async (_, id) => {
        const response = await api.get(`/documents/${id}`);
        return response.data.data;
    }),

    getDocumentAnalyze: thunk(async (_, id) => {
        try {
            const response = await api.get(`/documents/${id}/analyze`);
            return response.data.data;
        } catch {
            return undefined;
        }
    }),

    uploadFileToS3: thunk(async (actions, payload) => {
        return await axios.request({
            method: 'put',
            url: payload.presignedUrl,
            data: payload.file,
            headers: {
                'Content-Type': payload.file.type,
            },
            onUploadProgress: (p) => {
                if (!p.total) {
                    return;
                }
                actions.setUploadProgress((p.loaded / p.total) * 100);
            },
        });
    }),

    uploadMultipeToS3: thunk(async (actions, payload) => {
        const uploadPromises = payload.map(({ presignedUrl, file }) => {
            return axios.request({
                method: 'put',
                url: presignedUrl,
                data: file,
                headers: {
                    'Content-Type': file.type,
                },
                onUploadProgress: (p) => {
                    if (!p.total) {
                        return;
                    }
                    actions.setUploadMultipleProgress({
                        url: presignedUrl,
                        progress: (p.loaded / p.total) * 100,
                    });
                },
            });
        });

        return await Promise.all(uploadPromises);
    }),
};

export default documentsModel;
