import { Action, Thunk } from 'easy-peasy';
import { DocumentPathTypes } from './Documents';

export default interface DocumentsModel {
    // state
    uploadProgress: number;
    uploadMultipleProgress: { [presignedUrl: string]: number };

    //actions
    setUploadProgress: Action<DocumentsModel, number>;
    resetUploadProgress: Action<DocumentsModel>;

    setUploadMultipleProgress: Action<DocumentsModel, { url: string; progress: number }>;
    resetUploadMultipleProgress: Action<DocumentsModel>;

    //thunks
    getDocuments: Thunk<DocumentsModel, { path: DocumentPathTypes; count?: number }>;
    getDocument: Thunk<DocumentsModel, string>;
    getDocumentAnalyze: Thunk<DocumentsModel, string>;
    uploadFileToS3: Thunk<DocumentsModel, { presignedUrl: string; file: File }>;
    uploadMultipeToS3: Thunk<DocumentsModel, { presignedUrl: string; file: File }[]>;
}
