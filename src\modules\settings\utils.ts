import { fromRawRole } from '../../store/user/mappers';
import { RawUserRole } from '../../store/user/types/User';
import { ContactMethod, FilterParam } from '../../types';
import { capitalizeString } from '../../utils/strings';
import { SelectBoxOption } from '../app/shared/form/MultiSelectBox';

export type UserFilter = 'Admins' | 'Adjusters' | 'Policyholders';

export const getFilterParams = (type: UserFilter): FilterParam[] => {
    switch (type) {
        case 'Policyholders':
            return [];
        case 'Adjusters':
            return [
                {
                    field: 'role',
                    operator: '=',
                    value: RawUserRole.ADJUSTER,
                },
            ];
        case 'Admins':
            return [
                {
                    field: 'role',
                    operator: 'in',
                    value: [RawUserRole.ADMIN, RawUserRole.SUPER_ADMIN],
                },
            ];
    }
};

export enum UserActions {
    VIEW_USER = 'VIEW_USER',
    VIEW_HOLDER = 'VIEW_HOLDER',
    EDIT = 'EDIT',
    DELETE = 'DELETE',
}

export const RoleOptions: SelectBoxOption[] = Object.values(RawUserRole).map((role) => ({
    value: role,
    label: fromRawRole[role],
}));

export const Timezones: SelectBoxOption[] = ['PST', 'MST', 'CST', 'EST', 'HAST', 'AKST'].map(
    (timezone) => ({
        value: timezone,
        label: timezone,
    }),
);

export const PreferedContactMethods: SelectBoxOption[] = Object.values(ContactMethod).map(
    (method) => ({
        value: method,
        label: capitalizeString(method),
    }),
);
