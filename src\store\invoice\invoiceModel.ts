import { action, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapInvoice, mapInvoices } from './mappers';
import InvoiceModel from './type/InvoiceModel';

const invoiceModel: InvoiceModel = {
    // state
    list: transition.reset([]),
    pagination: null,

    // actions
    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    load: action((state, invoices) => {
        state.list = transition.loaded(invoices);
    }),

    setPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    // thunks
    get: thunk(async (actions, dto) => {
        actions.loading();
        const {
            data: { data, meta },
        } = await api.get('/invoices', {
            params: dto,
        });

        actions.load(mapInvoices(data));
        actions.setPagination(mapPagination(meta));
    }),

    getInvoice: thunk(async (actions, invoiceId) => {
        const {
            data: { data },
        } = await api.get(`/invoices/${invoiceId}`);

        return mapInvoice(data);
    }),

    getInvoiceRequest: thunk(async (actions, dto) => {
        const response = await api.post('/invoices/request-invoice', dto);
        if (!response.data.data) {
            return null;
        }
        return mapInvoice(response.data.data);
    }),

    getInvoicePreview: thunk(async (actions, dto) => {
        const response = await api.post('/invoices/preview', dto);
        if (!response.data.data) {
            return null;
        }
        return mapInvoice(response.data.data);
    }),

    add: thunk(async (actions, dto) => {
        const res = await api.post('/invoices', dto);
        return res.data.data ? mapInvoice(res.data.data) : null;
    }),

    delete: thunk(async (actions, invoiceId) => {
        await api.delete(`/invoices/${invoiceId}`);
    }),

    checkIfPdfReady: thunk(async (actions, invoiceId) => {
        const res = await api.get(`/invoices/${invoiceId}/ready-for-download`);
        return res.data.data;
    }),

    getPDF: thunk(async (actions, invoiceId) => {
        const res = await api.get(`/invoices/${invoiceId}/pdf`);
        return res.data.data.url;
    }),

    getCSV: thunk(async (actions, invoiceId) => {
        const res = await api.get(`/invoices/${invoiceId}/csv`);
        return res.data;
    }),

    uploadedtoQbs: thunk(async (actions, invoiceId) => {
        const res = await api.get(`/invoices/${invoiceId}/uploadedtoqbs`);
        return res.data;
    }),

    sendToAdjuster: thunk(async (actions, invoiceId) => {
        await api.post(`/invoices/${invoiceId}/send-adjuster`);
    }),

    approve: thunk(async (actions, { id: invoiceId, dto }) => {
        await api.post(`/invoices/${invoiceId}`, dto);
    }),

    reject: thunk(async (actions, { id: invoiceId, dto }) => {
        await api.post(`/invoices/${invoiceId}`, dto);
    }),
};
export default invoiceModel;
