import React, { ReactNode, createContext, useContext, useEffect, useState } from 'react';
import useNotificationListener from './hooks/useNotificationListener';
import { useStoreActions, useStoreState } from '../../store';

interface NotificationsContextState {
    showModal: boolean;
    setShowModal: (show: boolean) => void;
}

const NotificationsContext = createContext<NotificationsContextState | null>(null);

interface Props {
    children: ReactNode;
}

export const NotificationsProvider: React.FC<Props> = ({ children }) => {
    const [showModal, setShowModal] = useState(false);

    const { user: authUser } = useStoreState((state) => state.auth);
    const { unload } = useStoreActions((actions) => actions.notification);

    useNotificationListener(!showModal && !!authUser);

    useEffect(() => {
        if (!authUser) {
            unload();
        }
    }, [authUser, unload]);

    return (
        <NotificationsContext.Provider
            value={{
                showModal,
                setShowModal,
            }}
        >
            {children}
        </NotificationsContext.Provider>
    );
};

export const useNotifications = (): NotificationsContextState => {
    const context = useContext(NotificationsContext);

    if (!context) {
        throw new Error('useNotifications hook must be used within a NotificationContext');
    }

    return context;
};
