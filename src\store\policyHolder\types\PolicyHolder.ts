import { FilterParamsOperator } from '../../../types';

export interface RawPolicyHolder {
    id: string;
    full_name: string;
    email: string;
    salesforce_id: string;
    phone: string;
    status: string;
    insurance_company: string;
    preferred_contact_method: string;
    profile_photo: string;
    job_title: string;
    timezone: string;
    address: string | null;
    created_at: string;
    deactivated_at: string | null;
    terms_accepted_at: string | null;
    activation_sent_at: string | null;
    insured_claim_number: string;
}

export interface PolicyHolderFilterParam {
    field: 'full_name' | 'email' | 'status' | 'salesforce_id' | 'job_title';
    operator: FilterParamsOperator;
    value: string;
}

export enum PolicyholderStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
}
