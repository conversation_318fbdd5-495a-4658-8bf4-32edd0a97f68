import { useStoreState } from '../../../../../store';
import FileUploadCard from './FileUploadCard';
import { AnalyzeError, FileUpload } from './types';

interface Props {
    errors: AnalyzeError[];
    fileUploads: Array<FileUpload>;
    onRemoveImage: (presignedUrl: string) => void;
}

const UploadList: React.FC<Props> = ({ fileUploads, onRemoveImage, errors }) => {
    const { uploadMultipleProgress } = useStoreState((state) => state.documents);

    return (
        <div className="add-expense-upload-list">
            {fileUploads.map(({ presignedUrl, file }) => {
                const isError = errors
                    .map(({ presignedUrl }) => presignedUrl)
                    .includes(presignedUrl);

                return (
                    <FileUploadCard
                        error={!!isError}
                        presignedUrl={presignedUrl}
                        file={file}
                        progress={uploadMultipleProgress[presignedUrl]}
                        key={presignedUrl}
                        onRemove={(url) => onRemoveImage(url)}
                    />
                );
            })}
        </div>
    );
};
export default UploadList;
