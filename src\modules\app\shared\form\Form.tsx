import { ReactNode } from 'react';
import { FieldValues, FormProvider, UseFormReturn } from 'react-hook-form';
import classNames from 'classnames';

type Props<T extends FieldValues> = {
    children: ReactNode;
    className?: string;
    methods: UseFormReturn<T>;
    onSubmit?: VoidFunction;
};

const Form = <T extends FieldValues>({ children, className, onSubmit, methods }: Props<T>) => {
    return (
        <FormProvider {...methods}>
            <form
                onSubmit={onSubmit}
                className={classNames(
                    {
                        form: true,
                    },
                    className,
                )}
                autoComplete="off"
            >
                {children}
            </form>
        </FormProvider>
    );
};

export default Form;
