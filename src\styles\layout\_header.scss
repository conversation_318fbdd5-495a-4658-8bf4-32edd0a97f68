.header {
    background-color: #fff;
    height: auto;
    padding: 0;

    &-main,
    &-sub {
        background: #fff;
        border-bottom: 1px solid $color-gray-5;
        padding: 16px $padding;
    }

    &-main {
        .actions {
            &-avatar {
                line-height: 20px;
                cursor: pointer;

                &-info {
                    margin-right: 16px;
                    text-align: right;
                }

                &-picture {
                    border: none;
                }
            }

            &-icons {
                margin-right: 40px;
            }

            .unread-count {
                @include flex(flex, row, nowrap, center, center);
                background-color: $color-error;
                border-radius: 50%;
                color: white;
                cursor: pointer;
                font-size: 9px;
                font-weight: $font-weight-semi-bold;
                left: 45%;
                letter-spacing: 0;
                line-height: 18px;
                text-align: center;
                position: absolute;
                top: 7px;
                width: 18px;
                word-spacing: 0;

                &.has-many {
                    font-size: 8px;
                }
            }
        }

        .logo {
            cursor: pointer;
        }
    }
}
