import { Button, Dropdown, Space } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { useCallback, useMemo } from 'react';
import { ReactComponent as <PERSON>con<PERSON>he<PERSON> } from '../../../../assets/icons/checkmark-sharp.svg';
import { ReactComponent as IconChevron } from '../../../../assets/icons/chevron-down.svg';
import { ReactComponent as MoneyIcon } from '../../../../assets/icons/money icon.svg';
import { ReactComponent as TimePeriodIcon } from '../../../../assets/icons/time period_icon.svg';
import { getCategoryIcon } from '../../../../common/getCategoryIcon';
import { getSortableColumn } from '../../../../common/table';
import { useStoreState } from '../../../../store';
import { ExpenseDto } from '../../../../store/expense/dto/ExpenseDto';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import { UserRole } from '../../../../store/user/types/User';
import { Resource } from '../../../../types';
import { formatCurrency } from '../../../../utils/currencyFormat';
import { convertUtcToSpecifiedTimeZone, DATE_ONLY, DATE_TIME } from '../../../../utils/dateFormat';
import { PaginationDirection } from '../../../../utils/paginate';
import ContextMenu from '../../../app/shared/ContextMenu';
import DataGrid from '../../../app/shared/DataGrid';
import Spinner from '../../../app/shared/Spinner';
import {
    ExpenseActions,
    ExpenseStatus,
    actionMenuItems,
    archivedActionMenuItems,
    statusMenuItems,
} from './utils';
import { AlignType } from 'rc-table/lib/interface';
import { format } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';

interface Props {
    list: Resource<ExpenseDto[]>;
    claim: ClaimDto;
    onActionClick: (item: MenuItemType | undefined, expense: ExpenseDto) => void;
    selectedIds?: string[];
    setSelectedIds?: (ids: string[]) => void;
    onStatusChange: (status: RawExpenseStatus, expense: ExpenseDto) => void;
    isStatusLoading: string | boolean;
    readonly: boolean;
    sortDirection?: PaginationDirection;
    onToggleSort?: VoidFunction;
    enableDateSorting?: boolean;
}

export const ExpensesList: React.FC<Props> = ({
    list,
    claim,
    onActionClick,
    selectedIds,
    setSelectedIds,
    onStatusChange,
    isStatusLoading,
    readonly,
    sortDirection,
    onToggleSort,
    enableDateSorting = true,
}) => {
    const { user } = useStoreState((state) => state.auth);

    const onSelectChange = useCallback(
        (newSelectedRowKeys: React.Key[]) => {
            setSelectedIds && setSelectedIds(newSelectedRowKeys as string[]);
        },
        [setSelectedIds],
    );

    const onSelectAll = (selectAll: boolean) => {
        if (!setSelectedIds) {
            return;
        }
        selectAll ? setSelectedIds(list.value.map((e) => e.id)) : setSelectedIds([]);
    };

    const rowSelection = useMemo(
        () =>
            selectedIds
                ? {
                      selectedRowKeys: selectedIds,
                      onChange: onSelectChange,
                  }
                : undefined,
        [selectedIds, onSelectChange],
    );

    const columns = useMemo(() => {
        const actionItems =
            user?.role === UserRole.SUPER_ADMIN || (!readonly && user?.role === UserRole.ADMIN)
                ? actionMenuItems
                : actionMenuItems?.filter((item) => item?.key !== ExpenseActions.EDIT);

        let cols = [] as ColumnsType<ExpenseDto>;

        !rowSelection &&
            cols.push({
                align: 'center',
                key: 'ix',
                title: '#',
                render: (_, __, index) => <div>{index + 1}</div>,
            });

        cols = cols.concat([
            {
                align: 'center ' as AlignType,
                key: 'category',
                title: 'Category',
                render: ({ category }) => (
                    <div className="flex-center claim-expenses-list-category">
                        {getCategoryIcon(category)}
                    </div>
                ),
            },
            {
                key: 'vendor',
                title: 'Vendor Name',
                render: (expense: ExpenseDto) => {
                    return (
                        <span
                            className="cursor"
                            onClick={() => onActionClick({ key: ExpenseActions.VIEW }, expense)}
                        >
                            <b>
                                {expense.vendor}{' '}
                                {/* {new Date(claim.approvedReceiptEndDate ?? '').toLocaleDateString() >
                                    new Date(expense.date ?? '').toLocaleDateString() &&
                                    claim.approvedReceiptEndDate && (
                                        <span className="claim-expenses-error claim-expenses-center">
                                            •
                                        </span>
                                    )} */}
                            </b>
                        </span>
                    );
                },
            },
            {
                key: 'attention',
                title: 'Attention',
                render: (expense: ExpenseDto) => (
                    <div className="claim-expenses-error claim-expenses-center">
                        <b>
                            {(claim.excessiveSpending === '$100>' ||
                                claim.excessiveSpending?.toLowerCase() === 'yes') &&
                                (expense?.submittedAmount ?? 0) >= 100 && (
                                    <>
                                        <MoneyIcon
                                            className="claim-expenses-image"
                                            width={24}
                                            height={24}
                                            viewBox="0 0 2084 2084"
                                            style={{ display: 'block' }}
                                        />{' '}
                                        {/* Excessive Spending */}
                                    </>
                                )}
                            {claim.approvedReceiptEndDate &&
                                (() => {
                                    // Convert expense date to local time zone and get YYYY-MM-DD
                                    const expenseDateStr = new Date(
                                        format(
                                            convertUtcToSpecifiedTimeZone(
                                                expense.date,
                                                claim.timezone,
                                            ),
                                            DATE_ONLY,
                                            { locale: enUS },
                                        ),
                                    )
                                        .toISOString()
                                        .split('T')[0];
                                    const approvedStartDateStr = claim.approvedReceiptStartDate
                                        ? new Date(claim.approvedReceiptStartDate)
                                              .toISOString()
                                              .split('T')[0]
                                        : null;
                                    const approvedEndDateStr = claim.approvedReceiptEndDate
                                        ? new Date(claim.approvedReceiptEndDate)
                                              .toISOString()
                                              .split('T')[0]
                                        : null;
                                    // Compare purely by date strings
                                    if (
                                        (approvedStartDateStr &&
                                            expenseDateStr < approvedStartDateStr) ||
                                        (approvedEndDateStr && expenseDateStr > approvedEndDateStr)
                                    ) {
                                        return (
                                            <>
                                                <TimePeriodIcon
                                                    className="claim-expenses-image"
                                                    width={24}
                                                    height={24}
                                                    viewBox="0 0 2084 2084"
                                                    style={{ display: 'block' }}
                                                />
                                            </>
                                        );
                                    }

                                    return null;
                                })()}
                        </b>
                    </div>
                ),
            },
            {
                key: 'submittedAmount',
                title: 'Submitted Amount',
                render: ({ submittedAmount }) => (
                    <span>
                        <b>{formatCurrency(submittedAmount || 0)}</b>
                    </span>
                ),
            },
            {
                key: 'approvedAmount',
                title: 'Approved Amount',
                render: ({ approvedAmount, status }) => {
                    if (status !== ExpenseStatus.AUDITED) {
                        return;
                    }
                    return (
                        <span>
                            <b>{formatCurrency(approvedAmount || 0)}</b>
                        </span>
                    );
                },
            },
            {
                key: 'declinedAmount',
                title: 'Declined Amount',
                render: ({ declinedAmount, status }) => {
                    if (status !== ExpenseStatus.AUDITED) {
                        return;
                    }
                    return (
                        <span>
                            <b>{formatCurrency(declinedAmount || 0)}</b>
                        </span>
                    );
                },
            },
            enableDateSorting
                ? {
                      ...getSortableColumn(
                          {
                              key: 'expenseDate',
                              title: 'Expense Date',
                              className: 'claim-expenses-list-date',
                              render: (expense: ExpenseDto) => (
                                  <span>
                                      {format(
                                          convertUtcToSpecifiedTimeZone(
                                              expense.date,
                                              claim.timezone,
                                          ),
                                          DATE_TIME,
                                          { locale: enUS },
                                      )}
                                  </span>
                              ),
                          },
                          sortDirection,
                      ),
                  }
                : {
                      key: 'expenseDate',
                      title: 'Expense Date',
                      className: 'claim-expenses-list-date',
                      render: (expense: ExpenseDto) => (
                          <span>
                              {format(
                                  convertUtcToSpecifiedTimeZone(expense.date, claim.timezone),
                                  DATE_TIME,
                                  { locale: enUS },
                              )}
                          </span>
                      ),
                  },
            {
                key: 'status',
                title: 'Status',
                align: 'center' as AlignType,
                render: (expense: ExpenseDto) => {
                    if (isStatusLoading === expense.id) {
                        return (
                            <div className="flex-center">
                                <Spinner />
                            </div>
                        );
                    }

                    if (expense.archivedAt) {
                        return (
                            <Button className="claim-expenses-list-status pill ghost" disabled>
                                <Space>
                                    {expense.status}
                                    {expense.status === ExpenseStatus.AUDITED ? (
                                        <IconCheck />
                                    ) : (
                                        <IconChevron />
                                    )}
                                </Space>
                            </Button>
                        );
                    }

                    return (
                        <div className="flex-center">
                            <Dropdown
                                menu={{
                                    items: statusMenuItems,
                                    onClick: (item) =>
                                        item &&
                                        onStatusChange(item.key as RawExpenseStatus, expense),
                                }}
                                className={classNames({
                                    'claim-expenses-list-status pill': true,
                                    info: expense.status === ExpenseStatus.SUBMITTED,
                                    warning: expense.status === ExpenseStatus.IN_REVIEW,
                                    success: expense.status === ExpenseStatus.AUDITED,
                                })}
                                disabled={
                                    readonly ||
                                    (expense.status === ExpenseStatus.AUDITED &&
                                        user?.role !== UserRole.SUPER_ADMIN)
                                }
                            >
                                <Space align="center" className={readonly ? undefined : 'cursor'}>
                                    <span>{expense.status}</span>
                                    {readonly ? null : expense.status === ExpenseStatus.AUDITED ? (
                                        <IconCheck />
                                    ) : (
                                        <IconChevron />
                                    )}
                                </Space>
                            </Dropdown>
                        </div>
                    );
                },
            },
        ]);

        if (!readonly) {
            cols.push({
                align: 'center',
                key: 'actions',
                title: 'Actions',
                render: (expense: ExpenseDto) => {
                    const isSuperAdmin = user?.role === UserRole.SUPER_ADMIN;
                    let menuItems = !expense.archivedAt ? actionItems : archivedActionMenuItems;
                    if (!isSuperAdmin && expense.status === ExpenseStatus.AUDITED) {
                        menuItems = menuItems?.filter(
                            (item) => (item as any).key !== ExpenseActions.EDIT,
                        );
                    }

                    return (
                        <div className="flex-center">
                            <ContextMenu
                                items={menuItems}
                                onItemClick={(item) => onActionClick(item, expense)}
                            />
                        </div>
                    );
                },
            });
        }

        return cols;
    }, [
        isStatusLoading,
        onActionClick,
        onStatusChange,
        readonly,
        rowSelection,
        sortDirection,
        user?.role,
        claim.timezone,
        enableDateSorting,
        claim.excessiveSpending,
        claim.approvedReceiptEndDate,
        claim.approvedReceiptStartDate,
    ]);

    return (
        <DataGrid
            list={list}
            columns={columns as ColumnsType<ExpenseDto>}
            rowSelection={rowSelection}
            onSelectAll={onSelectAll}
            onToggleSort={onToggleSort}
        />
    );
};
