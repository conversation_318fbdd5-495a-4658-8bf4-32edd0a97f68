import { Button, Space } from 'antd';
import classNames from 'classnames';
import { useCallback, useEffect, useState } from 'react';
import { useStoreActions, useStoreState } from '../../store';
import { ClaimFilterableField, ClaimsOrderBy } from '../../store/claim/types/Claim';
import PolicyHolderDto from '../../store/policyHolder/dto/PolicyHolderDto';
import { PolicyholderStatus } from '../../store/policyHolder/types/PolicyHolder';
import { PaginationDirection, makeFilterParam } from '../../utils/paginate';
import FlashMessages from '../app/FlashMessages';
import Avatar from '../app/shared/Avatar';
import Dialog from '../app/shared/Dialog';
import Modal from '../app/shared/Modal';
import Pagination from '../app/shared/Pagination';
import ConnectedClaimsList from './ConnectedClaimsList';
import { formatDateStringWithBrowserTimeZone } from '../../utils/dateFormat';

interface Props {
    onToggleStatusSuccess: VoidFunction;
    onClose: VoidFunction;
    show: boolean;
    policyholder: PolicyHolderDto;
}

const PolicyholderDetailsModal: React.FC<Props> = ({
    policyholder: policyholder,
    onClose,
    onToggleStatusSuccess,
    show,
}) => {
    const [showDeactivateDialog, setShowDeactivateDialog] = useState(false);
    const [dialogDeactivated, setDialogDeactivated] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const { list: claims, pagination } = useStoreState((state) => state.claim);
    const {
        get: geClaimsList,
        loadPagination,
        unload,
    } = useStoreActions((actions) => actions.claim);

    const { deactivate, activate } = useStoreActions((actions) => actions.policyholder);

    const getClaims = useCallback(
        (
            page = 1,
            limit = 6,
            order_by = ClaimsOrderBy.ID,
            direction = PaginationDirection.DESC,
        ) => {
            if (!policyholder) return;

            geClaimsList({
                page,
                limit,
                order_by,
                direction,
                params: [
                    makeFilterParam<ClaimFilterableField>('policy_holder_id', '=', policyholder.id),
                ],
            });
        },
        [geClaimsList, policyholder],
    );

    useEffect(() => {
        getClaims(pagination?.currentPage);
    }, [pagination?.currentPage, geClaimsList, getClaims]);

    useEffect(() => {
        return () => {
            unload();
        };
    }, [unload]);

    const handlePaginationChange = (currentPage: number) => {
        if (!pagination) {
            return;
        }
        loadPagination({ ...pagination, currentPage });
    };

    const handleDeactivate = async () => {
        setDialogDeactivated(true);
        try {
            await deactivate(policyholder.id);
            FlashMessages.success('Policyholder Deactivated');
            onToggleStatusSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Deactivate Policyholder');
        } finally {
            setShowDeactivateDialog(false);
            setDialogDeactivated(false);
        }
    };

    const handleActivate = async () => {
        setIsLoading(true);
        try {
            await activate(policyholder.id);
            FlashMessages.success('Policyholder Activated');
            onToggleStatusSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Activate Policyholder');
        } finally {
            setIsLoading(false);
        }
    };

    const ModalHeader = () => (
        <div className="flex-space header">
            <div className="flex-start">
                <Avatar
                    className="margin-right-24"
                    size="medium"
                    name={policyholder.name}
                    photoUrl={policyholder.profilePhoto}
                />
                <Space direction="vertical">
                    <div className="header-name">{policyholder.name}</div>
                    <div className="header-role">Policyholder</div>
                    <div
                        className={classNames('pill header-status', {
                            primary: policyholder.status === PolicyholderStatus.ACTIVE,
                            inactive: policyholder.status === PolicyholderStatus.INACTIVE,
                        })}
                    >
                        {policyholder.status}
                    </div>
                </Space>
            </div>
        </div>
    );

    return (
        <>
            <Modal
                className="settings-page-modal-details"
                onClose={onClose}
                show={show}
                title={<ModalHeader />}
            >
                <div className="body">
                    <div className="group">
                        <div className="label">Phone Number:</div>
                        <div className="value">{policyholder.phone}</div>
                    </div>
                    <div className="group">
                        <div className="label">Email:</div>
                        <div className="value">{policyholder.email}</div>
                    </div>
                    <div className="group">
                        <div className="label">Method of Contact</div>
                        <div className="value contact-method">{policyholder.contactMethod}</div>
                    </div>
                    <hr />
                    <div>
                        <ConnectedClaimsList list={claims} />
                        {pagination && (
                            <Pagination {...pagination} onChange={handlePaginationChange} simple />
                        )}
                    </div>
                    <hr />
                    {policyholder.status === PolicyholderStatus.ACTIVE && (
                        <>
                            <Button
                                type="link"
                                className="btn-text"
                                block
                                onClick={() => setShowDeactivateDialog(true)}
                            >
                                <b>Deactivate Account</b>
                            </Button>
                            <hr />
                        </>
                    )}
                    {policyholder.emailVerifiedAt &&
                        policyholder.status === PolicyholderStatus.INACTIVE && (
                            <>
                                <Button
                                    type="link"
                                    className="btn-text"
                                    block
                                    onClick={() => handleActivate()}
                                    loading={isLoading}
                                >
                                    <b>Activate Account</b>
                                </Button>
                                <hr />
                            </>
                        )}
                    <div className="timestamps">
                        {policyholder.activationSentAt && (
                            <div className="timestamp">
                                <span>Welcome Email Sent:</span>
                                <span>
                                    {formatDateStringWithBrowserTimeZone(
                                        policyholder.activationSentAt,
                                    )}{' '}
                                    (PST)
                                </span>
                            </div>
                        )}
                        {policyholder.emailVerifiedAt && (
                            <div className="timestamp">
                                <span>Terms Accepted:</span>
                                <span>
                                    {formatDateStringWithBrowserTimeZone(
                                        policyholder.emailVerifiedAt,
                                    )}{' '}
                                    (PST)
                                </span>
                            </div>
                        )}
                    </div>
                </div>
            </Modal>
            <Dialog
                onCancel={() => setShowDeactivateDialog(false)}
                onOk={handleDeactivate}
                show={showDeactivateDialog}
                disabled={dialogDeactivated}
            />
        </>
    );
};
export default PolicyholderDetailsModal;
