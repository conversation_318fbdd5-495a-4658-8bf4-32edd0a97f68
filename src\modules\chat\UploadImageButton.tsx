import classNames from 'classnames';
import { ChangeEvent } from 'react';
import { ReactComponent as IconPicture } from '../../assets/icons/images-outline.svg';
import { useStoreActions, useStoreState } from '../../store';
import { DocumentPathTypes } from '../../store/documents/types/Documents';
import { ImageDimensions, getImageDimensions } from '../../utils/imageUpload';
import { MAX_IMAGE_SIZE } from '../../store/chat/utils';

interface Props {
    onSuccess: (imagePath: string, dimensions: ImageDimensions) => void;
    onError: (message?: string) => void;
    disabled?: boolean;
}

const UploadImageButton: React.FC<Props> = ({ onSuccess, onError, disabled }) => {
    const { uploadProgress } = useStoreState((state) => state.documents);
    const { getDocuments, uploadFileToS3, getDocument, resetUploadProgress } = useStoreActions(
        (actions) => actions.documents,
    );

    const onUploadImage = async (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] as File;

        if (!file) {
            return;
        }

        try {
            if (file.size > MAX_IMAGE_SIZE) {
                onError('Maximum Upload Size is 10MB');
                return;
            }

            resetUploadProgress();
            const [presignedDocument] = await getDocuments({
                path: DocumentPathTypes.CHAT,
            });

            const imageDimensions = await getImageDimensions(file);
            await uploadFileToS3({
                file,
                presignedUrl: presignedDocument.presignedUrl,
            });

            const signedImageReference = await getDocument(presignedDocument.id);

            onSuccess(signedImageReference.image_path, imageDimensions);
        } catch (err: any) {
            console.error(err.message);
            onError();
        }
    };

    return (
        <div
            className={classNames('btn btn-image cursor', { 'has-image': uploadProgress === 100 })}
        >
            <input
                type="file"
                accept="image/*"
                onChange={onUploadImage}
                style={{ display: 'none' }}
                id="upload-button"
                disabled={disabled}
            />
            <label htmlFor="upload-button">
                <IconPicture className="cursor" />
            </label>
        </div>
    );
};
export default UploadImageButton;
