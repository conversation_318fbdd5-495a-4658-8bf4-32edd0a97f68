import { useNavigate, useParams } from 'react-router-dom';

import withoutAuth from '../../hooks/withoutAuth';
import AuthLayout from '../app/layouts/AuthLayout';
import ForgotPasswordForm from './ForgotPasswordForm';

const ForgotPasswordPage: React.FC = () => {
    const { email } = useParams();

    const navigate = useNavigate();

    const onSuccess = () => {
        navigate('/');
    };

    return (
        <AuthLayout>
            <ForgotPasswordForm email={email} onSuccess={onSuccess} />
        </AuthLayout>
    );
};

export default withoutAuth(ForgotPasswordPage);
