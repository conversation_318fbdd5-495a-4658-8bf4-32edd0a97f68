export interface NotificationData {
    title: string;
    message: string;
    claimId: string;
    reportId?: string;
    policyholderId?: string;
}

export interface RawNotification {
    id: string;
    data: RawNotificationData;
    read_at: string | null;
    created_at: string;
    type: string;
}

export interface RawNotificationData {
    title: string;
    message: string;
    claim_id: string;
    report_id?: string;
    policy_holder_id?: string;
}

export enum NotificationType {
    REPORT_APPROVED = 'App\\Notifications\\Adjuster\\AdjusterApprovedReportNotification',
    REPORT_REJECTED = 'App\\Notifications\\Adjuster\\AdjusterRejectReportNotification',
    REPORT_READY = 'App\\Notifications\\AutomatedReportReadyNotification',
    REPORT_NOT_READY = 'App\\Notifications\\AutomatedReportNotReadyNotification',
    CLAIM_INVITATION = 'App\\Notifications\\Common\\ClaimInvitationNotification',
    POLICYHOLDER_CONTACT_METHOD_CHANGED = 'App\\Notifications\\PolicyHolders\\PolicyHolderChangedContactMethodNotification',
    ADJUSTER_CHANGED = 'App\\Notifications\\Adjuster\\AdjusterChangedNotification',
    UREAD_CHAT = 'App\\Notifications\\Common\\ChatInactivityNotification',
}
