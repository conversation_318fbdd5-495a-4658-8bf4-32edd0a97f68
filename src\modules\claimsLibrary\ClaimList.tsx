import { MenuProps, Space } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { canAccess } from '../../common/canAccess';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { ClaimStatus } from '../../store/claim/types/Claim';
import { UserRole } from '../../store/user/types/User';
import { Resource } from '../../types';
// import { formatCurrencyString } from '../../utils/currencyFormat';
import Avatar from '../app/shared/Avatar';
import ContextMenu from '../app/shared/ContextMenu';
import DataGrid from '../app/shared/DataGrid';
import { ClaimActions, addMoreClaimActions } from './utils';
import { capitalizeString } from '../../utils/strings';
import { DATE_TIME, formatDateString, formatDateUTCString } from '../../utils/dateFormat';
import { PaginationDirection } from '../../utils/paginate';
import { getSortableColumn } from '../../common/table';

interface Props {
    list: Resource<ClaimDto[]>;
    handleActionClick: (item: MenuItemType | undefined, user: ClaimDto) => void;
    loading?: boolean;
    sortDirection: PaginationDirection;
    onToggleSort: (sortField: string) => void;
    sortField: string;
}

const ClaimList: React.FC<Props> = ({
    list,
    handleActionClick,
    loading = false,
    sortDirection,
    sortField,
    onToggleSort,
}) => {
    const navigate = useNavigate();

    const [actionItems] = useState<MenuProps['items']>([
        { label: 'View Claim', key: ClaimActions.VIEW_CLAIM },
        {
            label: 'Add Expense',
            key: ClaimActions.ADD_EXPENSE,
        },
        {
            label: 'Send Message',
            key: ClaimActions.SEND_MESSAGE,
        },
        {
            label: 'Expense Reminder',
            key: ClaimActions.EXPENSE_REMINDER,
        },
    ]);

    const columns: ColumnsType<ClaimDto> = useMemo(() => {
        const getSortDirection = (currField) => {
            if (sortField == currField) return sortDirection;
            return PaginationDirection.DESC;
        };
        if (canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]))
            return [
                // {
                //     key: 'id',
                //     title: 'Policyholder',
                //     className: 'user',
                //     render: ({ policyHolder, id }) => (
                //         <Space
                //             className="cursor"
                //             onClick={() =>
                //                 navigate(
                //                     canAccess([UserRole.ADJUSTER])
                //                         ? `/claim/${id}/reports`
                //                         : `/claim/${id}`,
                //                 )
                //             }
                //         >
                //             <Avatar
                //                 photoUrl={policyHolder.profilePhoto}
                //                 name={policyHolder.name}
                //                 size="table"
                //             />
                //             <b>{policyHolder.name}</b>
                //         </Space>
                //     ),
                // },
                {
                    ...getSortableColumn(
                        {
                            key: 'id',
                            title: 'Policyholder',
                            className: 'user',
                            render: ({ policyHolder, id }) => (
                                <Space
                                    className="cursor"
                                    onClick={() =>
                                        navigate(
                                            canAccess([UserRole.ADJUSTER])
                                                ? `/claim/${id}/reports`
                                                : `/claim/${id}`,
                                        )
                                    }
                                >
                                    <Avatar
                                        photoUrl={policyHolder.profilePhoto}
                                        name={policyHolder.name}
                                        size="table"
                                    />
                                    <b>{policyHolder.name}</b>
                                </Space>
                            ),
                        },
                        getSortDirection('id'),
                    ),
                },
                // {
                //     key: 'claimNumber',
                //     title: 'Claim Number',
                //     render: ({ claimNumber }: ClaimDto) => <span>{claimNumber}</span>,
                // },
                {
                    ...getSortableColumn(
                        {
                            key: 'claimNumber',
                            title: 'Claim Number',
                            className: 'Claim Number',
                            render: ({ claimNumber }: ClaimDto) => <span>{claimNumber}</span>,
                        },
                        getSortDirection('claimNumber'),
                    ),
                },
                // {
                //     key: 'ALE Limits',
                //     title: 'ALE Limits',
                //     render: ({ aleLimit }: ClaimDto) => (
                //         <span>{formatCurrencyString(aleLimit)}</span>
                //     ),
                // },
                {
                    ...getSortableColumn(
                        {
                            key: 'Carrier',
                            title: 'Carrier',
                            className: 'Carrier',
                            render: ({ insuranceCompany }: ClaimDto) => (
                                <span>{insuranceCompany}</span>
                            ),
                        },
                        getSortDirection('Carrier'),
                    ),
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'OOPE Rep',
                            title: 'OOPE Rep',
                            className: 'OOPE Rep',
                            render: ({ admin }: ClaimDto) => <span>{admin?.name}</span>,
                        },
                        getSortDirection('OOPE Rep'),
                    ),
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'Submitted Receipts',
                            title: 'Submitted Receipts',
                            className: 'Submitted Receipts',
                            render: ({ submitted }: ClaimDto) => <span>{submitted}</span>,
                        },
                        getSortDirection('Submitted Receipts'),
                    ),
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'In Review Receipts',
                            title: 'In Review Receipts',
                            className: 'In Review Receipts',
                            render: ({ in_review }: ClaimDto) => <span>{in_review}</span>,
                        },
                        getSortDirection('In Review Receipts'),
                    ),
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'Audited Receipts',
                            title: 'Audited Receipts',
                            className: 'Audited Receipts',
                            render: ({ audited }: ClaimDto) => <span>{audited}</span>,
                        },
                        getSortDirection('Audited Receipts'),
                    ),
                },
                // {
                //     key: 'Normal Daily Expense',
                //     title: 'Normal Daily Expense',
                //     render: ({ normalDailyExpense }: ClaimDto) => (
                //         <span>{formatCurrencyString(normalDailyExpense)}</span>
                //     ),
                // },
                // {
                //     ...getSortableColumn(
                //         {
                //             key: 'Normal Daily Expense',
                //             title: 'Normal Daily Expense',
                //             className: 'Normal Daily Expense',
                //             width: 200,
                //             render: ({ normalDailyExpense }: ClaimDto) => (
                //                 <span>{formatCurrencyString(normalDailyExpense)}</span>
                //             ),
                //         },
                //         getSortDirection('Normal Daily Expense'),
                //     ),
                // },
                {
                    ...getSortableColumn(
                        {
                            key: 'Activity',
                            title: 'Activity',
                            className: 'activity',
                            width: 200,

                            render: ({ updatedAt }: ClaimDto) => (
                                <span>{formatDateString(updatedAt)}</span>
                            ),
                        },
                        getSortDirection('Activity'),
                    ),
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'status',
                            title: 'Status',
                            align: 'center',
                            render: ({ status }: ClaimDto) => (
                                <div
                                    className={classNames(
                                        {
                                            'pill primary': [
                                                ClaimStatus.ACTIVE,
                                                ClaimStatus.ON_HOLD,
                                                ClaimStatus.INVOICED,
                                                ClaimStatus.PAYMENT_PENDING,
                                            ].includes(status),
                                        },
                                        {
                                            'pill inactive': [
                                                ClaimStatus.SIU,
                                                ClaimStatus.CLOSED,
                                                ClaimStatus.CLOSED_WON,
                                                ClaimStatus.CLOSED_LOST,
                                            ].includes(status),
                                        },
                                    )}
                                >
                                    {capitalizeString(status)}
                                </div>
                            ),
                        },
                        getSortDirection('status'),
                    ),
                },
                {
                    key: 'Actions',
                    title: 'Actions',
                    width: 60,
                    render: (claim: ClaimDto) => (
                        <div className="flex-center">
                            {canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) && (
                                <ContextMenu
                                    onItemClick={(item) => handleActionClick(item, claim)}
                                    items={addMoreClaimActions(actionItems, claim)}
                                />
                            )}
                        </div>
                    ),
                },
            ];
        else {
            return [
                {
                    key: 'id',
                    title: 'Policyholder',
                    className: 'user',
                    render: ({ policyHolder, id }) => (
                        <Space
                            className="cursor"
                            onClick={() =>
                                navigate(
                                    canAccess([UserRole.ADJUSTER])
                                        ? `/claim/${id}/reports`
                                        : `/claim/${id}`,
                                )
                            }
                        >
                            <Avatar
                                photoUrl={policyHolder.profilePhoto}
                                name={policyHolder.name}
                                size="table"
                            />
                            <b>{policyHolder.name}</b>
                        </Space>
                    ),
                },
                {
                    key: 'claimNumber',
                    title: 'Claim Number',
                    render: ({ claimNumber }: ClaimDto) => <span>{claimNumber}</span>,
                },
                {
                    key: 'reportFrequency',
                    title: 'Report Frequency',
                    render: ({ reportDays }: ClaimDto) => <span>{reportDays}</span>,
                },
                {
                    ...getSortableColumn(
                        {
                            key: 'lossDate',
                            title: 'Loss Date',
                            className: 'activity',
                            width: 200,

                            render: ({ lossDate }: ClaimDto) => (
                                <span>{formatDateUTCString(lossDate, DATE_TIME)}</span>
                            ),
                        },
                        sortDirection,
                    ),
                },
                {
                    key: 'status',
                    title: 'Status',
                    align: 'center',
                    render: ({ status }: ClaimDto) => (
                        <div
                            className={classNames(
                                {
                                    'pill primary': [
                                        ClaimStatus.ACTIVE,
                                        ClaimStatus.ON_HOLD,
                                        ClaimStatus.INVOICED,
                                        ClaimStatus.PAYMENT_PENDING,
                                    ].includes(status),
                                },
                                {
                                    'pill inactive': [
                                        ClaimStatus.SIU,
                                        ClaimStatus.CLOSED,
                                        ClaimStatus.CLOSED_WON,
                                        ClaimStatus.CLOSED_LOST,
                                    ].includes(status),
                                },
                            )}
                        >
                            {capitalizeString(status)}
                        </div>
                    ),
                },
            ];
        }
    }, [actionItems, handleActionClick, navigate, sortDirection, sortField]);
    return (
        <DataGrid<ClaimDto>
            loading={loading}
            list={list}
            columns={columns}
            onToggleSort={onToggleSort}
        />
    );
};
export default ClaimList;
