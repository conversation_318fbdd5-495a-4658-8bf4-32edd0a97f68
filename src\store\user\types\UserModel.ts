import { Action, Computed, Thunk } from 'easy-peasy';
import { Resource, ResourcePagination } from '../../app/types';
import UserDto, { UserCreateDto, UserPaginateDto, UserUpdateDto } from '../dto/UserDto';

export default interface UserModel {
    // state
    list: Resource<UserDto[]>;
    pagination: ResourcePagination | null;

    // computed
    hasMore: Computed<UserModel, boolean>;

    // actions
    load: Action<UserModel, UserDto[]>;
    loading: Action<UserModel>;
    setPagination: Action<UserModel, ResourcePagination | null>;
    unload: Action<UserModel>;

    // thunks
    get: Thunk<UserModel, UserPaginateDto>;
    getAndAppend: Thunk<UserModel, UserPaginateDto>;
    create: Thunk<UserModel, UserCreateDto>;
    update: Thunk<UserModel, { id: string; dto: UserUpdateDto }>;
    deleteUser: Thunk<UserModel, string>;
}
