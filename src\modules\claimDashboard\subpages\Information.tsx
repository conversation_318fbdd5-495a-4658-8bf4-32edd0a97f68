import { <PERSON><PERSON>, Col, Row } from 'antd';
import { format } from 'date-fns';
import { toDate } from 'date-fns-tz';
import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { canAccess } from '../../../common/canAccess';
import { useStoreActions } from '../../../store';
import { ClaimDto } from '../../../store/claim/dto/ClaimDto';
import { ClaimLossAddress, ClaimStatus } from '../../../store/claim/types/Claim';
import { UserRole } from '../../../store/user/types/User';
import { formatCurrencyString } from '../../../utils/currencyFormat';
import { DATE_ONLY } from '../../../utils/dateFormat';
import { formatPhoneNumber } from '../../../utils/phoneFormat';
import FlashMessages from '../../app/FlashMessages';
import Dialog from '../../app/shared/Dialog';
import Spinner from '../../app/shared/Spinner';

const claimLossOrder: (keyof ClaimLossAddress)[] = ['streetAddress', 'city', 'state', 'zipCode'];

interface Props {
    claim: ClaimDto;
    onClaimStatusChange?: VoidFunction;
}

const Information: React.FC<Props> = ({ claim, onClaimStatusChange }) => {
    const { id } = useParams();
    const [isLoading, setIsLoading] = useState(false);
    const [showArchiveDialog, setShowArchiveDialog] = useState(false);
    const [showCloseDialog, setShowCloseDialog] = useState(false);

    const { close, reopen, archive } = useStoreActions((actions) => actions.claim);

    const formattedLossAddress = useMemo(() => {
        if (!claim) return '';

        return claimLossOrder
            .map((key) => claim.lossAddress[key])
            .filter((val) => !!val)
            .join(', ');
    }, [claim]);

    const onCloseClaim = async () => {
        setIsLoading(true);
        try {
            if (!id) {
                throw new Error('No claim id');
            }
            await close(id);
            FlashMessages.success('Claim closed');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to close claim');
        } finally {
            setShowCloseDialog(false);
            !!onClaimStatusChange && onClaimStatusChange();
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    };

    const onReopenClaim = async () => {
        setIsLoading(true);
        try {
            if (!id) {
                throw new Error('No claim id');
            }
            await reopen(id);
            FlashMessages.success('Claim reopened');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to reopen claim');
        } finally {
            !!onClaimStatusChange && onClaimStatusChange();
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    };

    const onArchiveClaim = async () => {
        setIsLoading(true);
        try {
            if (!id) {
                throw new Error('No claim id');
            }
            await archive(id);
            FlashMessages.success('Claim archived');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to archive claim');
        } finally {
            setShowArchiveDialog(false);
            !!onClaimStatusChange && onClaimStatusChange();
            setTimeout(() => {
                setIsLoading(false);
            }, 1000);
        }
    };

    if (!claim || isLoading) {
        return <Spinner type="skeleton" />;
    }
    return (
        <div className="claim-information margin">
            <div className="box claim-information-policyholder">
                <div className="heading h5">Policyholder Information</div>
                <Row gutter={[40, 0]}>
                    <Col span={6} className="group word-break ">
                        <div className="label">Policyholder:</div>
                        <div className="value ">{claim.policyHolder.name}</div>
                    </Col>
                    <Col span={6} className="group word-break">
                        <div className="label">Phone Number:</div>
                        <div className="value">{formatPhoneNumber(claim.policyHolder.phone)}</div>
                    </Col>
                    <Col span={6} className="group word-break ">
                        <div className="label">Email:</div>
                        <div className="value">{claim.policyHolder.email}</div>
                    </Col>
                    <Col span={6} className="group word-break">
                        <div className="label">Method of Contact:</div>
                        <div className="value contact">{claim.policyHolder.contactMethod}</div>
                    </Col>
                </Row>
                <hr />
                <Row gutter={[40, 0]}>
                    <Col span={12} className="group word-break">
                        <div className="label">Loss Address</div>
                        <div className="value ">{formattedLossAddress}</div>
                    </Col>
                    <Col span={12} className="group word-break">
                        {!!claim.temporaryAddress && (
                            <>
                                <div className="label">Temporary Address</div>
                                <div className="value">{claim.temporaryAddress}</div>
                            </>
                        )}
                    </Col>
                </Row>
                <hr />
                <Row gutter={[40, 0]}>
                    <Col className="group word-break" span={6}>
                        {claim.numberAdults && (
                            <>
                                <div className="label">Adults</div>
                                <div className="value">{claim.numberAdults}</div>
                            </>
                        )}
                    </Col>
                    <Col className="group word-break" span={6}>
                        {!!claim.numberChildren && (
                            <>
                                <div className="label">Children</div>
                                <div className="value">{claim.numberChildren}</div>
                            </>
                        )}
                    </Col>

                    <Col className="group word-break" span={6}>
                        {claim.numberPets && (
                            <>
                                <div className="label">Pets</div>
                                <div className="value">{claim.numberPets}</div>
                            </>
                        )}
                    </Col>

                    <Col className="group word-break" span={6}>
                        {claim.petTypes && (
                            <>
                                <div className="label">Pet Info</div>
                                <div className="value">{claim.petTypes}</div>
                            </>
                        )}
                    </Col>
                </Row>
            </div>
            <div className="box claim-information-claim">
                <div className="heading h5 margin-bottom-24">Claim Information</div>
                <Row gutter={[40, 0]} className="margin-bottom-24">
                    <Col span={6} className="group word-break">
                        {claim.typeOfPolicy && (
                            <>
                                <div className="label">Type of policy:</div>
                                <div className="value">{claim.typeOfPolicy}</div>
                            </>
                        )}
                    </Col>
                    <Col span={6} className="group word-break">
                        {claim.lossReason && (
                            <>
                                <div className="label">Loss Reason:</div>
                                <div className="value">{claim.lossReason}</div>
                            </>
                        )}
                    </Col>
                    <Col span={6} className="group word-break">
                        <div className="label">Normal Daily Expense:</div>
                        <div className="value">
                            {formatCurrencyString(claim.normalDailyExpense)}
                        </div>
                    </Col>
                    <Col span={6} className="group word-break">
                        <div className="label">ALE Limits:</div>
                        <div className="value">{formatCurrencyString(claim.aleLimit)}</div>
                    </Col>
                </Row>
                <hr />
                <Row gutter={[40, 0]} className="margin-top-24">
                    {claim.lossDate && (
                        <Col span={6} className="group word-break">
                            <div className="label">Date of Loss:</div>
                            <div className="value">{format(toDate(claim.lossDate), DATE_ONLY)}</div>
                        </Col>
                    )}
                    {claim.relocationLength && (
                        <Col span={6} className="group word-break">
                            <div className="label">Length of Relo:</div>
                            <div className="value">{claim.relocationLength}</div>
                        </Col>
                    )}
                    {claim.specialNeeds && (
                        <Col span={6} className="group word-break">
                            <div className="label">Special Needs:</div>
                            <div className="value">{claim.specialNeeds}</div>
                        </Col>
                    )}
                </Row>
            </div>
            {(claim.approvedReceiptStartDate ||
                claim.approvedReceiptEndDate ||
                claim.approvedReceiptCategories ||
                claim.bankStatementsApproved ||
                claim.alcoholApproved ||
                claim.alcoholLimitedTo ||
                claim.groceriesApproved ||
                claim.unitemizedReceiptsApproved ||
                claim.approvedReceiptsMileRadius ||
                claim.description) && (
                <div className="box claim-information-additional">
                    <div className="heading h5 margin-bottom-24">Additional Information</div>
                    <Row gutter={[40, 0]} className="margin-bottom-24">
                        <Col span={6} className="group word-break">
                            <div className="label">Approved Receipt Start Date:</div>
                            {claim.approvedReceiptStartDate && (
                                <div className="value">
                                    {format(toDate(claim.approvedReceiptStartDate), DATE_ONLY)}
                                </div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Approved Receipt End Date:</div>
                            {claim.approvedReceiptEndDate && (
                                <div className="value">
                                    {format(toDate(claim.approvedReceiptEndDate), DATE_ONLY)}
                                </div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Approved Receipt Categories:</div>
                            {claim.approvedReceiptCategories && (
                                <div className="value">
                                    {claim.approvedReceiptCategories?.join(', ')}
                                </div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Groceries (Pre-Made Meals) Approved:</div>
                            <div className="value">{claim.groceriesApproved}</div>
                        </Col>
                    </Row>
                    <hr />
                    <Row gutter={[40, 0]} className="margin-top-24 margin-bottom-24">
                        <Col span={6} className="group word-break">
                            <div className="label">Bank Statements Approved:</div>
                            {claim.bankStatementsApproved && (
                                <div className="value">{claim.bankStatementsApproved}</div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Alcohol Approved:</div>
                            {claim.alcoholApproved && (
                                <div className="value">{claim.alcoholApproved}</div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Alcohol Limited To:</div>
                            {claim.alcoholLimitedTo && (
                                <div className="value">{claim.alcoholLimitedTo}</div>
                            )}
                        </Col>
                        <Col span={6} className="group word-break">
                            <div className="label">Unitemized Receipts Approved:</div>
                            {claim.unitemizedReceiptsApproved && (
                                <div className="value">{claim.unitemizedReceiptsApproved}</div>
                            )}
                        </Col>
                    </Row>
                    <hr />
                    <Row gutter={[40, 0]} className="margin-top-24">
                        <Col span={8} className="group word-break">
                            <div className="label">Mile Radius for Approved Receipts:</div>
                            {claim.approvedReceiptsMileRadius && (
                                <div className="value">{claim.approvedReceiptsMileRadius}</div>
                            )}
                        </Col>
                        <Col span={8} className="group word-break">
                            <div className="label">Excessive Spending:</div>
                            {claim.excessiveSpending && (
                                <div className="value">{claim.excessiveSpending}</div>
                            )}
                        </Col>
                        <Col span={8} className="group word-break">
                            <div className="label">Description:</div>
                            {claim.description && <div className="value">{claim.description}</div>}
                        </Col>
                    </Row>
                </div>
            )}
            {!claim.archivedAt && canAccess([UserRole.SUPER_ADMIN]) && (
                <div className="flex-end">
                    {claim.status === ClaimStatus.ACTIVE && (
                        <Button
                            className="btn-primary"
                            size="large"
                            type="primary"
                            onClick={() => setShowCloseDialog(true)}
                        >
                            Close Claim
                        </Button>
                    )}

                    {claim.status === ClaimStatus.CLOSED && (
                        <>
                            <Button
                                size="large"
                                className="btn-secondary outline margin-right-16"
                                type="primary"
                                onClick={() => onReopenClaim()}
                            >
                                Reopen Claim
                            </Button>
                            <Button
                                className="btn-primary"
                                size="large"
                                onClick={() => setShowArchiveDialog(true)}
                            >
                                Archive Claim
                            </Button>
                        </>
                    )}
                </div>
            )}
            <Dialog
                show={showCloseDialog}
                onOk={() => onCloseClaim()}
                onCancel={() => setShowCloseDialog(false)}
                disabled={isLoading}
            />
            <Dialog
                show={showArchiveDialog}
                onOk={() => onArchiveClaim()}
                onCancel={() => setShowArchiveDialog(false)}
                disabled={isLoading}
            />
        </div>
    );
};
export default Information;
