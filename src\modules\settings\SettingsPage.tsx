import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import withAuth from '../../hooks/withAuth';
import { UserRole } from '../../store/user/types/User';
import SettingsSubHeader from '../app/layouts/Header/SubHeader/SettingsSubHeader';
import MainLayout from '../app/layouts/MainLayout';
import { ArchivedClaims } from './ArchivedClaims';
import Users from './Users';

const SettingsPage: React.FC = () => {
    const navigate = useNavigate();
    const { pathname } = useLocation();
    const isUsersInitial = pathname.includes('/users');

    const [isUsers, setIsUsers] = useState(isUsersInitial);

    const handleToggleIsUsers = (isUsers: boolean) => {
        setIsUsers(isUsers);
        if (isUsers) {
            navigate('/settings/users');
        } else {
            navigate('/settings/archived-claims');
        }
    };

    return (
        <MainLayout
            SubHeaderComponent={
                <SettingsSubHeader isUsers={isUsers} onToggleUsers={handleToggleIsUsers} />
            }
        >
            {isUsers ? <Users /> : <ArchivedClaims />}

            <div className="settings-page"></div>
        </MainLayout>
    );
};

export default withAuth(SettingsPage, [UserRole.SUPER_ADMIN]);
