import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
// import { ReactComponent as PlusIcon } from '../../../../assets/icons/add-sharp.svg';
import { ReactComponent as ExpenseIcon } from '../../../../assets/icons/receipt-outline.svg';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { isOpen } from '../../../../common/claim';
import { canAccess } from '../../../../common/canAccess';
import { UserRole } from '../../../../store/user/types/User';

interface Props {
    claim: ClaimDto;
}

export const QuickActions: React.FC<Props> = ({ claim }) => {
    const navigate = useNavigate();

    if (claim.archivedAt || (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN]))) {
        return null;
    }

    return (
        <div className="quick-actions box">
            <div className="heading medium quick-actions-title">Report Types</div>
            <div className="quick-actions-list">
                <Button
                    className={location.pathname.includes('/request') ? 'btn-secondary' : undefined}
                    size="large"
                    type={'primary'}
                    onClick={() =>
                        navigate(`/claim/${claim.id}/reports`, {
                            state: {
                                quickAddReprot: true,
                            },
                        })
                    }
                    style={{
                        marginRight: '10px',
                        backgroundColor: location.pathname.includes('/request')
                            ? '#D6DCDB'
                            : '#6bcaba66',
                    }}
                >
                    {/* <PlusIcon /> */}
                    <span>Classic Reports</span>
                </Button>
                <Button
                    className={
                        !location.pathname.includes('/request') ? 'btn-secondary' : undefined
                    }
                    size="large"
                    type={'primary'}
                    style={{
                        backgroundColor: !location.pathname.includes('/request')
                            ? '#D6DCDB'
                            : '#6bcaba66',
                    }}
                    onClick={() =>
                        navigate(`/claim/${claim.id}/reports/request`, {
                            state: {
                                quickRequestReprot: true,
                            },
                        })
                    }
                >
                    <ExpenseIcon />
                    <span>Report Request</span>
                </Button>
            </div>
        </div>
    );
};
