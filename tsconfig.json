{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "noImplicitAny": false, "noImplicitReturns": true, "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client"], "noFallthroughCasesInSwitch": true}, "include": ["src", "babel.config.js", "src/global.d.ts"], "files": ["vite.config.mts", "src/svg.d.ts", "node_modules/@types/jest/index.d.ts"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["node_modules"]}