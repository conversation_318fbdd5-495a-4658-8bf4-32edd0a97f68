export enum UserRole {
    ADMIN = 'Admin',
    SUPER_ADMIN = 'Super Admin',
    ADJUSTER = 'Adjuster',
}

export interface RawUserData {
    id: string;
    name: string;
    email: string;
    phone: string;
    company: string;
    status: string;
    role: string;
    profile_photo: string | null;
    job_title: string;
    timezone: string;
    chat_user_arn: string | null;
    created_at: string;
    preferred_contact_method: string | null;
    activation_sent_at: string | null;
    terms_accepted_at: string | null;
}

export enum RawUserRole {
    ADMIN = 'admin',
    SUPER_ADMIN = 'super_admin',
    ADJUSTER = 'adjuster',
}

export interface RawAuthData {
    access_token: string;
    refresh_token: string;
    expires_in: number;
}
