import { action, computed, thunk } from 'easy-peasy';
import { api } from '../../common/api';
import { DEFAULT_PER_PAGE, mapPagination } from '../../utils/paginate';
import { transition } from '../app/types';
import { mapNotifications } from './mappers';
import NotificationModel from './types/NotificationModel';

const notificationModel: NotificationModel = {
    // state
    list: transition.reset([]),
    pagination: null,
    unreadCount: 0,
    hasMore: computed((state) => state.list.value.length < (state.pagination?.total || 0)),

    // actions
    load: action((state, notifications) => {
        state.list = transition.loaded(notifications);
    }),

    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    setPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    setUnreadCount: action((state, unread) => {
        state.unreadCount = unread;
    }),

    setAllRead: action((state) => {
        state.list = transition.loaded(
            state.list.value.map((notification) => ({ ...notification, readAt: new Date() })),
        );
        state.unreadCount = 0;
    }),

    setNotificationRead: action((state, id) => {
        const notifications = state.list.value.map((notification) => {
            return notification.id !== id ? notification : { ...notification, readAt: new Date() };
        });

        state.list = transition.loaded(notifications);
    }),

    unloadList: action((state) => {
        state.pagination = null;
        state.list = transition.reset([]);
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
        state.unreadCount = 0;
    }),

    get: thunk(async (actions, payload, { getState }) => {
        payload.page = payload.page || getState().pagination?.currentPage || 1;
        payload.limit = payload.limit || DEFAULT_PER_PAGE;

        actions.loading();

        const {
            data: { data, meta },
        } = await api.get('/notifications', {
            params: {
                ...payload,
            },
        });

        actions.load(getState().list.value.concat(mapNotifications(data)));

        const pagination = mapPagination(meta);
        pagination.currentPage = (getState().pagination?.currentPage || 1) + 1;
        actions.setPagination(pagination);
    }),

    checkUnread: thunk(async (actions) => {
        const {
            data: { data },
        } = await api.get(`/notifications/count`);

        actions.setUnreadCount(data);
    }),

    markRead: thunk(async (actions, id) => {
        await api.post(`/notifications/${id}`);

        actions.setNotificationRead(id);

        return actions.checkUnread();
    }),

    readAll: thunk(async (actions) => {
        await api.get('/notifications/read-all');
        actions.setAllRead();
        return actions.checkUnread();
    }),
};

export default notificationModel;
