import { ClaimDto } from '../store/claim/dto/ClaimDto';
import { ClaimStatus } from '../store/claim/types/Claim';

export const isOpen = (claim: ClaimDto) =>
    claim.status === ClaimStatus.ACTIVE ||
    claim.status === ClaimStatus.ON_HOLD ||
    claim.status === ClaimStatus.INVOICED ||
    claim.status === ClaimStatus.PAYMENT_PENDING ||
    claim.status === ClaimStatus.CLOSED ||
    claim.status === ClaimStatus.CLOSED_WON ||
    claim.status === ClaimStatus.CLOSED_LOST ||
    claim.status === ClaimStatus.SIU;
