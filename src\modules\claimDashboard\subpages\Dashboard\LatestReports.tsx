import { Button, Space } from 'antd';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as IconArrow } from '../../../../assets/icons/arrow-forward-sharp.svg';
import { useStoreActions, useStoreState } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { PaginationDirection, makeFilterParam } from '../../../../utils/paginate';
import useReportActions from '../Reports/useReportActions';
import LatestReportsList from './LatestReportsList';
import DetailsModal from '../Reports/DetailsModal';
import { ReportFilterableFields } from '../../../../store/report/type/Report';

interface Props {
    claim: ClaimDto;
}

const LatestReports: React.FC<Props> = ({ claim }) => {
    const navigate = useNavigate();

    const { list, pagination } = useStoreState((state) => state.report);
    const { get } = useStoreActions((actions) => actions.report);

    const handleGetReports = useCallback(() => {
        get({
            page: 1,
            order_by: 'created_at',
            direction: PaginationDirection.DESC,
            limit: 5,
            params: [makeFilterParam<ReportFilterableFields>('claim_id', '=', claim.id)],
        });
    }, [claim.id, get]);

    const { onActionClick, actionLoading, showDetailsModal, setShowDetailsModal, selectedReport } =
        useReportActions(handleGetReports);

    useEffect(() => {
        handleGetReports();
    }, [handleGetReports]);

    return (
        <div className="box claim-dashboard-widget">
            <div className="claim-dashboard-widget-heading flex-space">
                <div className="heading medium margin-bottom-15">Reports by Date</div>
                {(pagination?.total || 0) > 5 && (
                    <Button
                        type="primary"
                        size="large"
                        className="btn-secondary outline "
                        onClick={() => navigate(`/claim/${claim.id}/reports`)}
                    >
                        <Space>
                            <span>
                                <b>View All</b>
                            </span>
                            <IconArrow />
                        </Space>
                    </Button>
                )}
            </div>
            <LatestReportsList
                list={list}
                onActionClick={onActionClick}
                actionLoading={actionLoading}
                claim={claim}
            />
            {showDetailsModal && selectedReport && (
                <DetailsModal
                    claim={claim}
                    show={showDetailsModal}
                    onClose={() => setShowDetailsModal(false)}
                    report={selectedReport}
                    onSuccess={() => {
                        setShowDetailsModal(false);
                        handleGetReports();
                    }}
                    onActionClick={onActionClick}
                />
            )}
        </div>
    );
};
export default LatestReports;
