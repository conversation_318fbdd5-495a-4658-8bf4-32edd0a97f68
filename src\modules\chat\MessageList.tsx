import classNames from 'classnames';
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import useInfiniteScroll from '../../hooks/useInfiniteScroll';
import { useStoreActions, useStoreState } from '../../store';
import { ChatStatus } from '../../store/chat/types/Chat';
import Spinner from '../app/shared/Spinner';
import Message from './Message';
import { useChat } from './context/ChatContext';

const MessageList: React.FC = () => {
    const { id: claimId } = useParams();
    const { messages, groupedMessages, hasMoreMessages, chatStatus } = useStoreState(
        (state) => state.chat,
    );
    const { pingSeen } = useStoreActions((actions) => actions.chat);

    const { messageListRef, getMessages } = useChat();

    const [loaderRef] = useInfiniteScroll({
        loading: messages.loading,
        onLoadMore: () => {
            getMessages();
        },
        hasMore: hasMoreMessages,
    });

    useEffect(() => {
        claimId && pingSeen(claimId);
    }, [claimId, pingSeen]);

    return (
        <div
            id="chat-list"
            className={classNames('chat-list', {
                'is-loading':
                    chatStatus === ChatStatus.CONNECTING || chatStatus === ChatStatus.RECONNECTING,
            })}
            ref={messageListRef}
        >
            {hasMoreMessages && (
                <div className="chat-loader" ref={messages.loading ? undefined : loaderRef}>
                    <Spinner />
                </div>
            )}

            {groupedMessages.map((message) => (
                <Message key={message.id} message={message} />
            ))}
        </div>
    );
};

export default MessageList;
