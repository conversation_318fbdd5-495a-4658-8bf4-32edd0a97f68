.claim-dashboard {
    padding-bottom: 16px;

    &-widget {
        &:last-child {
            margin-bottom: 0;
        }

        margin-bottom: 16px;
    }

    &-subheader {
        @include flex(flex, row, nowrap, space-between, center);
        gap: 4px;

        &-actions {
            @include flex(flex, row, nowrap, flex-end, center);
            gap: 8px;
        }

        .icon-back {
            transform: rotate(180deg);
        }

        &-info {
            @include flex(flex, row, nowrap, flex-start, center);
            gap: 24px;

            .summary {
                display: block !important;
                margin-top: 0;
                width: 100%;

                &-name {
                    font-size: 24px;
                    font-weight: $font-weight-bold;
                    line-height: 29px;
                }

                &-list {
                    font-size: 14px;
                    line-height: 18px;

                    &-item {
                        font-weight: $font-weight-regular;
                        padding-right: 8px;
                        border-right: 1px solid $color-text;

                        &-last {
                            border-right: none;
                        }
                    }
                }

                &-reports {
                    font-weight: $font-weight-regular;
                    font-size: 14px;
                    line-height: 18px;
                }
            }
        }
    }

    .quick-actions {
        margin-bottom: 16px;

        &-list {
            @include flex(flex, row, wrap, flex-start, center);

            button {
                @include flex(flex, row, nowrap, space-between, center);
                font-size: 16px;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
                gap: 16px;
                margin-right: 8px;
                padding: 13px 16px;
            }
        }

        &-title {
            margin-bottom: 16px;
        }
    }
}
