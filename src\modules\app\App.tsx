import { ConfigProvider } from 'antd';
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import { useStoreState } from '../../store';
import { UserRole } from '../../store/user/types/User';
import theme from '../../theme/theme';
import ForgotPasswordPage from '../auth/ForgotPasswordPage';
import LoginPage from '../auth/LoginPage';
import ResetPasswordPage from '../auth/ResetPasswordPage';
import SetupPasswordPage from '../auth/SetupPasswordPage';
import ClaimDashboardPage from '../claimDashboard/ClaimDashboardPage';
import ClaimLibraryPage from '../claimsLibrary/ClaimLibraryPage';
import { PathnamePrefixes } from '../claimsLibrary/utils';
import MyAccountPage from '../myAccount/MyAccountPage';
import SettingsPage from '../settings/SettingsPage';
import NotificationsPage from './notifications/NotificationsPage';
import ResetPasswordInfoPage from '../auth/ResetPasswordInfoPage';

const App = () => {
    const { user } = useStoreState((state) => state.auth);

    return (
        <ConfigProvider theme={theme}>
            <BrowserRouter>
                <Routes>
                    <Route path="/auth/login" element={<LoginPage />} />
                    <Route path="/auth/activation/:token" element={<SetupPasswordPage />} />
                    <Route path="/auth/reset-password/:token" element={<ResetPasswordPage />} />
                    <Route path="/reset-password-message" element={<ResetPasswordInfoPage />} />
                    <Route path="/auth/forgot-password/" element={<ForgotPasswordPage />} />
                    <Route path="/auth/forgot-password/:email" element={<ForgotPasswordPage />} />
                    <Route path="/" element={<Navigate to={`/${PathnamePrefixes.MY_CLAIMS}`} />} />
                    <Route path={`/${PathnamePrefixes.MY_CLAIMS}`} element={<ClaimLibraryPage />} />
                    <Route path={`/${PathnamePrefixes.RECENT}`} element={<ClaimLibraryPage />} />
                    <Route
                        path={`/${PathnamePrefixes.MY_CLAIMS}/page/:pageNumber`}
                        element={<ClaimLibraryPage />}
                    />
                    <Route
                        path={`/all-claims`}
                        element={
                            user?.role === UserRole.SUPER_ADMIN || user?.role === UserRole.ADMIN ? (
                                <ClaimLibraryPage />
                            ) : (
                                <Navigate to="/" />
                            )
                        }
                    />
                    <Route
                        path={`/all-claims/page/:pageNumber`}
                        element={
                            user?.role === UserRole.SUPER_ADMIN || user?.role === UserRole.ADMIN ? (
                                <ClaimLibraryPage />
                            ) : (
                                <Navigate to="/" />
                            )
                        }
                    />
                    <Route path="/claim/:id/*" element={<ClaimDashboardPage />} />
                    <Route path="/account" element={<MyAccountPage />} />
                    <Route path="/account/page/:pageNumber" element={<MyAccountPage />} />
                    <Route path="/settings" element={<Navigate to="/settings/users" />} />
                    <Route path="/settings/users" element={<SettingsPage />} />
                    <Route path="/settings/users/new" element={<SettingsPage />} />
                    <Route path="/settings/users/page/:pageNumber" element={<SettingsPage />} />
                    <Route path="/settings/archived-claims" element={<SettingsPage />} />
                    <Route
                        path="/settings/archived-claims/page/:pageNumber"
                        element={<SettingsPage />}
                    />
                    <Route path="/notifications" element={<NotificationsPage />} />
                    <Route path="*" element={<Navigate to="/" />} />
                </Routes>
            </BrowserRouter>
        </ConfigProvider>
    );
};

export default App;
