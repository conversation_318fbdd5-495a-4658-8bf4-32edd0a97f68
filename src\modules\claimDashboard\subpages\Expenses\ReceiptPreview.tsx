import { Button } from 'antd';
import TransformContainer from '../../../app/shared/TransformContainer';
import Spinner from '../../../app/shared/Spinner';
import React, { useEffect, useState } from 'react';

interface ReceiptPreviewProps {
    photoUrl?: string;
    canRequestReceipt?: boolean;
    onRequestReceipt?: VoidFunction;
}

const ReceiptPreview: React.FC<ReceiptPreviewProps> = ({
    photoUrl,
    canRequestReceipt,
    onRequestReceipt,
}) => {
    const [isLoading, setIsLoading] = useState(true);
    const [currentImage, setCurrentImage] = useState<string>();

    useEffect(() => {
        // if (currentImage !== photoUrl) {
        setIsLoading(true);
        setCurrentImage(photoUrl);
        // }
    }, [photoUrl]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
        <TransformContainer
            additionalActions={
                canRequestReceipt ? (
                    <Button
                        className="btn-ghost btn-tab request-receipt btn-ghost-bg"
                        onClick={() => onRequestReceipt && onRequestReceipt()}
                    >
                        <b>Request Receipt</b>
                    </Button>
                ) : undefined
            }
        >
            {(!photoUrl || isLoading) && <Spinner type="overlay" />}
            {photoUrl && (
                <img
                    className="responsive-image"
                    src={currentImage}
                    alt="Receipt"
                    onLoad={() => setIsLoading(false)}
                />
            )}
        </TransformContainer>
    );
};
export default ReceiptPreview;
