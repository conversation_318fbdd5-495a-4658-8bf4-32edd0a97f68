.selectbox {
    &-container {
        margin-bottom: 24px;
    }

    &-control {
        background-color: $color-input-background !important ;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        padding: 16px 18px !important;
    }

    &-indicator {
        padding: 0px !important;
    }

    &-menu {
        background-color: $color-input-background !important ;
        border-radius: 0 !important;
        margin-top: 4px !important;

        &-list {
            padding: 0 !important;
        }
    }

    &-multivalue {
        background: none !important;
        margin: 0 !important;
        margin-right: 4px !important;

        &-label {
            color: $color-text !important;
            font-size: 16px !important;
            line-height: 130% !important;
            font-weight: $font-weight-light !important;
            padding: 0 !important;
        }
    }

    &-option {
        padding: 0 !important;
        color: $color-text !important;
        transition: all 0.3s ease;
        background-color: $color-input-background !important;

        &:hover {
            background-color: $color-gray-5 !important;
        }

        &.selected {
            background-color: $color-secondary-transparent !important;
            color: $color-text !important;
        }

        &-inner {
            @include flex(flex, row, nowrap, flex-start, center);
            border-radius: 0;
            font-size: 16px;
            font-weight: $font-weight-light;
            line-height: 130%; /* 20.8px */
            padding: 12px 18px !important;

            &-checkbox {
                @include flex(flex, row, nowrap, center, center);
                background-color: white;
                margin-right: 8px;
                padding: 2px;
                width: 16px;
                height: 16px;

                &.selected {
                    background-color: $color-secondary;
                    color: white !important;
                }

                svg {
                    width: 12px;
                    height: 12px;
                }
            }
        }
    }

    &-separator {
        display: none;
    }

    &-placeholder {
        padding: 0 !important;
        font-size: 16px !important;
        line-height: 130% !important;
        font-weight: $font-weight-semi-bold !important;
        opacity: 0.6;
    }

    &-value {
        &-container {
            padding: 0 !important;
            font-size: 16px !important;
            line-height: 130% !important;
            font-weight: $font-weight-light;
        }
    }

    &-disabled-cursor {
        cursor: not-allowed !important;

        * {
            cursor: not-allowed !important;
        }

        .selectbox-control {
            cursor: not-allowed !important;
        }

        .selectbox-value-container {
            cursor: not-allowed !important;
        }

        .selectbox-indicator {
            cursor: not-allowed !important;
        }
    }
}
