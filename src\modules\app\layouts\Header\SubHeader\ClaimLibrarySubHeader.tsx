import { Button, Col, Row, Space } from 'antd';
import { useState } from 'react';
import { ReactComponent as IconAdd } from '../../../../../assets/icons/add-sharp.svg';
import { canAccess } from '../../../../../common/canAccess';
import { UserRole } from '../../../../../store/user/types/User';
import Search from '../../../shared/Search';
import AddClaimModal from './AddClaimModal';
import classNames from 'classnames';

interface Props {
    searchTerm: string;
    onSearch: (searchTerm: string) => void;
    // myClaims: boolean;
    selectedTab: string;
    // onToggleClaims: (allClaims: boolean) => void;
    onSelectTab: (claim: string) => void;
    onAddSuccess: VoidFunction;
}

const ClaimLibrarySubHeader: React.FC<Props> = ({
    searchTerm,
    onSearch,
    // myClaims,
    selectedTab,
    // onToggleClaims,
    onSelectTab,
    onAddSuccess,
}) => {
    const [showAddModal, setShowAddModal] = useState(false);

    return (
        <Row className="header-sub claim-library-subheader" justify="space-between" align="stretch">
            <Col span={12}>
                <Space style={{ height: '100%' }}>
                    <div className="claim-library-subheader-title">Expense Portal</div>
                    <Button
                        className={classNames('btn-tab', {
                            'btn-secondary': selectedTab === 'my_claims',
                        })}
                        size="large"
                        type={selectedTab !== 'my_claims' ? 'text' : undefined}
                        // onClick={() => onToggleClaims(true)}
                        onClick={() => onSelectTab('my_claims')}
                    >
                        My Claims
                    </Button>
                    {canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) && (
                        <Button
                            className={classNames('btn-tab', {
                                'btn-secondary': selectedTab === 'all_claims',
                            })}
                            type={selectedTab !== 'all_claims' ? 'text' : undefined}
                            size="large"
                            // onClick={() => onToggleClaims(false)}
                            onClick={() => onSelectTab('all_claims')}
                        >
                            All Claims
                        </Button>
                    )}
                    <Button
                        className={classNames('btn-tab', {
                            'btn-secondary': selectedTab === 'recent',
                        })}
                        type={selectedTab !== 'recent' ? 'text' : undefined}
                        size="large"
                        onClick={() => onSelectTab('recent')}
                    >
                        Recent
                    </Button>
                </Space>
            </Col>
            <Col span={canAccess([UserRole.SUPER_ADMIN]) ? 10 : 8}>
                <Row gutter={[8, 8]} justify="end">
                    <Col
                        className={classNames('claim-library-subheader-search', {
                            wide: !canAccess([UserRole.SUPER_ADMIN]),
                        })}
                        xxl={canAccess([UserRole.SUPER_ADMIN]) ? 16 : 24}
                        xl={canAccess([UserRole.SUPER_ADMIN]) ? 14 : 24}
                        lg={canAccess([UserRole.SUPER_ADMIN]) ? 13 : 24}
                        md={canAccess([UserRole.SUPER_ADMIN]) ? 10 : 24}
                    >
                        <Search onSearch={onSearch} value={searchTerm} />
                    </Col>
                    {canAccess([UserRole.SUPER_ADMIN]) && (
                        <Col
                            xxl={8}
                            xl={10}
                            lg={11}
                            md={14}
                            className="claim-library-subheader-add"
                        >
                            <Button
                                className="btn-primary "
                                size="large"
                                onClick={() => setShowAddModal(true)}
                                block
                            >
                                <Space align="center">
                                    <IconAdd />
                                    <div>Add New Claim</div>
                                </Space>
                            </Button>
                        </Col>
                    )}
                </Row>
            </Col>
            {showAddModal && (
                <AddClaimModal
                    show={showAddModal}
                    onSuccess={onAddSuccess}
                    onClose={() => setShowAddModal(false)}
                />
            )}
        </Row>
    );
};

export default ClaimLibrarySubHeader;
