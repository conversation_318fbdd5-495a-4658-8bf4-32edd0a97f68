import 'dayjs/locale/en';
import 'reflect-metadata';
import { StoreProvider, useStoreRehydrated } from 'easy-peasy';
import React from 'react';
import * as ReactDOM from 'react-dom/client';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './init';
import App from './modules/app/App';
import { store } from './store';
import './styles/style.scss';
import { NotificationsProvider } from './modules/notifications/NotificationsContext';

interface WaitForStateRehydrationProps {
    children: React.ReactNode;
}

const WaitForStateRehydration: React.FC<WaitForStateRehydrationProps> = ({ children }) => {
    const isRehydrated = useStoreRehydrated();
    return isRehydrated ? (children as React.ReactElement) : null;
};

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

root.render(
    <StoreProvider store={store}>
        <WaitForStateRehydration>
            <NotificationsProvider>
                <App />
            </NotificationsProvider>
        </WaitForStateRehydration>
        <ToastContainer
            pauseOnHover={false}
            icon={true}
            position="top-left"
            autoClose={4000}
            limit={4}
            newestOnTop={true}
        />
    </StoreProvider>,
);
