import { useForm } from 'react-hook-form';
import Modal from '../../../shared/Modal';
import Form from '../../../shared/form/Form';
import TextField from '../../../shared/form/TextField';
import { Button } from 'antd';
import { useStoreActions } from '../../../../../store';
import { ClaimAddDto } from '../../../../../store/claim/dto/ClaimDto';
import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import FlashMessages from '../../../FlashMessages';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
}

const AddClaimModal: React.FC<Props> = ({ show, onClose, onSuccess }) => {
    const { add } = useStoreActions((actions) => actions.claim);

    const methods = useForm<ClaimAddDto>({
        resolver: classValidatorResolver(ClaimAddDto),
        defaultValues: { id: '' },
    });

    const onSubmit = async (fields) => {
        try {
            await add(fields.id);
            FlashMessages.success('Claim added');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to add claim');
        }
    };

    return (
        <Modal
            show={show}
            onClose={onClose}
            title="New Claim"
            subtitle={`If the sync does not happen properly with Salesforce, please enter the Salesforce Claim ID and click "Add Claim".`}
            className="add-claim-modal"
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                {/* <div className="add-claim-modal-title">Salesforce Claim ID</div> */}
                <TextField name="id" label="Salesforce Claim ID" className="margin-bottom-24" />
                <Button
                    className="btn btn-primary"
                    loading={methods.formState.isSubmitting}
                    htmlType="submit"
                    type="primary"
                    size="large"
                >
                    Add Claim
                </Button>
            </Form>
        </Modal>
    );
};
export default AddClaimModal;
