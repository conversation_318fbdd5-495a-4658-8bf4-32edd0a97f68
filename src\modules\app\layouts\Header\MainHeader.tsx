import { Col, Row, Space } from 'antd';

import { ReactComponent as <PERSON><PERSON> } from '../../../../assets/images/logo.svg';
import Actions from './Actions';
import { useNavigate } from 'react-router-dom';

const MainHeader: React.FC = () => {
    const navigate = useNavigate();

    return (
        <div className="header-main">
            <Row justify="space-between" align="middle">
                <Col>
                    <Space>
                        <Logo onClick={() => navigate('/')} className="logo" />
                    </Space>
                </Col>
                <Col>
                    <Actions />
                </Col>
            </Row>
        </div>
    );
};

export default MainHeader;
