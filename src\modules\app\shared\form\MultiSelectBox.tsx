import classNames from 'classnames';
import React, { ReactNode, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import Select, { components } from 'react-select';
import { ReactComponent as IconCheck } from '../../../../assets/icons/checkmark-sharp.svg';
import { ReactComponent as IconIndeterminate } from '../../../../assets/icons/remove-sharp.svg';

export interface SelectBoxOption {
    label: string;
    value: string | number | undefined;
}

export interface SelectBoxProps {
    disabled?: boolean;
    name: string;
    label?: string;
    options: SelectBoxOption[];
    formatOptionLabel?: (label, value) => ReactNode;
    onOptionChange?: (option: SelectBoxOption) => void;
    placeholder?: string;
}

const MultiSelectBox: React.FC<SelectBoxProps> = ({
    disabled,
    name,
    label,
    options,
    placeholder = 'Select',
}) => {
    const methods = useFormContext();

    const selectedOptions: string[] | undefined = methods.watch(name);

    const isAllSelected = useMemo(
        () => selectedOptions?.length === options.length,
        [options.length, selectedOptions?.length],
    );

    const isAllIndeterminate = useMemo(
        () => !!selectedOptions?.length && selectedOptions.length < options.length,
        [options.length, selectedOptions?.length],
    );

    const handleChange = (selected: SelectBoxOption[]) => {
        const isAllClicked = !!selected.find(({ value }) => value === '*');
        if (isAllClicked) {
            return;
        }

        const newValues = selected.filter(({ value }) => value !== '*');
        methods.setValue(
            name,
            newValues.map(({ value }) => value),
        );
        methods.trigger(name);
    };

    const handleSelectAll = useCallback(() => {
        // if all are selected, disselect all, otherwise, select all
        methods.setValue(
            name,
            selectedOptions?.length === options.length ? [] : options.map(({ value }) => value),
        );
    }, [methods, name, options, selectedOptions?.length]);

    const selectAllOption: SelectBoxOption = {
        label: 'All',
        value: '*',
    };

    const formattedSelectedOptions = (selectedOptions || []).map((value) =>
        options.find((option) => option.value === value),
    );

    const fieldState = methods.getFieldState(name);

    return (
        <div className="selectbox-container">
            {label && <label htmlFor={name}>{label}</label>}
            <Select
                placeholder={placeholder}
                value={formattedSelectedOptions}
                className="selectbox"
                isMulti
                isSearchable={false}
                options={[selectAllOption, ...options]}
                onChange={(selected) => handleChange(selected as SelectBoxOption[])}
                hideSelectedOptions={false}
                tabSelectsValue={false}
                closeMenuOnSelect={false}
                isClearable={false}
                components={{ Option, MultiValue }}
                isDisabled={disabled}
                classNames={{
                    control: () => 'selectbox-control',
                    valueContainer: () => 'selectbox-value-container',
                    dropdownIndicator: () => 'selectbox-indicator',
                    indicatorSeparator: () => 'selectbox-separator',
                    placeholder: () => 'selectbox-placeholder',
                    option: (state) =>
                        classNames({
                            'selectbox-option': true,
                            selected: state.isSelected || isAllSelected,
                        }),
                    menu: () => 'selectbox-menu',
                    menuList: () => 'selectbox-menu-list',
                    multiValue: () => 'selectbox-multivalue',
                    multiValueLabel: () => 'selectbox-multivalue-label',
                }}
                maxMenuHeight={600}
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                isAllSelected={isAllSelected}
                isAllIndeterminate={isAllIndeterminate}
                selectedOptions={selectedOptions}
                onSelectAll={handleSelectAll}
            />
            {!!fieldState.error && (
                <div
                    className={classNames(
                        {
                            note: true,
                        },
                        [fieldState.error.type],
                    )}
                >
                    {fieldState.error.message}
                </div>
            )}
        </div>
    );
};

const MultiValue = (props: any) => {
    const { data } = props;
    const { selectedOptions } = props.selectProps;

    const needsComma =
        selectedOptions &&
        selectedOptions?.length > 1 &&
        selectedOptions?.indexOf(data.value) !== (selectedOptions?.length || 0) - 1;

    props.components.Remove = () => null;

    if (data.value === '*') return null;

    return (
        <components.MultiValue {...props}>
            {props.children}
            {needsComma && ','}
        </components.MultiValue>
    );
};

const Option = (props: any) => {
    const { value } = props;
    const { isAllSelected, onSelectAll, isAllIndeterminate, selectedOptions } = props.selectProps;

    const isAllOption = value === '*';

    const isSelected = selectedOptions?.includes(value);

    return (
        <components.Option {...props}>
            <div onClick={() => isAllOption && onSelectAll()} className="selectbox-option-inner">
                <div
                    className={classNames({
                        'selectbox-option-inner-checkbox': true,
                        selected: isAllOption ? isAllSelected || isAllIndeterminate : isSelected,
                    })}
                >
                    {(isSelected || isAllSelected) && <IconCheck />}
                    {isAllOption && isAllIndeterminate && <IconIndeterminate />}
                </div>

                <div>{props.label}</div>
            </div>
        </components.Option>
    );
};

export default MultiSelectBox;
