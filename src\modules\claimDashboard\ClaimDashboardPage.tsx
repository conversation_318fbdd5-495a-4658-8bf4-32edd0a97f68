import { useCallback, useEffect } from 'react';
import { Outlet, Route, Routes, useNavigate, useParams } from 'react-router-dom';
import withAuth from '../../hooks/withAuth';
import { useStoreActions, useStoreState } from '../../store';
import { UserRole } from '../../store/user/types/User';
import DashboardLayout from '../app/layouts/DashboardLayout';
import ClaimDashboardSubHeader from '../app/layouts/Header/SubHeader/ClaimDashboardSubHeader';
import Spinner from '../app/shared/Spinner';
import { ClaimDashboard } from './subpages';
import ClaimChat from './subpages/ClaimChat';
import { ClaimExpenses } from './subpages/Expenses';
import Information from './subpages/Information';
import Reports from './subpages/Reports/ClaimReports';
import { ChatContextProvider } from '../chat';
import Invoices from './subpages/Invoices/ClaimInvoices';

const ClaimDashboardPage = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const { user } = useStoreState((state) => state.auth);
    const { claim } = useStoreState((state) => state.claim);
    const { getClaim, unloadClaim } = useStoreActions((actions) => actions.claim);

    const handleGetClaim = useCallback(() => {
        if (!id) return;
        getClaim(id);
    }, [getClaim, id]);

    useEffect(() => {
        !claim && handleGetClaim();
    }, [claim, handleGetClaim]);

    useEffect(() => {
        if (claim?.archivedAt && user?.role !== UserRole.SUPER_ADMIN) {
            navigate('/');
        }
    }, [claim?.archivedAt, navigate, user?.role]);

    useEffect(() => {
        return () => {
            unloadClaim();
        };
    }, [unloadClaim]);

    return (
        <DashboardLayout
            SubHeaderComponent={
                <ClaimDashboardSubHeader handleGetClaim={handleGetClaim} claim={claim} />
            }
        >
            {!claim && <Spinner type="skeleton" />}
            {claim && (
                <ChatContextProvider claim={claim}>
                    <Routes>
                        <Route path="" element={<ClaimDashboard claim={claim} />} />
                        <Route path="dashboard" element={<ClaimDashboard claim={claim} />} />
                        <Route path="expenses" element={<ClaimExpenses claim={claim} />} />
                        <Route
                            path="expenses/page/:pageNumber"
                            element={<ClaimExpenses claim={claim} />}
                        />
                        <Route path="expenses/new" element={<ClaimExpenses claim={claim} />} />
                        <Route path="reports" element={<Reports claim={claim} />} />
                        <Route path="reports/:reportId" element={<Reports claim={claim} />} />
                        <Route path="reports/new" element={<Reports claim={claim} />} />
                        <Route path="reports/request" element={<Reports claim={claim} />} />
                        <Route
                            path="reports/page/:pageNumber"
                            element={<Reports claim={claim} />}
                        />
                        <Route path="invoices" element={<Invoices claim={claim} />} />
                        <Route path="invoices/:reportId" element={<Invoices claim={claim} />} />
                        <Route path="invoices/new" element={<Invoices claim={claim} />} />
                        <Route path="invoices/request" element={<Invoices claim={claim} />} />
                        <Route
                            path="invoices/page/:pageNumber"
                            element={<Invoices claim={claim} />}
                        />
                        <Route path="chat" element={<ClaimChat />} />
                        <Route
                            path="information"
                            element={
                                <Information
                                    claim={claim}
                                    onClaimStatusChange={() => handleGetClaim()}
                                />
                            }
                        />
                    </Routes>
                    <Outlet />
                </ChatContextProvider>
            )}
        </DashboardLayout>
    );
};

export default withAuth(ClaimDashboardPage, [
    UserRole.ADJUSTER,
    UserRole.ADMIN,
    UserRole.SUPER_ADMIN,
]);
