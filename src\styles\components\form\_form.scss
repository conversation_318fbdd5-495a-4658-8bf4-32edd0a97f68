.form {
    label {
        display: block;
        font-size: 16px;
        font-weight: $font-weight-semi-bold;
        line-height: 130%;
        margin-bottom: 8px;
    }

    .icon {
        color: $color-secondary;
    }

    .text-field-container {
        @include flex(flex, column, nowrap, flex-start, flex-start);
        margin-bottom: 24px;
        position: relative;
        width: 100%;

        &-inner {
            @include flex(flex, row, nowrap, space-between, center);
            background-color: $color-input-background;
            padding: 0 18px;
            width: 100%;

            .icon {
                cursor: pointer;
            }

            input,
            textarea {
                background-color: $color-input-background !important;
                border: none;
                border-radius: 0;
                flex: 1;
                font-size: 16px;
                line-height: 20px;
                padding: 16px 0;
                resize: none;
                transition: 0.3s all ease;

                &:focus {
                    border: none;
                    box-shadow: none;
                }
            }

            textarea {
                height: 150px;
            }
        }
    }

    .note {
        color: $color-error;
        font-size: 14px;
        line-height: 1;
        margin-top: 8px;
        text-align: left;
        width: 100%;

        &:first-letter {
            text-transform: uppercase;
        }
    }
}
