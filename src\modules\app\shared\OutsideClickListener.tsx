import { useCallback, useEffect, useRef } from 'react';

interface Props {
    children: React.ReactNode;
    onOutsideClick: VoidFunction;
    className?: string;
}

const OutsideClickListener: React.FC<Props> = ({ children, onOutsideClick, className }) => {
    const containerRef = useRef<HTMLDivElement>(null);

    const handleOutsideClick = useCallback(
        (event) => {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                onOutsideClick();
            }
        },
        [onOutsideClick],
    );

    useEffect(() => {
        // Attach the event listener when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);

        // Detach the event listener when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
        };
    }, [handleOutsideClick]);

    return (
        <div style={{ width: '100%', height: '100%' }} ref={containerRef} className={className}>
            {children}
        </div>
    );
};

export default OutsideClickListener;
