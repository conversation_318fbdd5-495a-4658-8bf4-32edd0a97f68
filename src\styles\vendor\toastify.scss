.Toastify {
    &__progress-bar {
        opacity: 1;
        z-index: 2 !important;

        &--error {
            background: $color-error !important;
        }

        &--success {
            background: $color-success !important;
        }

        &--warning {
            background: $color-warning !important;
        }
    }

    &__toast {
        @include flex(flex, row, nowrap, flex-start, center);
        background-color: $color-text !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        gap: 16px;
        padding: 16px !important;

        x &:after {
            bottom: 0;
            content: '';
            height: 5px;
            left: 0;
            position: absolute;
            width: 100%;
        }

        > svg {
            align-self: flex-start;
            color: #fff !important;
            cursor: pointer;
            font-size: 20px;
        }

        &-body {
            color: #fff !important;
            font-size: 14px !important;
            margin: 0 !important;
            padding: 0 !important;
            word-break: break-all;

            > div {
                @include flex(flex, row, nowrap, flex-start, center);
            }

            .content {
                @include flex(flex, column, nowrap, flex-start, flex-start);

                .message {
                    color: white;
                    font-size: 12px;
                    line-height: 17px;
                }

                .status {
                    font-size: 20px;
                    font-weight: $font-weight-bold;
                    line-height: 24px;
                    text-transform: capitalize;

                    &-error {
                        color: $color-error;
                    }

                    &-success {
                        color: $color-secondary;
                    }

                    &-warning {
                        color: $color-warning;
                    }
                }
            }

            .icon {
                @include flex(flex, row, nowrap, center, center);
                background-color: white;
                border-radius: 50%;
                font-size: 20px;
                height: 32px;
                width: 32px;
                margin-right: 16px;

                &-error {
                    background-color: $color-error;
                    color: white !important;
                }

                &-success {
                    background-color: $color-secondary;
                    color: white !important;
                }

                &-warning {
                    background-color: $color-warning;
                    color: white !important;
                }

                svg {
                    height: 20px !important;
                    width: 20px !important;
                }
            }
        }

        &-container {
            background-color: transparent !important;
            border-radius: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .icon-close {
            display: block;
            align-self: flex-start !important;
            height: 16px !important;
            margin: 0;
            width: 16px !important;

            path {
                fill: #fff !important;
            }
        }

        &--error {
            &:after {
                background-color: transparentize($color-error, 0.5) !important;
            }
        }

        &--success {
            &:after {
                background-color: transparentize($color-success, 0.5) !important;
            }
        }

        &--warning {
            &:after {
                background-color: transparentize($color-warning, 0.5) !important;
            }
        }
    }
}
