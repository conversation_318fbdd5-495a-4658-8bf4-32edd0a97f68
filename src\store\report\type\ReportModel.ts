import { Action, Thunk } from 'easy-peasy';
import {
    ReportApproveDto,
    ReportCreateDto,
    ReportDto,
    ReportPaginateDto,
    ReportRejectDto,
} from '../dto/ReportDto';
import { Resource, ResourcePagination } from '../../app/types';

export default interface ReportModel {
    // state
    list: Resource<any[]>;
    pagination: ResourcePagination | null;

    // actions
    loading: Action<ReportModel>;
    load: Action<ReportModel, ReportDto[]>;
    setPagination: Action<ReportModel, ResourcePagination>;
    unload: Action<ReportModel>;

    // thunks
    get: Thunk<ReportModel, ReportPaginateDto>;
    getReport: Thunk<ReportModel, string>;
    getReportPreview: Thunk<ReportModel, ReportCreateDto>;
    getReportRequest: Thunk<ReportModel, ReportCreateDto>;
    add: Thunk<ReportModel, ReportCreateDto>;
    delete: Thunk<ReportModel, string>;
    checkIfPdfReady: Thunk<ReportModel, string>;
    getPDF: Thunk<ReportModel, string>;
    getCSV: Thunk<ReportModel, string>;
    sendToAdjuster: Thunk<ReportModel, string>;
    sendAnalytics: Thunk<ReportModel, string>;
    withdrawAnalytics: Thunk<ReportModel, string>;
    approve: Thunk<ReportModel, { id: string; dto: ReportApproveDto }>;
    reject: Thunk<ReportModel, { id: string; dto: ReportRejectDto }>;
}
