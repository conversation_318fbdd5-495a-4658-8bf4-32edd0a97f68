import { Button } from 'antd';
import { endOfDay, endOfToday, startOfDay } from 'date-fns';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { InvoiceFilterDto } from '../../../../store/invoice/dto/InvoiceDto';
import { InvoiceFilterParam } from '../../../../store/invoice/type/Invoice';
import { UserRole } from '../../../../store/user/types/User';
import Modal from '../../../app/shared/Modal';
import DatePickerRange from '../../../app/shared/form/DatePickerRange';
import Form from '../../../app/shared/form/Form';
import MultiSelectBox from '../../../app/shared/form/MultiSelectBox';
import SelectBox from '../../../app/shared/form/SelectBox';
import { InvoiceStatusOptions, TypeOptions, mapFilterParams } from './utils';

interface Props {
    filterParams: InvoiceFilterParam[];
    onFilterChange: (filterParams: InvoiceFilterParam[]) => void;
    claim: ClaimDto;
    show: boolean;
    onClose: VoidFunction;
}

const FilterModal: React.FC<Props> = ({ filterParams, onFilterChange, claim, show, onClose }) => {
    const mappedParams = mapFilterParams(filterParams);

    const methods = useForm<InvoiceFilterDto>({
        defaultValues: {
            status: [],
            ...mappedParams,
            dateRange: mappedParams.dateRange?.length
                ? [mappedParams.dateRange[0], mappedParams.dateRange[1]]
                : undefined,
        },
    });

    const onSubmit = (fields) => {
        const params: InvoiceFilterParam[] = [];

        if (fields.dateRange) {
            const { dateRange } = fields;
            if ((dateRange[0] && !dateRange[1]) || (!dateRange[0] && dateRange[1])) {
                methods.setError('dateRange', { message: 'Both dates need to be defined' });
                return;
            }
            params.push({
                field: 'created_at',
                operator: 'between',
                value: [startOfDay(dateRange[0]), endOfDay(dateRange[1])],
            });
        }

        if (fields.type) {
            params.push({
                field: 'type',
                operator: '=',
                value: fields.type,
            });
        }

        if (fields.status && fields.status.length) {
            params.push({
                field: 'status',
                operator: 'in',
                value: fields.status,
            });
        }

        onFilterChange(params);
        onClose();
    };

    const onClearFilter = useCallback(() => {
        methods.reset({ type: undefined, status: undefined, dateRange: undefined });
        onFilterChange([]);
    }, [methods, onFilterChange]);

    const UserInvoiceStatusOptions = canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])
        ? InvoiceStatusOptions
        : InvoiceStatusOptions.filter(({ value }) => value !== 'created');

    return (
        <Modal
            show={show}
            onClose={onClose}
            title="Filter"
            subtitle="Please enter the custom filters for your invoice."
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <SelectBox options={TypeOptions} label="Type" name="type" />
                <MultiSelectBox
                    options={UserInvoiceStatusOptions}
                    label="Invoice Status"
                    name="status"
                />
                <DatePickerRange
                    name="dateRange"
                    label="Date Range"
                    max={endOfToday()}
                    min={startOfDay(new Date(claim.lossDate))}
                />
            </Form>
            <div className="flex-start">
                <Button
                    type="primary"
                    size="large"
                    className="btn-primary margin-right-24"
                    loading={methods.formState.isSubmitting}
                    htmlType="submit"
                    onClick={() => methods.handleSubmit(onSubmit)()}
                >
                    Apply Filter
                </Button>
                <Button type="link" onClick={() => onClearFilter()} className="btn-text">
                    Clear
                </Button>
            </div>
        </Modal>
    );
};
export default FilterModal;
