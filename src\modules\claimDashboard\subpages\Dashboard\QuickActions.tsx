import { Button } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ReactComponent as PlusIcon } from '../../../../assets/icons/add-sharp.svg';
import { ReactComponent as ChatIcon } from '../../../../assets/icons/chatbox-outline.svg';
import { ReactComponent as ExpenseIcon } from '../../../../assets/icons/receipt-outline.svg';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { useStoreActions } from '../../../../store';
import { useState } from 'react';
import FlashMessages from '../../../app/FlashMessages';
import { isOpen } from '../../../../common/claim';
import { canAccess } from '../../../../common/canAccess';
import { UserRole } from '../../../../store/user/types/User';
import Dialog from '../../../app/shared/Dialog';

interface Props {
    claim: ClaimDto;
}

export const QuickActions: React.FC<Props> = ({ claim }) => {
    const navigate = useNavigate();

    const [showReminderDialog, setShowReminderDialog] = useState(false);
    const [reminderDialogDisabled, setReminderDialogDisabled] = useState(false);

    const { sendReminder } = useStoreActions((actions) => actions.claim);

    const onAddExpenseReminder = async () => {
        setReminderDialogDisabled(true);
        try {
            await sendReminder(claim.id);
            FlashMessages.success('Expense Reminder Sent');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send Reminder');
        } finally {
            setReminderDialogDisabled(false);
            setShowReminderDialog(false);
        }
    };

    if (claim.archivedAt || (!isOpen(claim) && !canAccess([UserRole.SUPER_ADMIN]))) {
        return null;
    }

    return (
        <div className="quick-actions box">
            <div className="heading medium quick-actions-title">Quick Actions</div>
            <div className="quick-actions-list">
                <Button
                    className="btn-primary"
                    size="large"
                    type="primary"
                    onClick={() => navigate(`/claim/${claim.id}/expenses/new`)}
                >
                    <PlusIcon />
                    Add Expenses
                </Button>
                <Button
                    className="btn-primary"
                    size="large"
                    type="primary"
                    onClick={() =>
                        navigate(`/claim/${claim.id}/reports/new`, {
                            state: {
                                quickAddReprot: true,
                            },
                        })
                    }
                >
                    <PlusIcon />
                    <span>Create Report</span>
                </Button>
                <Button
                    className="btn-secondary"
                    size="large"
                    onClick={() => navigate(`/claim/${claim.id}/chat`)}
                >
                    <ChatIcon />
                    <span>Chat</span>
                </Button>
                <Button
                    className="btn-secondary"
                    size="large"
                    onClick={() => setShowReminderDialog(true)}
                >
                    <ExpenseIcon />
                    <span>Add Expense Reminder</span>
                </Button>
                <Button
                    className="btn-secondary"
                    size="large"
                    style={{ backgroundColor: '#D6DCDB' }}
                    onClick={() =>
                        navigate(`/claim/${claim.id}/reports/request`, {
                            state: {
                                quickRequestReprot: true,
                            },
                        })
                    }
                >
                    <ExpenseIcon />
                    <span>Report Request</span>
                </Button>
            </div>
            <Dialog
                onCancel={() => setShowReminderDialog(false)}
                onOk={() => onAddExpenseReminder()}
                show={!!showReminderDialog}
                disabled={reminderDialogDisabled}
            />
        </div>
    );
};
