import { Input } from 'antd';
import classNames from 'classnames';
import { Controller, useFormContext } from 'react-hook-form';

interface Props {
    name: string;
    label?: string;
    placeholder?: string;
    disabled?: boolean;
    value?: string;
    className?: string;
}

const SearchField: React.FC<Props> = ({
    name,
    label,
    placeholder = 'Search...',
    disabled,
    value,
    className,
}) => {
    const { control, getValues, setValue } = useFormContext();

    return (
        <Controller
            name={name}
            control={control}
            render={({ field, fieldState, formState }) => (
                <div className={classNames('search-field-container', className)}>
                    {label && <label htmlFor={name}>{label}</label>}

                    <Input
                        autoComplete="nope"
                        {...field}
                        disabled={disabled || formState.isSubmitting}
                        type="text"
                        placeholder={placeholder}
                        value={value || getValues(name)}
                        onChange={(v) => setValue(name, v.target.value)}
                        status={fieldState.error ? 'error' : undefined}
                    />
                </div>
            )}
        />
    );
};
export default SearchField;
