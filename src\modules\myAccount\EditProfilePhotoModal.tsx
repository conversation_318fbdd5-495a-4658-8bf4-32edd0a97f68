import { Button } from 'antd';
import React, { ChangeEvent, useRef, useState } from 'react';
import AvatarEditor from 'react-avatar-editor';
import { useForm } from 'react-hook-form';
import { useStoreActions, useStoreState } from '../../store';
import { DocumentPathTypes } from '../../store/documents/types/Documents';
import { mapUser } from '../../store/user/mappers';
import FlashMessages from '../app/FlashMessages';
import Avatar from '../app/shared/Avatar';
import Modal from '../app/shared/Modal';
import Form from '../app/shared/form/Form';
import NumberRangePicker from '../app/shared/form/NumberRangePicker';
import { MAX_IMAGE_SIZE } from '../../store/chat/utils';

const base64ToBlob = (base64: string, mime = ''): Blob => {
    const sliceSize = 1024;
    const byteChars = window.atob(base64);

    const byteArrays = Array.from(
        {
            length: Math.ceil(byteChars.length / sliceSize),
        },
        (_, i) => {
            const start = i * sliceSize;
            const slice = byteChars.slice(start, start + sliceSize);
            const byteNumbers = Array.from(slice).map((char) => char.charCodeAt(0));
            return new Uint8Array(byteNumbers);
        },
    );

    return new Blob(byteArrays, {
        type: mime,
    });
};

interface Props {
    onClose: VoidFunction;
    onSuccess: (action: 'update' | 'remove') => void;
    show: boolean;
}

const EditProfilePhotoModal: React.FC<Props> = ({ onSuccess, onClose, show }) => {
    const [tempFile, setTempFile] = useState<File | undefined>(undefined);
    const editor = useRef<any>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [nextStep, setNextStep] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState(false);

    const { user } = useStoreState((state) => state.auth);
    const { loadAuthUser } = useStoreActions((actions) => actions.auth);
    const { getDocuments, uploadFileToS3 } = useStoreActions((actions) => actions.documents);
    const { updatePhoto } = useStoreActions((state) => state.profile);

    const methods = useForm<{ scale: number }>({
        defaultValues: { scale: 1.2 },
    });
    const scale = methods.watch('scale');

    const onSave = async () => {
        if (!tempFile) {
            return;
        }

        const base64Image = editor.current?.getImage().toDataURL();

        setIsLoading(true);

        try {
            const [signedImage] = await getDocuments({ path: DocumentPathTypes.PROFILE });

            const base64ImageContent = base64Image.split(',')[1];
            const mime = base64Image.split(',')[0].split(':')[1].split(';')[0];
            const blob = base64ToBlob(base64ImageContent, mime);

            await uploadFileToS3({ file: blob as File, presignedUrl: signedImage.presignedUrl });

            const userData = await updatePhoto(signedImage.id);
            loadAuthUser(mapUser(userData));
            onSuccess('update');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to update photo');
            setNextStep(false);
        } finally {
            setNextStep(false);
            setIsLoading(false);
        }
    };

    const onRemovePhoto = async () => {
        try {
            const userData = await updatePhoto(null);
            loadAuthUser(mapUser(userData));
            onSuccess('remove');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to remove photo');
        } finally {
            setNextStep(false);
            onClose();
        }
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) {
            return null;
        }

        if (e.target.files[0].size > MAX_IMAGE_SIZE) {
            FlashMessages.warn('Maximum Upload Size is 10MB');
            return;
        }

        setTempFile(e.target.files[0]);
        setNextStep(true);
        return null;
    };

    if (!user) return null;

    return (
        <Modal
            onClose={onClose}
            show={show}
            title={nextStep ? 'Set Profile Photo' : 'Change Profile Photo'}
            className="account-modal-upload"
        >
            {!nextStep && (
                <>
                    <div className="flex-center margin-bottom-8">
                        <Avatar photoUrl={user.profilePhoto} name={user.name} size="large" />
                    </div>
                    <div className="flex-center margin-bottom-40">Current photo</div>

                    <div>
                        <input
                            ref={fileInputRef}
                            type="file"
                            hidden={true}
                            onChange={handleFileChange}
                            accept=".jpg, .jpeg, .png"
                        />
                        <Button
                            onClick={() => fileInputRef.current && fileInputRef.current.click()}
                            className="btn-primary margin-bottom-4"
                            size="large"
                            block
                        >
                            Upload photo
                        </Button>

                        {user.profilePhoto && (
                            <Button
                                type="link"
                                className="btn-text color-warning"
                                size="large"
                                onClick={() => onRemovePhoto()}
                                block
                            >
                                Remove photo
                            </Button>
                        )}
                    </div>
                </>
            )}

            {nextStep && (
                <>
                    <div className="account-modal-upload-editor">
                        <div className="account-modal-upload-editor-inner margin-bottom-12">
                            <AvatarEditor
                                ref={editor}
                                borderRadius={200}
                                image={tempFile}
                                border={20}
                                scale={scale}
                                rotate={0}
                            />
                        </div>
                    </div>
                    <Form methods={methods} onSubmit={methods.handleSubmit(onSave)}>
                        <NumberRangePicker
                            name="scale"
                            min={0}
                            max={2}
                            step={0.01}
                            defaultValue={1.2}
                            isRange={false}
                        />
                    </Form>
                    <Button
                        className="btn-primary margin-top-auto"
                        onClick={onSave}
                        size="large"
                        loading={isLoading || methods.formState.isSubmitting}
                        block
                    >
                        Save
                    </Button>
                </>
            )}
        </Modal>
    );
};

export default EditProfilePhotoModal;
