import React from 'react';
import { Navigate } from 'react-router-dom';
import { useStoreState } from '../store';
import { UserRole } from '../store/user/types/User';

interface WithProps {
    WrappedComponent: React.ComponentType;
    expectedAuth: boolean;
    location: string;
    roles?: UserRole[];
}

export default function withAuthRedirect({
    WrappedComponent,
    expectedAuth,
    location,
    roles,
}: WithProps) {
    return (props) => {
        const { isAuthenticated, userRole } = useStoreState((state) => state.auth);

        if (expectedAuth !== isAuthenticated) {
            return <Navigate to={location} />;
        }

        if (userRole && roles && !roles.includes(userRole)) {
            return <Navigate to={'/'} />;
        }

        return <WrappedComponent {...props} />;
    };
}
