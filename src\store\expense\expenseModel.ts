import { action, thunk } from 'easy-peasy';
import ExpenseModel from './types/ExpenseModel';
import { api } from '../../common/api';
import { transition } from '../../types';
import { mapExpense, mapExpenses } from './mappers';
import { PaginationDirection, mapPagination } from '../../utils/paginate';
import { DEFAULT_EXPENSE_PER_PAGE, ExpenseOrderBy } from './types/Expense';
import { format } from 'date-fns';
import { API_DATE_TIME_FORMAT } from '../../utils/dateFormat';

const expenseModel: ExpenseModel = {
    // state
    list: transition.reset([]),
    pagination: null,

    // actions
    load: action((state, expenses) => {
        state.list = transition.loaded(expenses);
    }),

    loading: action((state) => {
        state.list = transition.loading(state.list.value);
    }),

    unload: action((state) => {
        state.list = transition.reset([]);
        state.pagination = null;
    }),

    setPagination: action((state, pagination) => {
        state.pagination = pagination;
    }),

    // thunks
    get: thunk(async (actions, { claimId, dto }) => {
        dto.page = dto.page || 1;
        dto.direction = dto.direction || PaginationDirection.DESC;
        dto.order_by = dto.order_by || ExpenseOrderBy.DATE;
        dto.meta_group_by = dto.meta_group_by || 'vendor';
        dto.limit = dto.limit || DEFAULT_EXPENSE_PER_PAGE;

        const dateData = dto.params?.find((item) => item.field === 'date');

        if (dateData) {
            dateData.value = (dateData.value as Date[]).map((item) =>
                format(item, API_DATE_TIME_FORMAT),
            );
        }

        actions.loading();
        const {
            data: { data, meta },
        } = await api.get(`/claims/${claimId}/expenses`, { params: dto });

        if (dateData) {
            dateData.value = (dateData.value as Date[]).map((item) => new Date(item));
        }

        actions.load(mapExpenses(data));
        actions.setPagination(mapPagination(meta));

        return mapExpenses(data);
    }),

    getExpense: thunk(async (actions, id) => {
        const res = await api.get(`/expenses/${id}`);
        return mapExpense(res.data.data);
    }),

    add: thunk(async (actions, dto) => {
        await api.post('/expenses', dto);
    }),

    update: thunk(async (actions, { id, dto }) => {
        const res = await api.post(`/expenses/${id}`, dto);
        return mapExpense(res.data.data);
    }),

    archive: thunk(async (actions, ids) => {
        await api.post('/expenses/archive', { ids });
    }),

    restore: thunk(async (actions, ids) => {
        await api.post('/expenses/restore', { ids });
    }),

    view: thunk(async (actions, id) => {
        await api.post(`/expenses/${id}/view`);
    }),

    requestReceipt: thunk(async (actions, id) => {
        await api.get(`/expenses/${id}/request-receipt`);
    }),
};
export default expenseModel;
