import type { MenuProps } from 'antd';
import { But<PERSON>, Menu } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useStoreActions, useStoreState } from '../../../../store';
import { allNavItems, communicationNavItems, getInitialNavKey, mainNavItems } from './utils';
import { ReactComponent as IconDot } from '../../../../assets/icons/icon-dot.svg';

const Nav: React.FC = () => {
    const { id: claimId } = useParams();
    const { pathname } = useLocation();
    const navigate = useNavigate();

    const [selectedKey, setSelectedKey] = useState(['']);

    const { hasUnread: hasUnreadChat } = useStoreState((state) => state.chat);
    const { setUnreadCount } = useStoreActions((actions) => actions.chat);
    const { claim } = useStoreState((state) => state.claim);

    useEffect(() => {
        const initialNavKey = getInitialNavKey(allNavItems, pathname);

        setSelectedKey([initialNavKey]);
    }, [pathname]);

    const communicationItems = useMemo(
        () =>
            communicationNavItems?.map((item) => {
                if (!hasUnreadChat) return item;

                return {
                    ...item,
                    label: (
                        <div className="flex-start">
                            <span className="margin-right-12">Chat</span>
                            <IconDot className="color-error" />
                        </div>
                    ),
                };
            }) as MenuProps['items'],
        [hasUnreadChat],
    );

    const onMenuItemClick: MenuProps['onClick'] = ({ key }) => {
        if (key === 'chat') {
            setUnreadCount(0);
        }
        setSelectedKey([key]);
        navigate(`/claim/${claimId}/${key}`);
    };

    return (
        <div className="sider-nav">
            <div className="sider-nav-submenu">
                <div className="sider-nav-submenu-title">Main</div>
                <Menu
                    className="sider-nav-menu"
                    onClick={onMenuItemClick}
                    items={mainNavItems}
                    mode="inline"
                    selectedKeys={selectedKey}
                />
            </div>
            <div className="sider-nav-submenu">
                <div className="sider-nav-submenu-title">General Communication</div>
                <Menu
                    className="sider-nav-menu"
                    items={communicationItems}
                    mode="inline"
                    onClick={onMenuItemClick}
                    selectedKeys={selectedKey}
                />
            </div>

            {!!claim?.archivedAt ? (
                <Button
                    size="large"
                    className="btn btn-ghost"
                    onClick={() => navigate('/settings/archived-claims')}
                    block
                >
                    Archived Claims
                </Button>
            ) : (
                <Button
                    size="large"
                    className="btn btn-secondary outline"
                    onClick={() => navigate('/')}
                    block
                >
                    Expense Portal
                </Button>
            )}
        </div>
    );
};

export default Nav;
