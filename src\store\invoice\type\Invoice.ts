import { FilterParamsOperator } from '../../../types';

export interface RawInvoice {
    id: string;
    claim_id: string;
    start_date: string;
    end_date: string;
    expenses_count: string;
    type: string;
    status: InvoiceStatus;
    submitted_count?: number;
    audited_count?: number;
    recommended_reimbursement?: number;
    cost_savings?: number;
    in_review_count?: number;
    approved_count?: number;
    declined_count?: number;
    expenses_category: string[];
    invoice_url?: string;
    html: string;
    metadata: RawInvoiceMetadata;
}

export interface RawInvoiceMetadata {
    cost_savings?: string;
    expenses_by_status_audited_amount?: string;
    expenses_by_status_audited_count?: number;
    expenses_by_status_in_review_amount?: string;
    expenses_by_status_in_review_count?: number;
    expenses_by_status_submitted_amount?: string;
    expenses_by_status_submitted_count?: number;
    recommended_reimbursement?: string;
}

export enum RawInvoiceType {
    AUTOMATIC = 'adminAutomatic',
    MANUAL = 'adminManual',
}

export enum InvoiceType {
    AUTOMATIC = 'Automatic',
    MANUAL = 'Manual',
}

export type InvoiceStatus = 'Open' | 'Held' | 'Uploaded to QBs' | 'open' | 'held' | 'uploadedtoqb';

export const invoiceStatusMap = {
    open: 'Open',
    held: 'Held',
    uploadedtoqb: 'Uploaded to QBs',
    rejected: 'Rejected',
    preparing: 'Preparing',
};

export type InvoiceFilterableFields =
    | 'claim_id'
    | 'start_date'
    | 'end_date'
    | 'expenses_count'
    | 'type'
    | 'status'
    | 'category'
    | 'created_at';

export type InvoiceOrderBy =
    | 'claim_id'
    | 'start_date'
    | 'end_date'
    | 'expenses_count'
    | 'type'
    | 'status'
    | 'category';

export interface InvoiceFilterParam {
    field: InvoiceFilterableFields;
    operator: FilterParamsOperator;
    value: number | string | string[] | number[] | Date[];
}
