.notifications {
    &-mark-read {
        cursor: pointer;
        font-size: 16px;
        font-weight: $font-weight-semi-bold;
        text-decoration-line: underline;
        transition: all 0.3s ease;

        &.disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
    }

    &-card {
        border: 1px solid $color-gray-5;
        box-sizing: border-box !important;
        cursor: pointer;
        padding: 16px;
        transition: all 0.3s ease;

        &:not(:last-child) {
            border-bottom: none;
        }

        &.unread {
            background-color: transparentize($color-primary, 0.8);
        }

        &-dot {
            color: $color-error;
        }

        &-message {
            font-size: 14px;
            font-weight: $font-weight-light;
            line-height: 137%;
        }

        &-timestamp {
            font-size: 14px;
            font-weight: $font-weight-semi-bold;
            line-height: 137%; /* 19.18px */
        }

        &-title {
            font-size: 18px;
            font-weight: $font-weight-bold;
            line-height: 120%; /* 21.6px */
        }
    }
}
