import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Row } from 'antd';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions, useStoreState } from '../../store';
import { ProfileUpdateDto } from '../../store/profile/dto/ProfileDto';
import { UserRole } from '../../store/user/types/User';
import { ContactMethod } from '../../types';
import { capitalizeString } from '../../utils/strings';
import FlashMessages from '../app/FlashMessages';
import Modal from '../app/shared/Modal';
import Form from '../app/shared/form/Form';
import { SelectBoxOption } from '../app/shared/form/MultiSelectBox';
import SelectBox from '../app/shared/form/SelectBox';
import TextField from '../app/shared/form/TextField';

interface Props {
    onSuccess: VoidFunction;
    onClose: VoidFunction;
    show: boolean;
}

const PreferedContactMethods: SelectBoxOption[] = Object.values(ContactMethod).map((method) => ({
    value: method,
    label: capitalizeString(method),
}));

const EditAccountModal: React.FC<Props> = ({ onClose, show, onSuccess }) => {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { data: profile } = useStoreState((state) => state.profile);
    const { update } = useStoreActions((actions) => actions.profile);

    const methods = useForm<ProfileUpdateDto>({
        resolver: classValidatorResolver(ProfileUpdateDto),
        defaultValues: {
            name: profile?.name,
            phone: profile?.phone,
            company: profile?.company,
            job_title: profile?.jobTitle,
            preferred_contact_method: profile?.contactMethod,
            role: profile?.role,
        },
    });

    const onSubmit = async (fields) => {
        setIsSubmitting(true);
        try {
            await update(fields);
            onSuccess();
        } catch (err: any) {
            FlashMessages.error('Profile not updated');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <Modal
            title="Personal Information"
            subtitle='Click "Save" to keep the changes made to your profile. If you do not wish to keep the changes, click "Cancel".'
            show={show}
            onClose={() => onClose()}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                <TextField disabled={isSubmitting} name="name" label="Full Name" />
                <TextField
                    disabled
                    type="email"
                    name="email"
                    label="Email"
                    value={profile?.email}
                />
                <TextField disabled={isSubmitting} name="phone" label="Phone Number" />

                <SelectBox
                    options={PreferedContactMethods}
                    disabled={isSubmitting}
                    name="preferred_contact_method"
                    label="Preferred Method of Contact"
                    placeholder={capitalizeString(profile?.contactMethod || '') || 'Select...'}
                />
                {profile?.role === UserRole.ADJUSTER && (
                    <>
                        <TextField disabled={isSubmitting} name="company" label="Company" />
                        <TextField disabled={isSubmitting} name="job_title" label="Title" />{' '}
                    </>
                )}
                <div className="edit-account-actions">
                    <Row justify="space-between" gutter={8} align="stretch">
                        <Col span={24}>
                            <Button
                                htmlType="submit"
                                size="large"
                                className="btn-primary margin-bottom-4"
                                loading={methods.formState.isSubmitting}
                                block
                            >
                                Save
                            </Button>
                        </Col>
                        <Col span={24}>
                            <Button
                                onClick={() => onClose()}
                                type="link"
                                className="btn-text"
                                size="large"
                                disabled={methods.formState.isSubmitting}
                                block
                            >
                                Cancel
                            </Button>
                        </Col>
                    </Row>
                </div>
            </Form>
        </Modal>
    );
};
export default EditAccountModal;
