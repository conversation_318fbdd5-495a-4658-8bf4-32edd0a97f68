import { Col, Row, Space } from 'antd';
import classNames from 'classnames';
import { formatDistanceStrict } from 'date-fns';
import { NotificationDto } from '../../store/notifications/dto/NotificationDto';
import { ReactComponent as IconDot } from '../../assets/icons/icon-dot.svg';

interface Props {
    notification: NotificationDto;
    onClick: (notification: NotificationDto) => void;
}

const NotificationCard: React.FC<Props> = ({ notification, onClick }) => {
    return (
        <Row
            gutter={[16, 0]}
            align={'stretch'}
            className={classNames('notifications-card', { unread: !notification.readAt })}
            onClick={() => onClick(notification)}
            style={{ margin: 0 }}
        >
            <Col
                span={1}
                className="flex-center notifications-card-dot"
                style={{ padding: '0', alignItems: 'flex-start', paddingTop: '6px' }}
            >
                {!notification.readAt && <IconDot />}
            </Col>
            <Col span={23}>
                <Space direction="vertical">
                    <div className="notifications-card-title">{notification.data.title}</div>
                    <div className="notifications-card-message">{notification.data.message}</div>
                    <div className="notifications-card-timestamp">
                        {formatDistanceStrict(notification.createdAt, new Date())} ago
                    </div>
                </Space>
            </Col>
        </Row>
    );
};
export default NotificationCard;
