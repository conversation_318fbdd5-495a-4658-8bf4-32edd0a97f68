import { Button } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useEffect, useState } from 'react';
import { canAccess } from '../../../../common/canAccess';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ReportDto } from '../../../../store/report/dto/ReportDto';
import { UserRole } from '../../../../store/user/types/User';
import FlashMessages from '../../../app/FlashMessages';
import ContextMenu from '../../../app/shared/ContextMenu';
import Modal from '../../../app/shared/Modal';
import Spinner from '../../../app/shared/Spinner';
import TransformContainer from '../../../app/shared/TransformContainer';
import ReportDetails from './Details';
import ReviewModal from './ReviewModal';
import { adjusterActionMenuItems } from './utils';
import classNames from 'classnames';

interface Props {
    report: ReportDto;
    claim: ClaimDto;
    show: boolean;
    onClose: VoidFunction;
    onActionClick: (item: MenuItemType | undefined, report: ReportDto) => void;
    onSuccess: VoidFunction;
}

const DetailsModal: React.FC<Props> = ({
    report,
    claim,
    show,
    onClose,
    onActionClick,
    onSuccess,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [reportHtml, setReportHtml] = useState<string>();
    const [showReviewModal, setShowReviewModal] = useState<'approve' | 'reject' | false>(false);
    const [shouldSend, setShouldSend] = useState(false);

    const { sendToAdjuster } = useStoreActions((actions) => actions.report);
    const { getReportHtml } = useStoreActions((actions) => actions.claim);

    useEffect(() => {
        getReportHtml(report.id).then(setReportHtml);
    }, [getReportHtml, report.id]);

    const handleSendToAdjuster = async () => {
        if (!shouldSend) {
            return;
        }

        setIsLoading(true);
        try {
            await sendToAdjuster(report.id);
            FlashMessages.success('Report Sent');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Modal
            className="claim-reports-details"
            show={show}
            onClose={onClose}
            title="Report Preview"
            subtitle="Please review the details of the report before you send it to adjuster for approval using the buttons below."
            sideContent={
                <TransformContainer
                    additionalActions={
                        <ContextMenu
                            toolTipContent="Download"
                            items={adjusterActionMenuItems}
                            onItemClick={(item) => onActionClick(item, report)}
                            hasTooltip={false}
                        />
                    }
                >
                    {!reportHtml && <Spinner type="skeleton" transparent />}
                    {reportHtml && (
                        <div
                            className="claim-reports-preview"
                            dangerouslySetInnerHTML={{ __html: reportHtml }}
                        />
                    )}
                </TransformContainer>
            }
            sideContentProps={{ size: 'large' }}
        >
            <ReportDetails
                report={report}
                claim={claim}
                canSend
                onToggleSend={() => setShouldSend(!shouldSend)}
            />

            {canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) && (
                <div className="actions">
                    {!claim.archivedAt && (
                        <Button
                            size="large"
                            className={classNames('btn btn-primary', { disabled: !shouldSend })}
                            onClick={() => handleSendToAdjuster()}
                            loading={isLoading}
                            disabled={!shouldSend}
                        >
                            Send
                        </Button>
                    )}
                    <Button type="link" size="large" onClick={() => onClose()} disabled={isLoading}>
                        Go Back
                    </Button>
                </div>
            )}

            {canAccess([UserRole.ADJUSTER]) &&
                !['rejected', 'approved'].includes(report.status) && (
                    <div className="actions">
                        <Button
                            size="large"
                            className={classNames('btn-primary', {
                                disabled:
                                    report.status === 'approved' || report.status === 'rejected',
                            })}
                            onClick={() => setShowReviewModal('approve')}
                            loading={isLoading}
                            disabled={report.status === 'approved' || report.status === 'rejected'}
                        >
                            APPROVE
                        </Button>
                        <Button
                            size="large"
                            type="link"
                            className={classNames({
                                disabled:
                                    report.status === 'approved' || report.status === 'rejected',
                            })}
                            onClick={() => setShowReviewModal('reject')}
                            loading={isLoading}
                            disabled={report.status === 'approved' || report.status === 'rejected'}
                        >
                            REJECT
                        </Button>
                        <Button
                            className="btn-text"
                            size="small"
                            type="link"
                            onClick={() => onClose()}
                            disabled={isLoading}
                        >
                            Go Back
                        </Button>
                    </div>
                )}
            {showReviewModal && (
                <ReviewModal
                    reportId={report.id}
                    show={!!showReviewModal}
                    isApprove={showReviewModal === 'approve'}
                    onClose={() => setShowReviewModal(false)}
                    onSuccess={() => {
                        setShowReviewModal(false);
                        onSuccess();
                    }}
                />
            )}
        </Modal>
    );
};
export default DetailsModal;
