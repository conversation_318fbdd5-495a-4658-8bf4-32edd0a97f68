import { Action, Thunk } from 'easy-peasy';
import ProfileDto from '../dto/ProfileDto';
import { UserUpdateDto } from '../../user/dto/UserDto';

export default interface ProfileModel {
    // state
    data: ProfileDto | null;

    // acitons
    load: Action<ProfileModel, ProfileDto>;
    unload: Action<ProfileModel>;

    // thunks
    get: Thunk<ProfileModel>;
    update: Thunk<ProfileModel, UserUpdateDto>;
    updatePhoto: Thunk<ProfileModel, string | null>;
}
