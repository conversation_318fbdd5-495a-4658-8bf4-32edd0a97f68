import { Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { Resource } from '../../store/app/types';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { ClaimStatus } from '../../store/claim/types/Claim';
import { DATE_ONLY } from '../../utils/dateFormat';
import { capitalizeString } from '../../utils/strings';
import DataGrid from '../app/shared/DataGrid';
import { canAccess } from '../../common/canAccess';
import { UserRole } from '../../store/user/types/User';

interface Props {
    list: Resource<ClaimDto[]>;
}

const ConnectedClaimsList: React.FC<Props> = ({ list }) => {
    const navigate = useNavigate();

    const columns: ColumnsType<ClaimDto> = [
        {
            key: 'id',
            title: 'Policyholder',
            render: ({ policyHolder, id }) => (
                <b
                    className="cursor"
                    onClick={() =>
                        canAccess([UserRole.ADJUSTER])
                            ? navigate(`/claim/${id}/reports`)
                            : navigate(`/claim/${id}`)
                    }
                >
                    {policyHolder.name}
                </b>
            ),
        },
        {
            key: 'claimNumber',
            title: 'Claim Number',
            render: ({ claimNumber }) => <span>{claimNumber}</span>,
        },
        {
            title: 'Claim Date Range',
            className: 'daterange',
            render: ({ closedAt, status, createdAt }: ClaimDto) => (
                <Space>
                    <span>{format(new Date(createdAt), DATE_ONLY)}</span>
                    <span> - </span>
                    <span>
                        {status === ClaimStatus.CLOSED && closedAt
                            ? format(new Date(closedAt), DATE_ONLY)
                            : format(new Date(), DATE_ONLY)}
                    </span>
                </Space>
            ),
        },
        {
            align: 'center',
            key: 'status',
            title: 'Status',
            render: (row) => (
                <div
                    className={classNames(
                        {
                            'pill primary': [
                                ClaimStatus.ACTIVE,
                                ClaimStatus.ON_HOLD,
                                ClaimStatus.INVOICED,
                                ClaimStatus.PAYMENT_PENDING,
                            ].includes(row.status),
                        },
                        {
                            'pill inactive': [
                                ClaimStatus.SIU,
                                ClaimStatus.CLOSED,
                                ClaimStatus.CLOSED_WON,
                                ClaimStatus.CLOSED_LOST,
                            ].includes(row.status),
                        },
                    )}
                >
                    {capitalizeString(row.status)}
                </div>
            ),
        },
    ];

    return <DataGrid<ClaimDto> list={list} columns={columns} />;
};

export default ConnectedClaimsList;
