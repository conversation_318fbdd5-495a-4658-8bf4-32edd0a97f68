import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import { ReactComponent as IconReader } from '../../../../assets/icons/reader-outline.svg';
import { ReportDto } from '../../../../store/report/dto/ReportDto';
import { Resource } from '../../../../types';
import { convertUtcToSpecifiedTimeZone } from '../../../../utils/dateFormat';
import DataGrid from '../../../app/shared/DataGrid';
import Spinner from '../../../app/shared/Spinner';
import { ReportActions } from '../Reports/utils';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { format } from 'date-fns';

interface Props {
    list: Resource<ReportDto[]>;
    onActionClick: (item: MenuItemType | undefined, report: ReportDto) => void;
    actionLoading: boolean | string;
    claim: ClaimDto;
}

export const LatestReportsList: React.FC<Props> = ({
    list,
    onActionClick,
    actionLoading,
    claim,
}) => {
    const columns: ColumnsType<ReportDto> = [
        {
            title: 'Date Range',
            render: (report: ReportDto) => (
                <div
                    className="flex-between cursor"
                    onClick={() => onActionClick({ key: ReportActions.VIEW }, report)}
                >
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.startDate, claim.timezone),
                                'dd MMM',
                            )}
                        </b>
                    </span>
                    <span>
                        <b>-</b>
                    </span>
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.endDate, claim.timezone),
                                'dd MMM',
                            )}
                        </b>
                    </span>
                </div>
            ),
        },

        {
            title: 'Type',
            render: ({ type }: ReportDto) => <span>{type}</span>,
        },

        {
            title: 'Actions',
            width: 60,
            render: (report: ReportDto) =>
                actionLoading === report.id ? (
                    <div className="flex-center">
                        <Spinner />
                    </div>
                ) : (
                    <div className="flex-center">
                        <IconReader
                            className="cursor"
                            onClick={() => {
                                onActionClick(
                                    { label: ReportActions.VIEW, key: ReportActions.VIEW },
                                    report,
                                );
                            }}
                        />
                    </div>
                ),
        },
    ];

    return <DataGrid<ReportDto> list={list} columns={columns} />;
};
export default LatestReportsList;
