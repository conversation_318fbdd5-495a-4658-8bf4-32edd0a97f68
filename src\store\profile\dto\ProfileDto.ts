import {
    IsEmail,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    ValidateIf,
} from 'class-validator';
import { ContactMethod } from '../../../types';
import { UserRole } from '../../user/types/User';

export default class ProfileDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    name!: string;

    @IsNotEmpty()
    @IsEmail()
    email!: string;

    @IsNotEmpty()
    @IsNumber()
    phone!: string;

    @IsNotEmpty()
    @IsString()
    company!: string;

    @IsOptional()
    @IsEnum(ContactMethod)
    contactMethod?: ContactMethod;

    @IsNotEmpty()
    @IsString()
    status!: string;

    @IsNotEmpty()
    @IsString()
    profilePhoto!: string;

    @IsNotEmpty()
    @IsString()
    jobTitle!: string;

    @IsNotEmpty()
    @IsString()
    timezone!: string;

    @IsNotEmpty()
    @IsString()
    role!: UserRole;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;
}

export class ProfileUpdateDto {
    @IsString()
    @IsNotEmpty({ message: 'Required' })
    name?: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    phone?: string;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as ProfileUpdateDto)?.role === UserRole.ADJUSTER)
    company?: string;

    @IsEnum(ContactMethod, { message: 'Please enter a valid preferred method of contact' })
    @IsNotEmpty({ message: 'Required' })
    preferred_contact_method?: ContactMethod;

    @IsString()
    @IsNotEmpty({ message: 'Required' })
    @ValidateIf((object) => (object as ProfileUpdateDto)?.role === UserRole.ADJUSTER)
    job_title?: string;

    @IsString()
    @IsOptional()
    role?: UserRole;

    // @IsString()
    // @IsNotEmpty({ message: 'Required' })
    // createdAt?: string;
}
