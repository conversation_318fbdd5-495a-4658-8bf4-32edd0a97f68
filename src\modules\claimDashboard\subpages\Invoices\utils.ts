import { MenuProps } from 'antd';
import { plainToInstance } from 'class-transformer';
import { InvoiceFilterDto } from '../../../../store/invoice/dto/InvoiceDto';
import {
    RawInvoiceType,
    InvoiceFilterParam,
    InvoiceType,
} from '../../../../store/invoice/type/Invoice';
import { getReadableExpenseCategory } from '../Expenses/utils';
import { ExpenseCategory } from '../../../../types';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';

export enum InvoiceActions {
    DOWNLOAD_PDF = 'Download PDF',
    DOWNLOAD_INVOICE = 'Download Invoice',
    DOWNLOAD_CSV = 'Download CSV',
    UPLOADED_TO_QBS = 'Uploaded to QBs',
    DELETE = 'Delete Invoice',
    EMAIL = 'Email to Adjuster',
    VIEW = 'View',
}

export const adminActionMenuItems: MenuProps['items'] = [
    {
        label: InvoiceActions.VIEW,
        key: InvoiceActions.VIEW,
    },
    {
        label: InvoiceActions.DOWNLOAD_PDF,
        key: InvoiceActions.DOWNLOAD_PDF,
    },
    {
        label: InvoiceActions.DOWNLOAD_CSV,
        key: InvoiceActions.DOWNLOAD_CSV,
    },
    {
        label: InvoiceActions.DELETE,
        key: InvoiceActions.DELETE,
    },
    {
        type: 'divider',
    },
    {
        label: InvoiceActions.EMAIL,
        key: InvoiceActions.EMAIL,
    },
];

export const adminInvoiceActionMenuItems: MenuProps['items'] = [
    {
        label: InvoiceActions.VIEW,
        key: InvoiceActions.VIEW,
    },
    {
        label: InvoiceActions.DOWNLOAD_INVOICE,
        key: InvoiceActions.DOWNLOAD_PDF,
    },
    {
        label: InvoiceActions.UPLOADED_TO_QBS,
        key: InvoiceActions.UPLOADED_TO_QBS,
    },
    {
        label: InvoiceActions.DELETE,
        key: InvoiceActions.DELETE,
    },
    // {
    //     type: 'divider',
    // },
    // {
    //     label: InvoiceActions.EMAIL,
    //     key: InvoiceActions.EMAIL,
    // },
];

export const adjusterActionMenuItems: MenuProps['items'] = [
    {
        label: InvoiceActions.VIEW,
        key: InvoiceActions.VIEW,
    },
    {
        label: InvoiceActions.DOWNLOAD_INVOICE,
        key: InvoiceActions.DOWNLOAD_INVOICE,
    },
    {
        label: InvoiceActions.UPLOADED_TO_QBS,
        key: InvoiceActions.UPLOADED_TO_QBS,
    },
];

export const downloadPDFFile = (url: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'invoice.pdf');
    link.setAttribute('target', '_blank');
    link.click();
    link.remove();
};

export const downloadCSVFile = (csvString: string) => {
    const blob = new Blob([csvString], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'invoice.csv');
    link.click();
    URL.revokeObjectURL(url);
};

export const mapFilterParams = (filterParams: InvoiceFilterParam[]): InvoiceFilterDto => {
    const status = (filterParams.find((param) => param.field === 'status')?.value ||
        []) as string[];

    const type = filterParams.find((param) => param.field === 'type')?.value;
    const dateRange = (
        filterParams.find((param) => param.field === 'created_at')?.value as string[] | undefined
    )?.map((date) => new Date(date));

    return plainToInstance(InvoiceFilterDto, {
        status,
        type,
        dateRange,
    });
};

export const TypeOptions: SelectBoxOption[] = Object.keys(InvoiceType).map((key) => ({
    label: InvoiceType[key],
    value: RawInvoiceType[key],
}));

export const InvoiceStatusOptions: SelectBoxOption[] = [
    {
        label: 'Ready',
        value: 'created',
    },
    {
        label: 'Pending',
        value: 'pending',
    },
    {
        label: 'Approved',
        value: 'approved',
    },
    {
        label: 'Rejected',
        value: 'rejected',
    },
];

export const ExpenseCategoryOptions: SelectBoxOption[] = Object.values(ExpenseCategory).map(
    (category) => ({
        label: getReadableExpenseCategory(category),
        value: category as string,
    }),
);
