import { IsNotEmpty, <PERSON><PERSON>ptional, IsString, ValidateIf, ValidateNested } from 'class-validator';
import { MessageMetadataDto } from './MessageMetadataDto';

export class MessageDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsString()
    text!: string;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;

    @IsOptional()
    @IsString()
    updatedAt?: string;

    @IsOptional()
    @IsString()
    editedAt?: string;

    @IsNotEmpty()
    @ValidateNested()
    meta!: MessageMetadataDto;
}

export class SendMessageDto {
    @IsNotEmpty({ message: 'Please enter some text or attach an image' })
    @IsString()
    @ValidateIf((object) => !(object as SendMessageDto).meta?.attachment?.url)
    text!: string;

    @IsOptional()
    @ValidateNested()
    meta?: MessageMetadataDto;
}
