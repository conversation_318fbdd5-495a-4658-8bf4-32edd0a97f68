import { Col, Row, Space } from 'antd';
import classNames from 'classnames';
import { useEffect, useMemo } from 'react';
import { ReactComponent as CheckmarkIcon } from '../../assets/icons/checkmark-circle.svg';
import { PasswordCondition, getPasswordConditionList } from './utils';

interface Props {
    password?: string;
    repeatPassword?: string;
    onConditionsChange: (boolean) => void;
}

const PasswordConditionList: React.FC<Props> = ({
    password = '',
    repeatPassword = '',
    onConditionsChange,
}) => {
    const { conditionList, hasMetConditions } = useMemo(() => {
        const conditionList = getPasswordConditionList(password, repeatPassword);
        const hasMetConditions = Object.values(conditionList).every((value) => value);

        return {
            conditionList,
            hasMetConditions,
        };
    }, [password, repeatPassword]);

    useEffect(() => {
        onConditionsChange(hasMetConditions);
    }, [hasMetConditions, onConditionsChange]);

    return (
        <Row className="password-conditions" justify="space-between" align="middle">
            <Col sm={24} md={12}>
                <PasswordConditionItem
                    isMet={conditionList[PasswordCondition.LOWER_CASE]}
                    label={PasswordCondition.LOWER_CASE}
                />
                <PasswordConditionItem
                    isMet={conditionList[PasswordCondition.UPPER_CASE]}
                    label={PasswordCondition.UPPER_CASE}
                />
                <PasswordConditionItem
                    isMet={conditionList[PasswordCondition.HAS_NUMBER]}
                    label={PasswordCondition.HAS_NUMBER}
                />
            </Col>
            <Col xs={24} sm={12}>
                <PasswordConditionItem
                    isMet={conditionList[PasswordCondition.HAS_SYMBOL]}
                    label={PasswordCondition.HAS_SYMBOL}
                />
                <PasswordConditionItem
                    isMet={conditionList[PasswordCondition.MIN_LENGTH]}
                    label={PasswordCondition.MIN_LENGTH}
                />
            </Col>
        </Row>
    );
};

interface PasswordConditionProps {
    label: string;
    isMet: boolean;
}
const PasswordConditionItem: React.FC<PasswordConditionProps> = ({ label, isMet }) => {
    return (
        <Space
            className={classNames('password-conditions-item', {
                'is-not-met': !isMet,
            })}
        >
            <CheckmarkIcon /> <span>{label}</span>
        </Space>
    );
};

export default PasswordConditionList;
