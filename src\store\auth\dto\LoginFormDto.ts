import { IsE<PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';

export class LoginDto {
    @IsNotEmpty({
        message: 'Email must not be empty',
    })
    @IsEmail(
        // eslint-disable-next-line object-curly-newline
        {},
        {
            message: 'Incorrect email',
        },
    )
    email!: string;

    @IsNotEmpty({
        message: 'Password must not be empty',
    })
    @IsString()
    password!: string;

    @IsString()
    code!: string;
}
