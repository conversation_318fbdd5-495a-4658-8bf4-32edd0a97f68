import classNames from 'classnames';
import { useEffect, useState } from 'react';

interface Props {
    percent: number;
    error?: boolean;
    className?: string;
    barClassName?: string;
}

const ProgressBar: React.FC<Props> = ({ percent, error, className, barClassName }) => {
    const progressWidth = error ? 0 : `${Math.min(percent, 100)}%`;
    const [opacity, setOpacity] = useState(0);

    useEffect(() => {
        if (percent > 0) {
            setOpacity(1);
        } else {
            setOpacity(0);
        }
    }, [percent]);

    return (
        <div className={classNames('progress-bar', { error }, className)} style={{ opacity }}>
            <div
                className={classNames('progress-bar-progress', barClassName)}
                style={{ width: progressWidth }}
            ></div>
        </div>
    );
};
export default ProgressBar;
