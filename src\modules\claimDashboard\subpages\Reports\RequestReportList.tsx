import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { RequestReportDto } from '../../../../store/report/dto/ReportDto';
import { reportStatusMap } from '../../../../store/report/type/Report';
import { UserRole } from '../../../../store/user/types/User';
import { Resource } from '../../../../types';
import ContextMenu from '../../../app/shared/ContextMenu';
import DataGrid from '../../../app/shared/DataGrid';
import Spinner from '../../../app/shared/Spinner';
import {
    ReportActions,
    adjusterActionMenuItems,
    adminReportRequestActionAnalticsMenuItems,
    adminReportRequestActionMenuItems,
} from './utils';
import { format } from 'date-fns';
import { convertUtcToSpecifiedTimeZone } from '../../../../utils/dateFormat';

interface Props {
    list: Resource<RequestReportDto[]>;
    onActionClick: (item: MenuItemType | undefined, report: RequestReportDto) => void;
    actionLoading: boolean | string;
    claimArchived: boolean;
    claim: ClaimDto;
    adminRequestReportManualList: Resource<RequestReportDto[]>;
}

export const RequestReportList: React.FC<Props> = ({
    onActionClick,
    actionLoading: actionsLoading,
    claimArchived,
    claim,
    adminRequestReportManualList,
}) => {
    const columns: ColumnsType<RequestReportDto> = [
        {
            title: 'Report #',
            render: ({ report_name }: RequestReportDto) => <b>{report_name}</b>,
        },
        {
            title: 'Receipt Date Range',
            render: (report: RequestReportDto) => (
                <div
                    className="flex-between cursor"
                    onClick={() => {
                        onActionClick(
                            { label: ReportActions.VIEW, key: ReportActions.VIEW },
                            report,
                        );
                    }}
                >
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.startDate, claim.timezone),
                                'MM/dd/yy',
                            )}
                        </b>
                    </span>
                    <span>
                        <b>-</b>
                    </span>
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(report.endDate, claim.timezone),
                                'MM/dd/yy',
                            )}
                        </b>
                    </span>
                </div>
            ),
        },
        {
            title: 'Approved Date Range',
            render: (report: RequestReportDto) => (
                <div
                    className="flex-between cursor"
                    onClick={() => {
                        onActionClick(
                            { label: ReportActions.VIEW, key: ReportActions.VIEW },
                            report,
                        );
                    }}
                >
                    <span>
                        <b>{report.approved_start_date_dateonly}</b>
                    </span>
                    <span>
                        <b>-</b>
                    </span>
                    <span>
                        <b>
                            <b>{report.approved_end_date_dateonly}</b>
                        </b>
                    </span>
                </div>
            ),
        },
        {
            title: '# of Receipts',
            render: ({ expensesCount }: RequestReportDto) => <b>{expensesCount}</b>,
        },
        {
            title: 'Created Date',
            render: (report: RequestReportDto) => (
                <b>
                    {' '}
                    {format(
                        convertUtcToSpecifiedTimeZone(report.ready_for_download_at, claim.timezone),
                        'MM/dd/yy hh:mm a',
                    )}
                </b>
            ),
        },
        {
            title: 'Analytics Sent to Salesforce',
            render: (report: RequestReportDto) => (
                <b>
                    {' '}
                    <div className={classNames('pill', { primary: true })}>
                        {report.send_analytics_to_salesforce &&
                            format(
                                convertUtcToSpecifiedTimeZone(
                                    report.send_analytics_to_salesforce,
                                    claim.timezone,
                                ),
                                'MM/dd/yy hh:mm a',
                            )}
                    </div>
                </b>
            ),
        },
        {
            title: 'Type',
            render: ({ type }: RequestReportDto) => <span>{type || 'Manual'}</span>,
        },
        {
            title: 'Status',
            align: 'center',
            width: 120,
            render: ({ status }: RequestReportDto) => {
                return (
                    <div
                        className={classNames(
                            'pill',
                            { primary: status === 'approved' },
                            { info: status === 'created' },
                            { info: status === 'Referenced' },
                            { warning: status === 'pending' },
                            { warning: status === 'preparing' },
                            { animated: status === 'preparing' },
                            { error: status === 'rejected' },
                        )}
                    >
                        {reportStatusMap[status]}
                    </div>
                );
            },
        },
        {
            title: 'Actions',
            width: 90,
            render: (report: RequestReportDto) =>
                actionsLoading === report.id ? (
                    <div className="flex-center">
                        <Spinner />
                    </div>
                ) : (
                    <div className="flex-center">
                        {!claimArchived && (
                            <ContextMenu
                                className={classNames({ disabled: report.status === 'preparing' })}
                                items={
                                    canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])
                                        ? report.send_analytics_to_salesforce === null
                                            ? adminReportRequestActionAnalticsMenuItems
                                            : adminReportRequestActionMenuItems
                                        : adjusterActionMenuItems
                                }
                                onItemClick={(item) => onActionClick(item, report)}
                            />
                        )}
                    </div>
                ),
        },
    ];
    return <DataGrid<RequestReportDto> list={adminRequestReportManualList} columns={columns} />;
};
export default RequestReportList;
