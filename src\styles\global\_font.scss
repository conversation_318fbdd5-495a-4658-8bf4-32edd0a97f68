// Define the font faces

// League Spartan Font Faces
@font-face {
    font-family: 'League Spartan';
    font-weight: bold;
    src: url('../../assets/fonts/LeagueSpartan-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'League Spartan';
    font-weight: 300; // Light
    src: url('../../assets/fonts/LeagueSpartan-Light.ttf') format('truetype');
}

@font-face {
    font-family: 'League Spartan';
    font-weight: 500; // Medium
    src: url('../../assets/fonts/LeagueSpartan-Medium.ttf') format('truetype');
}

@font-face {
    font-family: 'League Spartan';
    font-weight: normal; // Regular
    src: url('../../assets/fonts/LeagueSpartan-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'League Spartan';
    font-weight: 600; // SemiBold
    src: url('../../assets/fonts/LeagueSpartan-SemiBold.ttf') format('truetype');
}

// Mulish Font Faces
@font-face {
    font-family: 'Mulish';
    font-weight: bold;
    src: url('../../assets/fonts/Mulish-Bold.ttf') format('truetype');
}

@font-face {
    font-family: 'Mulish';
    font-weight: 300; // Light
    src: url('../../assets/fonts/Mulish-Light.ttf') format('truetype');
}

@font-face {
    font-family: 'Mulish';
    font-weight: 500; // Medium
    src: url('../../assets/fonts/Mulish-Medium.ttf') format('truetype');
}

@font-face {
    font-family: 'Mulish';
    font-weight: normal; // Regular
    src: url('../../assets/fonts/Mulish-Regular.ttf') format('truetype');
}

@font-face {
    font-family: 'Mulish';
    font-weight: 600; // SemiBoldItalic
    src: url('../../assets/fonts/Mulish-SemiBold.ttf') format('truetype');
}
