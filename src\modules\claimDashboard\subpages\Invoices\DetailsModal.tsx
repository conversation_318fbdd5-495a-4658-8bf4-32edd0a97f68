import { Button } from 'antd';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { useEffect, useState } from 'react';
import { canAccess } from '../../../../common/canAccess';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { InvoiceDto } from '../../../../store/invoice/dto/InvoiceDto';
import { UserRole } from '../../../../store/user/types/User';
import FlashMessages from '../../../app/FlashMessages';
import ContextMenu from '../../../app/shared/ContextMenu';
import Modal from '../../../app/shared/Modal';
import Spinner from '../../../app/shared/Spinner';
import TransformContainer from '../../../app/shared/TransformContainer';
import InvoiceDetails from './Details';
import ReviewModal from './ReviewModal';
import { adjusterActionMenuItems } from './utils';
import classNames from 'classnames';

interface Props {
    invoice: InvoiceDto;
    claim: ClaimDto;
    show: boolean;
    onClose: VoidFunction;
    onActionClick: (item: MenuItemType | undefined, invoice: InvoiceDto) => void;
    onSuccess: VoidFunction;
}

const DetailsModal: React.FC<Props> = ({
    invoice,
    claim,
    show,
    onClose,
    onActionClick,
    onSuccess,
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [invoiceHtml, setInvoiceHtml] = useState<string>();
    const [showReviewModal, setShowReviewModal] = useState<'approve' | 'reject' | false>(false);
    const [shouldSend, setShouldSend] = useState(false);

    const { sendToAdjuster } = useStoreActions((actions) => actions.invoice);
    const { getInvoiceHtml } = useStoreActions((actions) => actions.claim);

    useEffect(() => {
        getInvoiceHtml(invoice.id).then(setInvoiceHtml);
    }, [getInvoiceHtml, invoice.id]);

    const handleSendToAdjuster = async () => {
        if (!shouldSend) {
            return;
        }

        setIsLoading(true);
        try {
            await sendToAdjuster(invoice.id);
            FlashMessages.success('Invoice Sent');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Send');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Modal
            className="claim-reports-details"
            show={show}
            onClose={onClose}
            title="Invoice Preview"
            subtitle="Please review the details of the invoice before you send it to adjuster for approval using the buttons below."
            sideContent={
                <TransformContainer
                    additionalActions={
                        <ContextMenu
                            toolTipContent="Download"
                            items={adjusterActionMenuItems}
                            onItemClick={(item) => onActionClick(item, invoice)}
                            hasTooltip={false}
                        />
                    }
                >
                    {!invoiceHtml && <Spinner type="skeleton" transparent />}
                    {invoiceHtml && (
                        <div
                            className="claim-reports-preview"
                            dangerouslySetInnerHTML={{ __html: invoiceHtml }}
                        />
                    )}
                </TransformContainer>
            }
            sideContentProps={{ size: 'large' }}
        >
            <InvoiceDetails
                invoice={invoice}
                claim={claim}
                canSend
                onToggleSend={() => setShouldSend(!shouldSend)}
            />

            {canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) && (
                <div className="actions">
                    {!claim.archivedAt && (
                        <Button
                            size="large"
                            className={classNames('btn btn-primary', { disabled: !shouldSend })}
                            onClick={() => handleSendToAdjuster()}
                            loading={isLoading}
                            disabled={!shouldSend}
                        >
                            Send
                        </Button>
                    )}
                    <Button type="link" size="large" onClick={() => onClose()} disabled={isLoading}>
                        Go Back
                    </Button>
                </div>
            )}

            {canAccess([UserRole.ADJUSTER]) &&
                !['rejected', 'approved'].includes(invoice.status) && (
                    <div className="actions">
                        <Button
                            size="large"
                            className={classNames('btn-primary', {
                                disabled: invoice.status === 'Open' || invoice.status === 'Held',
                            })}
                            onClick={() => setShowReviewModal('approve')}
                            loading={isLoading}
                            disabled={invoice.status === 'Open' || invoice.status === 'Held'}
                        >
                            APPROVE
                        </Button>
                        <Button
                            size="large"
                            type="link"
                            className={classNames({
                                disabled: invoice.status === 'Open' || invoice.status === 'Held',
                            })}
                            onClick={() => setShowReviewModal('reject')}
                            loading={isLoading}
                            disabled={invoice.status === 'Open' || invoice.status === 'Held'}
                        >
                            REJECT
                        </Button>
                        <Button
                            className="btn-text"
                            size="small"
                            type="link"
                            onClick={() => onClose()}
                            disabled={isLoading}
                        >
                            Go Back
                        </Button>
                    </div>
                )}
            {showReviewModal && (
                <ReviewModal
                    invoiceId={invoice.id}
                    show={!!showReviewModal}
                    isApprove={showReviewModal === 'approve'}
                    onClose={() => setShowReviewModal(false)}
                    onSuccess={() => {
                        setShowReviewModal(false);
                        onSuccess();
                    }}
                />
            )}
        </Modal>
    );
};
export default DetailsModal;
