import { Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { ClaimDto } from '../../store/claim/dto/ClaimDto';
import { Resource } from '../../types';
import { formatCurrencyString } from '../../utils/currencyFormat';
import { DATE_TIME, formatDateUTCString } from '../../utils/dateFormat';
import Avatar from '../app/shared/Avatar';
import DataGrid from '../app/shared/DataGrid';
import { PaginationDirection } from '../../utils/paginate';
import { getSortableColumn } from '../../common/table';

interface Props {
    list: Resource<ClaimDto[]>;
    loading?: boolean;
    onToggleSortDirection: VoidFunction;
    sortDirection: PaginationDirection;
}

const ArchivedClaimList: React.FC<Props> = ({
    list,
    loading = false,
    onToggleSortDirection,
    sortDirection,
}) => {
    const navigate = useNavigate();

    const columns: ColumnsType<ClaimDto> = useMemo(
        () => [
            {
                key: 'id',
                title: 'Policyholder',
                className: 'user',
                render: ({ policyHolder, id }) => (
                    <Space className="cursor" onClick={() => navigate(`/claim/${id}`)}>
                        <Avatar
                            photoUrl={policyHolder.profilePhoto}
                            name={policyHolder.name}
                            size="table"
                        />
                        <b>{policyHolder.name}</b>
                    </Space>
                ),
            },
            {
                key: 'claimNumber',
                title: 'Claim Number',
                render: ({ claimNumber }: ClaimDto) => <span>{claimNumber}</span>,
            },
            {
                key: 'ALE Limits',
                title: 'ALE Limits',
                render: ({ aleLimit }: ClaimDto) => <span>{formatCurrencyString(aleLimit)}</span>,
            },
            {
                key: 'Normal Daily Expense',
                title: 'Normal Daily Expense',
                render: ({ normalDailyExpense }: ClaimDto) => (
                    <span>{formatCurrencyString(normalDailyExpense)}</span>
                ),
            },

            {
                ...getSortableColumn(
                    {
                        key: 'archivedAt',
                        title: 'Date Archived',
                        width: 200,
                        render: ({ archivedAt }: ClaimDto) => (
                            <span className="font-gray">
                                {!!archivedAt && formatDateUTCString(archivedAt, DATE_TIME)}
                            </span>
                        ),
                    },
                    sortDirection,
                ),
            },
        ],
        [navigate, sortDirection],
    );

    return (
        <DataGrid<ClaimDto>
            loading={loading}
            list={list}
            columns={columns}
            onToggleSort={onToggleSortDirection}
        />
    );
};
export default ArchivedClaimList;
