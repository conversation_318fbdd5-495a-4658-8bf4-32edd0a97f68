import { Button, Dropdown, MenuProps } from 'antd';
import classNames from 'classnames';
import { ReactNode, useCallback, useState } from 'react';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';
import { ReactComponent as IconChevron } from '../../../assets/icons/chevron-down.svg';

interface Props {
    children: ReactNode;
    additionalActions?: ReactNode;
    initialScale?: number;
}

const items: MenuProps['items'] = [
    { label: '100%', key: '100%' },
    { label: '150%', key: '150%' },
    { label: '200%', key: '200%' },
];

const zoomLabelMap = {
    '100%': 1,
    '150%': 1.5,
    '200%': 2,
};

const TransformContainer: React.FC<Props> = ({
    initialScale = 1,
    children,
    additionalActions = null,
}) => {
    const [zoomLabel, setZoomLabel] = useState('100%');
    const [zoom, setZoom] = useState(initialScale);
    const [isPanning, setIsPanning] = useState(false);

    const handleZoomClick = useCallback(
        (item, zoomIn, zoomOut) => {
            const newZoom = zoomLabelMap[item.key];
            const zoomDif = newZoom - zoom;

            if (zoomDif < 0) {
                zoomOut(Math.abs(zoomDif));
            } else {
                zoomIn(zoomDif);
            }

            setZoomLabel(item.key);
            setZoom(newZoom);
        },
        [zoom],
    );

    return (
        <TransformWrapper
            initialScale={initialScale}
            wheel={{ disabled: true }}
            maxScale={2}
            onZoomStop={(e) => setZoom(e.state.scale)}
            doubleClick={{ step: 0.5 }}
            onPanningStart={() => setIsPanning(true)}
            onPanningStop={() => setIsPanning(false)}
        >
            {({ zoomIn, zoomOut }) => (
                <>
                    <div className="transform-tools">
                        <Dropdown
                            className="transform-tools-zoom"
                            overlayClassName="transform-tools-zoom-overlay"
                            menu={{
                                items,
                                onClick: (e) => handleZoomClick(e, zoomIn, zoomOut),
                            }}
                        >
                            <Button className="btn-ghost btn-tab btn-ghost-bg">
                                <b className="margin-right-4">{zoomLabel}</b> <IconChevron />
                            </Button>
                        </Dropdown>
                        {additionalActions}
                    </div>
                    <TransformComponent wrapperClass="transform-wrapper">
                        <div className={classNames('transform-content', { active: isPanning })}>
                            {children}
                        </div>
                    </TransformComponent>
                </>
            )}
        </TransformWrapper>
    );
};
export default TransformContainer;
