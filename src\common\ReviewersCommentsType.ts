export enum ReviewersCommentsType {
    APPROVED_BREAKFAST = 'APPROVED_BREAKFAST',
    APPROVED_LUNCH = 'APPROVED_LUNCH',
    APPROVED_DINNER = 'APPROVED_DINNER',
    APPROVED_SNACK = 'APPROVED_SNACK',
    APPROVED_GROCERIES = 'APPROVED_GROCERIES',
    APPROVED_COFFEE = 'APPROVED_COFFEE',
    APPROVED_DRINK = 'APPROVED_DRINK',
    APPROVED_PERSONAL_ITEMS = 'APPROVED_PERSONAL_ITEMS',
    APPROVED_MEAL = 'APPROVED_MEAL',
    APPROVED_GAS = 'APPROVED_GAS',
    APPROVED_DOCUMENTATION = 'APPROVED_DOCUMENTATION',
    APPROVED_OTHER = 'APPROVED_OTHER',
    APPROVED_PARTIAL = 'APPROVED_PARTIAL',
    APPROVED_PARTIAL_PERSONAL_ITEMS = 'APPROVED_PARTIAL_PERSONAL_ITEMS',
    APPROVED_PARTIAL_EXCESSIVE_TIP = 'APPROVED_PARTIAL_EXCESSIVE_TIP',
    APPROVED_PARTIAL_DELIVERY_FEE = 'APPROVED_PARTIAL_DELIVERY_FEE',
    APPROVED_PARTIAL_DEDUCTED_DONATIONS = 'APPROVED_PARTIAL_DEDUCTED_DONATIONS',
    APPROVED_PARTIAL_OTHER = 'APPROVED_PARTIAL_OTHER',
    IN_REVIEW_DUPLICATE = 'IN_REVIEW_DUPLICATE',
    IN_REVIEW_EXCESSIVE_SPENDING = 'IN_REVIEW_EXCESSIVE_SPENDING',
    IN_REVIEW_LOCATION = 'IN_REVIEW_LOCATION',
    IN_REVIEW_WAITING = 'IN_REVIEW_WAITING',
    IN_REVIEW_OTHER = 'IN_REVIEW_OTHER',
    NOT_APPROVED_ILLEGIBLE = 'NOT_APPROVED_ILLEGIBLE',
    NOT_APPROVED_DATETIME = 'NOT_APPROVED_DATETIME',
    NOT_APPROVED_NOT_ITEMIZED = 'NOT_APPROVED_NOT_ITEMIZED',
    NOT_APPROVED_NOT_APPROVED_RADIUS = 'NOT_APPROVED_NOT_APPROVED_RADIUS',
    NOT_APPROVED_NOT_APPROVED_TIME_PERIOD = 'NOT_APPROVED_NOT_APPROVED_TIME_PERIOD',
    NOT_APPROVED_PERSONAL_ITEMS = 'NOT_APPROVED_PERSONAL_ITEMS',
    NOT_APPROVED_VENDOR_NAME = 'NOT_APPROVED_VENDOR_NAME',
    NOT_APPROVED_GAS = 'NOT_APPROVED_GAS',
    NOT_APPROVED_DUPLICATE = 'NOT_APPROVED_DUPLICATE',
    NOT_APPROVED_OTHER = 'NOT_APPROVED_OTHER',
}
export enum ReviewersCommentsTypePartialApproved {
    APPROVED_PARTIAL = 'Partial Approval - Deducted Alcohol',
    APPROVED_PARTIAL_FOOD = 'Partial Approval - Deducted Non-Food Items',
    APPROVED_PARTIAL_PERSONAL_ITEMS = 'Partial Approval - Deducted Non-Essential or Personal Items',
    APPROVED_PARTIAL_EXCESSIVE_TIP = 'Partial Approval - Excessive Tip',
    APPROVED_PARTIAL_DELIVERY_FEE = 'Partial Approval - Delivery Fee',
    APPROVED_PARTIAL_DEDUCTED_DONATIONS = 'Partial Approval - Deducted Donations',
    APPROVED_PARTIAL_OTHER = 'Partial Approval - Other',
}
export enum ReviewersCommentsTypeApproved {
    APPROVED_BREAKFAST = 'Approved - Breakfast',
    APPROVED_LUNCH = 'Approved - Lunch',
    APPROVED_DINNER = 'Approved - Dinner',
    APPROVED_SNACK = 'Approved - Snack',
    APPROVED_GROCERIES = 'Approved - Groceries',
    APPROVED_COFFEE = 'Approved - Coffee',
    APPROVED_DRINK = 'Approved - Drink',
    APPROVED_PERSONAL_ITEMS = 'Approved - Personal Items',
    APPROVED_MEAL = 'Approved - Meal',
    APPROVED_GAS = 'Approved - Gas',
    APPROVED_DOCUMENTATION = 'Approved - Documentation/Duplicate/Zero Amount',
    APPROVED_OTHER = 'Approved - Other',
}

export enum ReviewersCommentsTypeInReview {
    IN_REVIEW_DUPLICATE = 'In Review - Duplicate',
    IN_REVIEW_EXCESSIVE_SPENDING = 'In Review - Excessive Spending',
    IN_REVIEW_LOCATION = 'In Review - Location',
    IN_REVIEW_WAITING = 'In Review - Waiting on Adjuster',
    IN_REVIEW_OTHER = 'In Review - Other',
}

export enum ReviewersCommentsTypeDeclined {
    NOT_APPROVED_ILLEGIBLE = 'Not Approved - Illegible',
    NOT_APPROVED_DATETIME = 'Not Approved - Date/Time Issue',
    NOT_APPROVED_NOT_ITEMIZED = 'Not Approved - Not Itemized',
    NOT_APPROVED_NOT_APPROVED_RADIUS = 'Not Approved - Not within approved radius',
    NOT_APPROVED_NOT_APPROVED_TIME_PERIOD = 'Not Approved - Not within approved time period',
    NOT_APPROVED_PERSONAL_ITEMS = 'Not Approved - Non-Essential or Personal Items',
    NOT_APPROVED_VENDOR_NAME = 'Not Approved - Vendor Name or Location Issue',
    NOT_APPROVED_GAS = 'Not Approved - Gas',
    NOT_APPROVED_DUPLICATE = 'Not Approved - Duplicate',
    NOT_APPROVED_OTHER = 'Not Approved - Other',
}
