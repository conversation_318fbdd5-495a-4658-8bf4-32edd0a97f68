.claim-reports {
    &-add,
    &-details {
        .actions {
            @include flex(flex, column, nowrap, flex-start, stretch);
            gap: 8px;
        }

        .info {
            &-label {
                color: var(--gray-3, #828282);
                font-size: 14px;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
                margin-bottom: 4px;
            }

            &-value {
                font-size: 16px;
                font-weight: $font-weight-semi-bold;
                line-height: 24px;
                margin-bottom: 4px;
            }

            &-subheading {
                font-size: 16px;
                font-style: normal;
                font-weight: $font-weight-semi-bold;
                line-height: 20px;
            }
        }

        // Add consistent font styling for both Invoices and Reports
        font-family: $font-family-primary, sans-serif;

        .modal-header {
            &-title {
                font-size: 24px;
                font-weight: $font-weight-bold;
                line-height: 28px;
            }

            &-subtitle {
                color: $color-gray-2;
                font-size: 14px;
                line-height: 18px;
                font-weight: $font-weight-light;
            }
        }

        // Ensure buttons have consistent styling
        button {
            font-family: $font-family-primary, sans-serif;
            font-weight: $font-weight-semi-bold !important;
        }
    }

    &-filter-btn {
        height: 48px !important;
    }

    &-header {
        @include flex(flex, row, nowrap, space-between, flex-start);
        .title {
            font-size: 21px;
            font-weight: $font-weight-semi-bold;
            line-height: 24px;
            margin-bottom: 8px;
        }

        .info {
            color: $color-gray-3;
            font-weight: $font-weight-light;
            line-height: 20px;
        }
    }

    &-preview {
        background-color: $color-background;

        .page {
            width: 100% !important;
            padding-bottom: 20px;

            background-color: white !important;
            margin-bottom: 40px;

            @include breakpoint(xl) {
                max-width: 842px;
                margin: 0 auto 40px auto;
            }

            .aggregate-data.row {
                margin-top: 24px;
            }

            .content.vertical {
                flex-wrap: nowrap !important    ;
            }

            &.cover {
                .content {
                    &-col {
                        &.left {
                            .wrapper {
                                @include flex(flex, column, nowrap, space-between, flex-start);
                            }
                        }
                    }
                }

                p {
                    margin: 0 !important;

                    &.big-margin {
                        margin-bottom: 76px !important;
                    }

                    &.small-margin {
                        margin-bottom: 40px !important;
                    }
                }
            }

            &.receipt-cover {
                .content.vertical {
                    flex-direction: column !important;

                    h1 {
                        margin-bottom: 0 !important;
                    }

                    p.count {
                        margin-top: 200px;
                    }
                }
            }
        }
    }
}
.hrStyles {
    background-color: $color-gray-5;
    border: 0 !important;
    display: block;
    height: 1px;
    margin: 24px auto;
    padding: 0;
    width: 100%;
}
