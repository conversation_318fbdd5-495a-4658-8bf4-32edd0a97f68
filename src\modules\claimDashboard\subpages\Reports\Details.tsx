import { Col, Row } from 'antd';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ReportDto } from '../../../../store/report/dto/ReportDto';
import { UserRole } from '../../../../store/user/types/User';
import { convertUtcToSpecifiedTimeZone, DATE_ONLY } from '../../../../utils/dateFormat';
import { ExpenseStatus, getReadableExpenseCategory } from '../Expenses/utils';
import AdjusterInfo from './AdjusterInfo';
import React, { useMemo } from 'react';
import { ExpenseCategory } from '../../../../types';
import { format } from 'date-fns';
import { formatCurrency } from '../../../../utils/currencyFormat';

interface Props {
    report: ReportDto;
    claim: ClaimDto;
    canSend?: boolean;
    onToggleSend?: VoidFunction;
}

const ReportDetails: React.FC<Props> = ({ report, claim, onToggleSend, canSend }) => {
    const isAllCategories = useMemo(
        () => report?.categories?.length === Object.keys(ExpenseCategory).length,
        [report?.categories?.length],
    );

    return (
        <>
            <Row gutter={[0, 24]} className="margin-bottom-24">
                <Col span={24} className="info">
                    <div className="info-label">Receipt Date Range</div>
                    <div className="info-value">
                        {`${format(
                            convertUtcToSpecifiedTimeZone(report.startDate, claim.timezone),
                            DATE_ONLY,
                        )} - ${format(
                            convertUtcToSpecifiedTimeZone(report.endDate, claim.timezone),
                            DATE_ONLY,
                        )}`}
                    </div>
                </Col>
                {isAllCategories && (
                    <Col span={24} className="info">
                        <div className="info-label">Category</div>
                        <div className="info-value">All</div>
                    </Col>
                )}
            </Row>
            {!isAllCategories && (
                <>
                    <div className="info-label">Category</div>
                    <Row gutter={[0, 25]} className="margin-bottom-24">
                        {report?.categories?.map((category) => {
                            return (
                                <Col key={category} span={12} className="info">
                                    <div className="info-value">
                                        {getReadableExpenseCategory(category)}
                                    </div>
                                </Col>
                            );
                        })}
                    </Row>
                </>
            )}
            <Row gutter={[16, 24]} className="margin-bottom-24">
                <Col span={12} className="info">
                    <div className="info-label">Recommended Reimbursement</div>
                    <div className="info-value">
                        {formatCurrency(report?.metadata?.recommended_reimbursement || 0)}
                    </div>
                </Col>
                <Col span={12} className="info">
                    <div className="info-label">Claim Cost Accuracy</div>
                    <div className="info-value">
                        {formatCurrency(report?.metadata?.cost_savings || 0)}
                    </div>
                </Col>
            </Row>
            <hr />
            <Row gutter={[24, 16]}>
                <Col span={24} className="info-subheading">
                    Expenses
                </Col>
                {!!report?.submitted && (
                    <Col lg={8} xl={8} md={8} sm={8} className="info">
                        <div className="info-label">{ExpenseStatus.SUBMITTED}</div>
                        <div className="info-value">
                            {report?.metadata?.expenses_by_status_submitted_count}
                        </div>
                        <div className="info-value">
                            {formatCurrency(
                                report?.metadata?.expenses_by_status_submitted_amount || 0,
                            )}
                        </div>
                    </Col>
                )}
                {!!report?.inReview && (
                    <Col lg={8} xl={8} md={8} sm={8} className="info">
                        <div className="info-label">{ExpenseStatus.IN_REVIEW}</div>
                        <div className="info-value">
                            {report?.metadata?.expenses_by_status_in_review_count}
                        </div>
                        <div className="info-value">
                            {formatCurrency(
                                report?.metadata?.expenses_by_status_in_review_amount || 0,
                            )}
                        </div>
                    </Col>
                )}
                {!!report?.audited && (
                    <Col lg={8} xl={8} md={8} sm={8} className="info">
                        <div className="info-label">{ExpenseStatus.AUDITED}</div>
                        <div className="info-value">
                            {report?.metadata?.expenses_by_status_audited_count}
                        </div>
                        <div className="info-value">
                            {formatCurrency(
                                report?.metadata?.expenses_by_status_audited_amount || 0,
                            )}
                        </div>
                    </Col>
                )}
            </Row>
            <hr />
            {canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN]) && claim.adjuster && (
                <AdjusterInfo
                    adjuster={claim.adjuster}
                    onToggleSend={() => onToggleSend && onToggleSend()}
                    canSend={canSend}
                />
            )}
        </>
    );
};
export default ReportDetails;
