import { Spin } from 'antd';
import { ReactComponent as IconSpinner } from '../../../assets/icons/spinner.svg';
import classNames from 'classnames';
import { ReactNode } from 'react';

type SpinnerType = 'default' | 'overlay' | 'skeleton';

interface Props {
    type?: SpinnerType;
    className?: string;
    message?: ReactNode;
    transparent?: boolean;
}

const Spinner: React.FC<Props> = ({ message, type = 'default', transparent, className }) => {
    return (
        <div className={classNames('spinner', { transparent: transparent }, type, className)}>
            <Spin className="margin-bottom-8" indicator={<IconSpinner />}></Spin>
            {!!message && <div className="message">{message}</div>}
        </div>
    );
};
export default Spinner;
