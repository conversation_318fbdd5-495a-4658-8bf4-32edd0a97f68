import {
    ChannelMessage,
    ChimeSDKMessagingClient,
    ListChannelMessagesCommandOutput,
} from '@aws-sdk/client-chime-sdk-messaging';
import { DefaultMessagingSession, MessagingSessionObserver } from 'amazon-chime-sdk-js';
import { Action, Computed, Thunk } from 'easy-peasy';
import { Resource } from '../../app/types';
import ChatCredentialsDto from '../dto/ChatCredentialsDto';
import { MessageDto, SendMessageDto } from '../dto/MessageDto';
import { SenderMetadataDto } from '../dto/MessageMetadataDto';
import { ChatMessage, ChatStatus, InitStatus } from './Chat';

export default interface ChatModel {
    // state
    isInitialized: InitStatus;
    messages: Resource<MessageDto[]>;
    chatStatus: ChatStatus;
    nextMessagesToken: string | undefined;
    credentials: ChatCredentialsDto | null;
    endpointUrl: string | null;
    chimeClient: ChimeSDKMessagingClient | null;
    messagingSession: DefaultMessagingSession | null;
    channelArn: string | null;
    userArn: string | null;
    profilePhotoMap: { [userId: string]: string | null };
    unreadCount: number;

    // computed
    hasMoreMessages: Computed<ChatModel, boolean>;
    groupedMessages: Computed<ChatModel, ChatMessage[]>;
    hasUnread: Computed<ChatModel, boolean>;

    // actions
    load: Action<
        ChatModel,
        {
            channelArn: string;
            userArn: string;
            messagingSession: DefaultMessagingSession;
            credentials: ChatCredentialsDto;
        }
    >;
    unload: Action<ChatModel>;
    setEndpointUrl: Action<ChatModel, string>;
    setCredentials: Action<ChatModel, ChatCredentialsDto>;
    setChatStatus: Action<ChatModel, ChatStatus>;
    setChimeClient: Action<ChatModel, ChimeSDKMessagingClient>;
    setMessagingSession: Action<ChatModel, DefaultMessagingSession>;
    setIsInitialized: Action<ChatModel, InitStatus>;
    setUserArn: Action<ChatModel, string>;
    setChannelArn: Action<ChatModel, string>;
    setProfilePhoto: Action<ChatModel, { userId: string; photoUrl: string }>;
    setUnreadCount: Action<ChatModel, number>;

    loadingMessages: Action<ChatModel>;
    loadMessages: Action<ChatModel, ListChannelMessagesCommandOutput>;
    loadSentMessage: Action<ChatModel, ChannelMessage>;

    // thunks
    init: Thunk<
        ChatModel,
        { claimChannelArn: string; claimId: string; observer: MessagingSessionObserver }
    >;
    getConfig: Thunk<ChatModel, string>;
    getCredentials: Thunk<ChatModel>;
    refreshCredentials: Thunk<ChatModel>;
    start: Thunk<ChatModel>;
    end: Thunk<ChatModel>;
    _getMessages: Thunk<ChatModel>;
    _sendMessage: Thunk<ChatModel, SendMessageDto | undefined>;
    getProfilePhoto: Thunk<ChatModel, SenderMetadataDto>;
    _getProfilePhoto: Thunk<ChatModel, SenderMetadataDto>;
    pingWritten: Thunk<ChatModel, string>;
    pingSeen: Thunk<ChatModel, string>;
    checkHasUnread: Thunk<ChatModel, string>;
    needsCredentialRefresh: Thunk<ChatModel>;
}
