import React from 'react';
import PinField from 'react-pin-field';
import { useFormContext } from 'react-hook-form';
import classNames from 'classnames';

interface Props {
    disabled?: boolean;
    error?: boolean;
    inputMode?: 'numeric' | 'search' | 'text' | 'none' | 'tel' | 'url' | 'email' | 'decimal';
    label?: string;
    length?: number;
    name: string;
    onFinish: (value: string) => void;
    validate?: string;
}

const CodeVerificationField: React.FC<Props> = ({
    disabled,
    error,
    inputMode = 'numeric',
    label,
    length = 6,
    name,
    onFinish,
    validate = '0123456789',
}) => {
    const { setValue } = useFormContext();

    return (
        <div className="code-verification">
            {label && <label className="code-verification-label">{label}</label>}

            <div className="code-verification-fields">
                <PinField
                    autoFocus={true}
                    disabled={disabled}
                    length={length}
                    validate={validate}
                    className={classNames({
                        input: true,
                        error,
                    })}
                    onComplete={onFinish}
                    onChange={(value) => {
                        setValue(name, value);
                    }}
                    inputMode={inputMode}
                />
            </div>
        </div>
    );
};

export default CodeVerificationField;
