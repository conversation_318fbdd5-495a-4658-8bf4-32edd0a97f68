import {
    Is<PERSON>rray,
    IsEnum,
    IsNotEmpty,
    IsNumber,
    IsObject,
    IsOptional,
    IsString,
} from 'class-validator';
import PaginationDto from '../../../common/PaginationDto';
import PolicyHolderDto from '../../policyHolder/dto/PolicyHolderDto';
import UserDto from '../../user/dto/UserDto';
import { type ClaimLossAddress, ClaimStatus, ClaimFilterParam } from '../types/Claim';

export class ClaimPaginateDto extends PaginationDto {
    @IsOptional()
    @IsString()
    search_term?: string;

    @IsOptional()
    my_claims?: boolean;

    @IsOptional()
    type?: string;

    @IsOptional()
    @IsString()
    admin_id?: string;

    @IsOptional()
    @IsString()
    user_id?: string;

    @IsOptional()
    @IsString()
    policy_holder_id?: string;

    @IsOptional()
    @IsArray()
    params?: ClaimFilterParam[];
}

export class ClaimDto {
    @IsNotEmpty()
    @IsString()
    id!: string;

    @IsNotEmpty()
    @IsEnum(ClaimStatus)
    status!: ClaimStatus;

    @IsOptional()
    @IsString()
    startedAt?: string;

    @IsOptional()
    @IsString()
    closedAt?: string;

    @IsOptional()
    @IsString()
    archivedAt?: string;

    @IsNotEmpty()
    @IsNumber()
    reportDays!: number;

    @IsNotEmpty()
    @IsObject()
    policyHolder!: PolicyHolderDto;

    @IsOptional()
    @IsObject()
    adjuster?: UserDto;

    @IsOptional()
    @IsString()
    chatChannelArn?: string;

    @IsOptional()
    @IsObject()
    admin?: UserDto;

    @IsNotEmpty()
    @IsObject()
    lossAddress!: ClaimLossAddress;

    @IsNotEmpty()
    @IsOptional()
    temporaryAddress!: string;

    timezone!: string;

    @IsOptional()
    @IsNumber()
    numberAdults?: number;

    @IsOptional()
    @IsNumber()
    numberChildren?: number;

    @IsOptional()
    @IsNumber()
    numberPets?: number;

    @IsOptional()
    @IsString()
    petTypes?: string;

    @IsNotEmpty()
    @IsString()
    lossDate!: string;

    @IsString()
    @IsNotEmpty()
    aleLimit!: string;

    @IsString()
    @IsNotEmpty()
    normalDailyExpense!: string;

    @IsOptional()
    @IsString()
    relocationLength?: string;

    @IsOptional()
    @IsString()
    specialNeeds?: string;

    @IsOptional()
    @IsString()
    insuranceCompany?: string;

    @IsOptional()
    @IsString()
    claimNumber?: string;

    @IsOptional()
    @IsString()
    submitted?: string;

    @IsOptional()
    @IsString()
    in_review?: string;

    @IsOptional()
    @IsString()
    audited?: string;

    @IsNotEmpty()
    @IsString()
    createdAt!: string;

    @IsNotEmpty()
    @IsString()
    updatedAt!: string;

    @IsNotEmpty()
    @IsString()
    typeOfPolicy!: string;

    @IsNotEmpty()
    @IsString()
    lossReason!: string;

    @IsOptional()
    @IsArray()
    approvedReceiptCategories?: string[];

    @IsOptional()
    @IsString()
    approvedReceiptStartDate?: string;

    @IsOptional()
    @IsString()
    lastAuditedDate?: string;

    @IsOptional()
    @IsString()
    firstAuditedDate?: string;

    @IsOptional()
    @IsString()
    approvedReceiptEndDate?: string;

    @IsOptional()
    @IsString()
    alcoholApproved?: string;

    @IsOptional()
    @IsString()
    alcoholLimitedTo?: string;

    @IsOptional()
    @IsString()
    excessiveSpending?: string;

    @IsOptional()
    @IsString()
    groceriesApproved?: string;

    @IsOptional()
    @IsString()
    unitemizedReceiptsApproved?: string;

    @IsOptional()
    @IsString()
    bankStatementsApproved?: string;

    @IsOptional()
    @IsNumber()
    approvedReceiptsMileRadius?: number;

    @IsOptional()
    @IsString()
    description?: string;
}

export class ClaimAddDto {
    @IsNotEmpty({ message: 'Salesforce Claim ID should not be empty' })
    @IsString({ message: 'Salesfoce Claim ID should be a string' })
    id!: string;
}

export class ClaimInviteDto {
    @IsNotEmpty()
    @IsString()
    policy_holder_id!: string;

    @IsNotEmpty()
    @IsString()
    user_id!: string;

    @IsNotEmpty()
    @IsString()
    admin_id!: string;
}

export class ExpenseRangesDto {
    @IsNotEmpty()
    @IsObject()
    approved!: {
        min: number;
        max: number;
    };

    @IsNotEmpty()
    @IsObject()
    submitted!: {
        min: number;
        max: number;
    };
}
