import { useEffect, useState } from 'react';
import { useStoreActions, useStoreState } from '../../../../store';
import { formatCurrency } from '../../../../utils/currencyFormat';
import { ExpenseMetadataDto } from '../../../../store/expense/dto/ExpenseDto';
import Spinner from '../../../app/shared/Spinner';

interface Props {
    claimId: string;
}

const Stats: React.FC<Props> = ({ claimId }) => {
    const [metadata, setMetadata] = useState<ExpenseMetadataDto>();

    const { list } = useStoreState((state) => state.expense);

    const { getMetadata } = useStoreActions((actions) => actions.claim);

    useEffect(() => {
        list.value && getMetadata(claimId).then(setMetadata);
    }, [claimId, getMetadata, list.value]);

    return (
        <div className="claim-expenses-stats">
            {!metadata && <Spinner type="skeleton" />}

            {metadata && (
                <>
                    <div className="claim-expenses-stats-cell">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>Total</b>
                            </span>
                            <span className="count">
                                <b>{formatCurrency(metadata.grandTotal)}</b>
                            </span>
                            <span className="color-secondary darker">
                                <b>{metadata.countTotal}</b>
                            </span>
                        </div>
                    </div>
                    <div className="claim-expenses-stats-cell">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>Submitted</b>
                            </span>
                            <span className={'count'}>
                                <b>{formatCurrency(metadata.totals.submitted || 0)}</b>
                            </span>
                            <span className="color-secondary darker">
                                <b>{metadata.counts.submitted || 0}</b>
                            </span>
                        </div>
                    </div>
                    <div className="claim-expenses-stats-cell">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>In Review</b>
                            </span>
                            <span className={'count'}>
                                <b>{formatCurrency(metadata.totals.inReview || 0)}</b>
                            </span>
                            <span className="color-secondary darker">
                                <b>{metadata.counts.inReview || 0}</b>
                            </span>
                        </div>
                    </div>
                    <div className="claim-expenses-stats-cell no-border">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>Audited</b>
                            </span>
                            <span className={'count'}>
                                <b>{formatCurrency(metadata.totals.audited || 0)}</b>
                            </span>
                            <span className="color-secondary darker">
                                <b>{metadata.counts.audited || 0}</b>
                            </span>
                        </div>
                    </div>
                    <div className="claim-expenses-stats-cell no-border">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>Approved</b>
                            </span>
                            <span className={'count'}>
                                <b>{formatCurrency(metadata.approved_audited_amount || 0)}</b>
                            </span>
                        </div>
                    </div>
                    <div className="claim-expenses-stats-cell">
                        <div className="claim-expenses-stats-cell-info">
                            <span className="color-secondary darker">
                                <b>Declined</b>
                            </span>
                            <span className={'count'}>
                                <b>{formatCurrency(metadata.declined_amount || 0)}</b>
                            </span>
                        </div>
                    </div>
                </>
            )}
        </div>
    );
};
export default Stats;
