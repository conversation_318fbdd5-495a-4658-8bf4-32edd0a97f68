import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Col, Row } from 'antd';
import { plainToInstance } from 'class-transformer';
import { endOfDay, format, parseISO, startOfDay } from 'date-fns';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import {
    ReportCreateDto,
    ReportRequestFormDto,
    ReportDto,
} from '../../../../store/report/dto/ReportDto';
import {
    API_DATE_TIME_FORMAT,
    convertLocalDateToUTC,
    convertUtcToSpecifiedTimeZone,
    DATE_ONLY,
} from '../../../../utils/dateFormat';
import FlashMessages from '../../../app/FlashMessages';
import Modal from '../../../app/shared/Modal';
import Spinner from '../../../app/shared/Spinner';
import TransformContainer from '../../../app/shared/TransformContainer';
import DatePickerRange from '../../../app/shared/form/DatePickerRange';
import DatePicker from '../../../app/shared/form/DatePicker';
import 'react-datepicker/dist/react-datepicker.css';
import Form from '../../../app/shared/form/Form';
import ReportDetails from './Details';
import TextField from '../../../app/shared/form/TextField';
import { enUS } from 'date-fns/locale';

interface Props {
    show: boolean;
    onClose: VoidFunction;
    onSuccess: VoidFunction;
    claim: ClaimDto;
}

const ReportRequest: React.FC<Props> = ({ show, onClose, onSuccess, claim }) => {
    const [isFirstStep, setIsFirstStep] = useState<boolean>(true);
    const [creatingReport, setCreatingReport] = useState<boolean>(false);
    const [reportHtml, setReportHtml] = useState<string>();
    const [tempReport, setTempReport] = useState<ReportDto>();
    const [sendAdjuster, setSendAdjuster] = useState(false);
    const [tempFormFields, setTempFormFields] = useState<ReportCreateDto>();
    console.log('claimclaimclaimclaim', claim);
    const { add, sendToAdjuster, getReportRequest } = useStoreActions((actions) => actions.report);
    // Default values
    const defaultStartDate = claim.firstAuditedDate
        ? new Date(
              format(
                  convertUtcToSpecifiedTimeZone(claim.firstAuditedDate, claim.timezone),
                  DATE_ONLY,
                  {
                      locale: enUS,
                  },
              ),
          )
        : startOfDay(new Date());
    const defaultApprovedStartDate = claim.approvedReceiptStartDate
        ? startOfDay(parseISO(claim.approvedReceiptStartDate))
        : startOfDay(new Date());
    const defaultEndDate = claim.lastAuditedDate
        ? new Date(
              format(
                  convertUtcToSpecifiedTimeZone(claim.lastAuditedDate, claim.timezone),
                  DATE_ONLY,
                  {
                      locale: enUS,
                  },
              ),
          )
        : endOfDay(new Date());
    const defaultApprovedEndDate = claim.approvedReceiptEndDate
        ? endOfDay(parseISO(claim.approvedReceiptEndDate))
        : endOfDay(new Date());
    const methods = useForm<ReportRequestFormDto>({
        resolver: classValidatorResolver(ReportRequestFormDto),
        defaultValues: {
            claim_id: claim.id,
            dateRange: [defaultStartDate, defaultEndDate],
            approved_receipt_start_date: defaultApprovedStartDate,
            approved_receipt_end_date: defaultApprovedEndDate,
            previous_report_sent: '',
            additional_comments: '',
        },
    });

    const firstStepSubtitle = `After entering the report details, click "Send Report Request" to generate a new report. The report will display data matching your selected criteria`;
    const secondStepSubtitle =
        'Here is your report! Click "Create Report" to store it in the Reports page as a manual report. If you would like to send this to the assigned adjuster, check the box next to the name. Click "Generate New" to make a new report or to back out click "Discard"!';

    const onSubmit = async (fields) => {
        console.log('fields', fields);
        try {
            const startDate = format(startOfDay(fields.dateRange[0]), API_DATE_TIME_FORMAT);
            const approvedRecieptStartDate = format(
                startOfDay(fields.approved_receipt_start_date),
                API_DATE_TIME_FORMAT,
            );
            const approvedRecieptEndDate = format(
                endOfDay(fields.approved_receipt_end_date),
                API_DATE_TIME_FORMAT,
            );
            const endDate = format(endOfDay(fields.dateRange[1]), API_DATE_TIME_FORMAT);
            const dto = plainToInstance(ReportCreateDto, {
                ...fields,
                start_date: startDate,
                approved_receipt_end_date: approvedRecieptEndDate,
                approved_receipt_start_date: approvedRecieptStartDate,
                end_date: endDate,
                claim_id: claim.id,
            });
            setTempFormFields(dto);
            const reportPreview: ReportDto = await getReportRequest(dto);

            if (reportPreview === null) {
                FlashMessages.error('Criteria Not Met for Any Expenses');
                return;
            }
            setReportHtml(reportPreview.html);
            FlashMessages.success('');
            onClose();
            onSuccess();
            // setIsFirstStep(false);
            // setTempReport(reportPreview);
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Generate Report');
        }
    };

    const { setValue } = useForm();

    const handleDateChange = (dateRange) => {
        // Since dateRange is an array of [startDate, endDate]
        const [start, end] = dateRange;
        const startDate = convertLocalDateToUTC(startOfDay(start).toISOString(), claim.timezone);
        const endDate = convertLocalDateToUTC(endOfDay(end).toISOString(), claim.timezone);
        setValue('dateRange', [startDate, endDate]);
    };

    const onCreate = async (data?: ReportCreateDto) => {
        if (!data) {
            return;
        }
        setCreatingReport(true);
        const newReport = await add(data);
        setCreatingReport(false);
        if (sendAdjuster) {
            try {
                newReport?.id && (await sendToAdjuster(newReport.id));
            } catch {
                FlashMessages.error('Failed to Send Report');
            }
        }

        onSuccess();
    };

    const onGenerateNew = async () => {
        if (!tempReport) {
            return;
        }
        try {
            setIsFirstStep(true);
            setTempReport(undefined);
        } catch (err: any) {
            FlashMessages.error('Failed to Reset');
            console.error(err.message);
        }
    };

    const onDiscard = async () => {
        onClose();
    };

    return (
        <Modal
            onClose={onClose}
            show={show}
            title="Report Request"
            className="claim-reports-add"
            subtitle={isFirstStep ? firstStepSubtitle : secondStepSubtitle}
            sideContent={
                isFirstStep ? undefined : (
                    <TransformContainer>
                        {!reportHtml && <Spinner type="skeleton" />}
                        {reportHtml && (
                            <div
                                className="claim-reports-preview"
                                dangerouslySetInnerHTML={{ __html: reportHtml }}
                            />
                        )}
                    </TransformContainer>
                )
            }
            sideContentProps={{ size: 'large' }}
        >
            <Form methods={methods} onSubmit={methods.handleSubmit(onSubmit)}>
                {isFirstStep && (
                    <Row>
                        <Col span={24}>
                            <DatePickerRange
                                name="dateRange"
                                label="Receipt Date Range"
                                // min={createDateWithBrowserTimezone(claim.lossDate)} // Local time for display
                                // max={new Date()} // Local time for display
                                onChange={handleDateChange} // Handle the change event
                            />
                        </Col>
                        <Col span={24}>
                            <DatePicker
                                name="approved_receipt_start_date"
                                label="Approved Receipt Start Date"
                                dateFormat="MMMM d, yyyy"
                                onChange={handleDateChange} // Handle the change event
                            />
                        </Col>
                        <Col span={24}>
                            <DatePicker
                                name="approved_receipt_end_date"
                                label="Approved Receipt End Date"
                                dateFormat="MMMM d, yyyy"
                                // min={createDateWithBrowserTimezone(claim.lossDate)} // Local time for display
                                // max={new Date()} // Local time for display
                                onChange={handleDateChange} // Handle the change event
                            />
                        </Col>
                        <Col span={24}>
                            <TextField
                                label="Has a previous report been sent out?"
                                name="previous_report_sent"
                                placeholder="No"
                            />
                        </Col>
                        <Col span={24}>
                            <TextField
                                label="Additional Comments"
                                name="additional_comments"
                                placeholder="N/A"
                            />
                        </Col>
                        <Col span={24}>
                            <Button
                                className="btn-primary"
                                size="large"
                                htmlType="submit"
                                loading={methods.formState.isSubmitting}
                            >
                                Send Report Request
                            </Button>
                        </Col>
                    </Row>
                )}
                {!isFirstStep && tempReport && (
                    <>
                        <ReportDetails
                            report={tempReport}
                            claim={claim}
                            onToggleSend={() => setSendAdjuster(!sendAdjuster)}
                            canSend
                        />

                        <div className="actions">
                            <Button
                                className="btn-primary"
                                size="large"
                                type="primary"
                                loading={creatingReport}
                                onClick={() => onCreate(tempFormFields)}
                                block
                            >
                                Create Report
                            </Button>
                            <Button
                                className="btn-secondary"
                                size="large"
                                disabled={methods.formState.isSubmitting}
                                onClick={() => onGenerateNew()}
                                block
                            >
                                Generate New
                            </Button>
                            <Button
                                className="btn-text"
                                size="large"
                                type="link"
                                disabled={methods.formState.isSubmitting}
                                onClick={() => onDiscard()}
                                block
                            >
                                Discard
                            </Button>
                        </div>
                    </>
                )}
            </Form>
        </Modal>
    );
};
export default ReportRequest;
