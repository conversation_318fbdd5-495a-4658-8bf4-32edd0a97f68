import { classValidatorResolver } from '@hookform/resolvers/class-validator/dist/class-validator';
import { Button, Space } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { endOfToday, format } from 'date-fns';
import { useForm } from 'react-hook-form';
import { useStoreActions } from '../../../../store';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { ExpenseDto, ExpenseUpdateDto } from '../../../../store/expense/dto/ExpenseDto';
import { toRawStatusMap } from '../../../../store/expense/mappers';
import { ExpenseCategory } from '../../../../types';
import {
    API_DATE_TIME_FORMAT,
    convertUtcToSpecifiedTimeZone,
    DATE_AT_TIME_MASK,
    DATE_AT_TIME_PLACEHOLDER,
    DATE_ONLY,
    DATE_TIME_DISPLAY_FORMAT,
} from '../../../../utils/dateFormat';
import FlashMessages from '../../../app/FlashMessages';
import Dialog from '../../../app/shared/Dialog';
import Modal from '../../../app/shared/Modal';
import RcDatePicker from '../../../app/shared/form/DatePicker';
import Form from '../../../app/shared/form/Form';
import SelectBox from '../../../app/shared/form/SelectBox';
import TextField from '../../../app/shared/form/TextField';
import ReceiptPreview from './ReceiptPreview';
import {
    AuditedType,
    ExpenseStatus,
    getAuditedTypeByReviewsCommentType,
    getInitialReviewersComments,
    getReadableExpenseCategory,
    getReviewerCommentByStatus,
    getReviewerCommentsByStatusToSelectBox,
    getReviewerCommentValueByStatus,
    isOtherField,
} from './utils';
import InputMask from 'react-input-mask';
import { ReviewersCommentsType } from '../../../../common/ReviewersCommentsType';
import { RawExpenseStatus } from '../../../../store/expense/types/Expense';
import { SelectBoxOption } from '../../../app/shared/form/MultiSelectBox';
import ModalNavigationWrapper from './ModalNavigationWrapper';
import { ReactComponent as DisableIcon } from '../../../../assets/icons/disable icon.svg';
import { enUS } from 'date-fns/locale';

interface Props {
    claim: ClaimDto;
    disableNav?: boolean;
    expense: ExpenseDto;
    onClose: VoidFunction;
    onNextExpense?: VoidFunction;
    onPrevExpense?: VoidFunction;
    onSuccess: VoidFunction;
    show: boolean;
    setFieldsData: (data: ExpenseUpdateDto) => void; // Add this line
}

const Categories = Object.values(ExpenseCategory).map((category) => ({
    label: getReadableExpenseCategory(category),
    value: category as string,
}));

const EditModal: React.FC<Props> = ({
    claim,
    disableNav,
    expense,
    onClose,
    onNextExpense,
    setFieldsData,
    onPrevExpense,
    onSuccess,
    show,
}) => {
    const { requestReceipt, update, view } = useStoreActions((actions) => actions.expense);
    const [showRequestReceiptDialog, setShowRequestReceiptDialog] = useState(false);
    const [requestReceiptDialogDisabled, setRequestReceiptDialogDisabled] = useState(false);
    const defaultDate = convertUtcToSpecifiedTimeZone(expense.date, claim.timezone);
    const [reviewersComments, setReviewersComments] = useState<SelectBoxOption[]>(
        getInitialReviewersComments(expense, toRawStatusMap[expense.status]),
    );
    const [auditedType, setAuditedType] = useState<AuditedType | undefined>(
        getAuditedTypeByReviewsCommentType(expense.reviewer_comment_type),
    );
    const [canChange, setCanChange] = useState(false);

    const methods = useForm<ExpenseUpdateDto>({
        resolver: classValidatorResolver(ExpenseUpdateDto),
        defaultValues: {
            date: defaultDate,
            category: expense.category,
            location: expense.location,
            vendor: expense.vendor,
            submitted_amount: expense.submittedAmount?.toFixed(2),
            approved_amount: expense.approvedAmount?.toFixed(2),
            declined_amount: expense.declinedAmount?.toFixed(2),
            notes: expense.notes || undefined,
            status: toRawStatusMap[expense.status],
            reviewer_comment:
                isOtherField(expense.reviewer_comment_type) && expense.reviewerComment
                    ? expense.reviewerComment
                    : getReviewerCommentValueByStatus(
                          toRawStatusMap[expense.status],
                          expense.reviewer_comment_type,
                          auditedType,
                      ),
            reviewer_comment_type: getReviewerCommentByStatus(
                toRawStatusMap[expense.status],
                expense,
                expense.reviewer_comment_type,
            ),
        },
    });

    const currentReviewerCommentType = methods.watch('reviewer_comment_type');

    const onSubmit = async (fields) => {
        fields.date = format(fields.date, API_DATE_TIME_FORMAT);
        fields.approved_amount = fields.approved_amount?.toString();
        fields.submitted_amount = fields.submitted_amount?.toString();
        if (!fields.reviewer_comment && isOtherField(fields.reviewer_comment_type)) {
            delete fields.reviewer_comment;
        }
        if (expense.status === ExpenseStatus.SUBMITTED) {
            delete fields.reviewer_comment;
        }
        if (
            fields.status === RawExpenseStatus.AUDITED &&
            +(+fields.approved_amount + +fields.declined_amount).toFixed(2) !==
                expense?.submittedAmount
        ) {
            methods.setError('approved_amount', {
                message: 'Ensure that both fields are filled out',
            });
            methods.setError('declined_amount', {
                message: 'Ensure that both fields are filled out',
            });
            return;
        }
        try {
            delete fields.declined_amount;
            await update({ id: expense.id, dto: fields }).then((r) => {
                setFieldsData(r);
            });
            FlashMessages.success('Expense Updated');
            onSuccess();
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Update Expense');
        }
    };

    const handleRequestReceipt = async () => {
        setRequestReceiptDialogDisabled(true);
        try {
            await requestReceipt(expense.id);
            FlashMessages.success('Receipt Requested');
        } catch (err: any) {
            console.error(err.message);
            FlashMessages.error('Failed to Request Receipt');
        } finally {
            setRequestReceiptDialogDisabled(false);
            setShowRequestReceiptDialog(false);
        }
    };

    const handleApproveAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        setCanChange(false);
        methods.clearErrors();
        const amountToNumber = parseFloat(value);

        if (amountToNumber <= 0) {
            methods.setValue('approved_amount', '0');
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }
        methods.setValue('approved_amount', value);
        methods.setValue('declined_amount', (expense.submittedAmount - amountToNumber).toFixed(2));

        if (isOtherField(currentReviewerCommentType)) {
            return;
        }

        if (!canChange) {
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
        }
        setCanChange(true);
    };

    const handleDeclinedAmount = (value: string) => {
        if (!expense.submittedAmount) {
            return;
        }
        setCanChange(false);
        methods.clearErrors();
        const amountToNumber = parseFloat(value);
        if (amountToNumber >= expense.submittedAmount) {
            methods.setValue('declined_amount', expense.submittedAmount.toFixed(2));
            methods.setValue('approved_amount', '0');
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.DECLINED,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.DECLINED,
                ReviewersCommentsType.NOT_APPROVED_ILLEGIBLE,
            );
            setCanChange(false);
            return;
        }

        // if (amountToNumber <= 0 || !amountToNumber) {
        //     methods.setValue('declined_amount', (0).toFixed(2));
        //     methods.setValue('approved_amount', expense.submittedAmount.toFixed(2));
        //     setReviewersComments(getReviewerCommentsByStatusToSelectBox(RawExpenseStatus.AUDITED));
        //     handleReviewersCommentsChange(undefined, ReviewersCommentsType.APPROVED_BREAKFAST);
        //     setCanChange(false);
        //     return;
        // }
        methods.setValue('declined_amount', value);
        methods.setValue('approved_amount', (expense.submittedAmount - amountToNumber).toFixed(2));

        if (isOtherField(currentReviewerCommentType)) {
            return;
        }

        if (!canChange) {
            setReviewersComments(
                getReviewerCommentsByStatusToSelectBox(
                    RawExpenseStatus.AUDITED,
                    AuditedType.PARTIAL,
                ),
            );
            handleReviewersCommentsChange(
                AuditedType.PARTIAL,
                ReviewersCommentsType.APPROVED_PARTIAL,
            );
            setCanChange(true);
        }
    };

    const handleReviewersCommentsChange = (
        auditedType?: AuditedType,
        commentType?: ReviewersCommentsType,
    ) => {
        setAuditedType(auditedType);
        methods.setValue(
            'reviewer_comment_type',
            getReviewerCommentByStatus(RawExpenseStatus.AUDITED, expense, commentType, auditedType),
        );
    };

    // change reviewer comment after reviewer comment type is changed or auditedType
    useEffect(() => {
        if (
            isOtherField(currentReviewerCommentType) &&
            expense.reviewer_comment_type !== currentReviewerCommentType
        ) {
            methods.setValue('reviewer_comment', '');
            return;
        }

        if (isOtherField(currentReviewerCommentType)) {
            methods.setValue(
                'reviewer_comment',
                expense.reviewerComment
                    ? expense.reviewerComment
                    : getReviewerCommentValueByStatus(
                          toRawStatusMap[expense.status],
                          currentReviewerCommentType,
                          auditedType,
                      ),
            );
            return;
        }
        methods.setValue(
            'reviewer_comment',
            getReviewerCommentValueByStatus(
                toRawStatusMap[expense.status],
                currentReviewerCommentType,
                auditedType,
            ),
        );

        methods.clearErrors('approved_amount');
        methods.clearErrors('declined_amount');
    }, [currentReviewerCommentType, auditedType]); // eslint-disable-line react-hooks/exhaustive-deps

    const Header = () => (
        <Space align="baseline">
            <div className="title">
                <div className="vendor">Edit Expense</div>
            </div>
            <div
                className={classNames({
                    'font-small': true,
                    pill: true,
                    info: expense.status === ExpenseStatus.SUBMITTED,
                    warning: expense.status === ExpenseStatus.IN_REVIEW,
                    success: expense.status === ExpenseStatus.AUDITED,
                })}
            >
                {expense.status}
            </div>
        </Space>
    );

    useEffect(() => {
        view(expense.id);
    }, [expense.id, view]);

    useEffect(() => {
        methods.setValue('date', defaultDate);
        methods.setValue('category', expense.category);
        methods.setValue('location', expense.location);
        methods.setValue('vendor', expense.vendor);
        methods.setValue('submitted_amount', (expense.submittedAmount || 0).toFixed(2));
        methods.setValue('approved_amount', (expense.approvedAmount || 0).toFixed(2));
        methods.setValue('declined_amount', (expense.declinedAmount || 0).toFixed(2));
        methods.setValue('notes', expense.notes || '');
        methods.setValue('status', toRawStatusMap[expense.status]);
        methods.setValue(
            'reviewer_comment',
            isOtherField(expense.reviewer_comment_type) && expense.reviewerComment
                ? expense.reviewerComment
                : getReviewerCommentValueByStatus(
                      toRawStatusMap[expense.status],
                      expense.reviewer_comment_type,
                      auditedType,
                  ) || '',
        );
        methods.setValue(
            'reviewer_comment_type',
            getReviewerCommentByStatus(
                toRawStatusMap[expense.status],
                expense,
                expense.reviewer_comment_type,
            ),
        );
    }, [expense, methods]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        setAuditedType(getAuditedTypeByReviewsCommentType(expense.reviewer_comment_type));
        setReviewersComments(getInitialReviewersComments(expense, toRawStatusMap[expense.status]));
    }, [expense.id]); // eslint-disable-line react-hooks/exhaustive-deps

    return (
        <>
            <Modal
                show={show}
                onClose={onClose}
                title={<Header />}
                sideContent={
                    <ModalNavigationWrapper
                        onPrev={() => !!onPrevExpense && onPrevExpense()}
                        disableNav={disableNav}
                        onNext={() => !!onNextExpense && onNextExpense()}
                        expense={expense}
                    >
                        <ReceiptPreview
                            photoUrl={expense.imagePath}
                            canRequestReceipt
                            onRequestReceipt={() => setShowRequestReceiptDialog(true)}
                        />
                    </ModalNavigationWrapper>
                }
            >
                <Form
                    className="claim-expenses-add-form"
                    methods={methods}
                    onSubmit={methods.handleSubmit(onSubmit)}
                >
                    <TextField label="Vendor Name" name="vendor" placeholder="Vendor" />
                    <SelectBox
                        label="Category"
                        name="category"
                        placeholder={getReadableExpenseCategory(expense.category)}
                        options={Categories}
                    />
                    <TextField label="Location" name="location" placeholder="Location" />
                    <div className="claim-expenses-attentionBlock">
                        <TextField
                            label="Attention"
                            name="Excessive Spending"
                            value={(() => {
                                const isExcessiveSpending =
                                    (claim.excessiveSpending === '$100>' ||
                                        claim.excessiveSpending?.toLowerCase() === 'yes') &&
                                    (expense?.submittedAmount ?? 0) >= 100;

                                const expenseDateStr = new Date(
                                    format(
                                        convertUtcToSpecifiedTimeZone(expense.date, claim.timezone),
                                        DATE_ONLY,
                                        { locale: enUS },
                                    ),
                                )
                                    .toISOString()
                                    .split('T')[0];

                                const approvedStartDateStr = claim.approvedReceiptStartDate
                                    ? new Date(claim.approvedReceiptStartDate)
                                          .toISOString()
                                          .split('T')[0]
                                    : null;

                                const approvedEndDateStr = claim.approvedReceiptEndDate
                                    ? new Date(claim.approvedReceiptEndDate)
                                          .toISOString()
                                          .split('T')[0]
                                    : null;

                                const isTimePeriod =
                                    (approvedStartDateStr &&
                                        expenseDateStr < approvedStartDateStr) ||
                                    (approvedEndDateStr && expenseDateStr > approvedEndDateStr);

                                if (isExcessiveSpending) return 'Excessive Spending';
                                if (isTimePeriod) return 'Time Period';
                                return '';
                            })()}
                            placeholder=""
                            disabled
                        />
                        <DisableIcon
                            className="claim-expenses-disableIcon"
                            width={24}
                            height={24}
                            viewBox="0 0 2084 2084"
                            style={{ display: 'block' }}
                        />
                    </div>
                    <RcDatePicker
                        name="date"
                        label="Expense Date"
                        maxDate={endOfToday()}
                        showTimeSelect={true}
                        dateFormat={DATE_TIME_DISPLAY_FORMAT}
                        placeholder={DATE_AT_TIME_PLACEHOLDER}
                        customInput={
                            <InputMask
                                mask={DATE_AT_TIME_MASK}
                                maskPlaceholder={DATE_AT_TIME_PLACEHOLDER}
                            />
                        }
                    />
                    <TextField
                        label="Submitted Amount"
                        name="submitted_amount"
                        type="number"
                        step={0.01}
                        placeholder="Submitted Amount"
                        contentBefore="$"
                    />

                    {expense.status === ExpenseStatus.AUDITED && (
                        <>
                            <TextField
                                name="approved_amount"
                                label="Approved Amount"
                                type="number"
                                step={0.01}
                                max={expense.submittedAmount}
                                min={0}
                                contentBefore="$"
                                onChange={(value) => handleApproveAmount(value)}
                            />
                            <TextField
                                name="declined_amount"
                                label="Declined Amount"
                                type="number"
                                step={0.01}
                                max={expense.submittedAmount}
                                min={0}
                                contentBefore="$"
                                onChange={(value) => handleDeclinedAmount(value)}
                            />
                        </>
                    )}
                    <TextField label="Note" name="notes" placeholder="Insert Expense Note" />

                    {[ExpenseStatus.AUDITED, ExpenseStatus.IN_REVIEW].includes(expense.status) && (
                        <SelectBox
                            name="reviewer_comment_type"
                            label="Reviewer Comment"
                            options={reviewersComments}
                            onOptionChange={(option) =>
                                methods.setValue(
                                    'reviewer_comment_type',
                                    option.value as ReviewersCommentsType,
                                )
                            }
                        />
                    )}
                    {isOtherField(
                        methods.getValues('reviewer_comment_type') as ReviewersCommentsType,
                    ) && <TextField name="reviewer_comment" type="textarea" />}

                    <Button
                        className="btn-primary"
                        loading={methods.formState.isSubmitting}
                        htmlType="submit"
                        size="large"
                    >
                        Update Expense
                    </Button>
                </Form>
            </Modal>
            <Dialog
                show={showRequestReceiptDialog}
                onCancel={() => setShowRequestReceiptDialog(false)}
                onOk={() => handleRequestReceipt()}
                disabled={requestReceiptDialogDisabled}
            />
        </>
    );
};
export default EditModal;
