import { MenuItemType } from 'antd/es/menu/hooks/useItems';
import { ColumnsType } from 'antd/es/table';
import classNames from 'classnames';
import { canAccess } from '../../../../common/canAccess';
import { ClaimDto } from '../../../../store/claim/dto/ClaimDto';
import { InvoiceDto } from '../../../../store/invoice/dto/InvoiceDto';
import { invoiceStatusMap } from '../../../../store/invoice/type/Invoice';
import { UserRole } from '../../../../store/user/types/User';
import { Resource } from '../../../../types';
import ContextMenu from '../../../app/shared/ContextMenu';
import DataGrid from '../../../app/shared/DataGrid';
import Spinner from '../../../app/shared/Spinner';
import { InvoiceActions, adjusterActionMenuItems, adminInvoiceActionMenuItems } from './utils';
import { format } from 'date-fns';
import { convertUtcToSpecifiedTimeZone } from '../../../../utils/dateFormat';

interface Props {
    list: Resource<InvoiceDto[]>;
    onActionClick: (item: MenuItemType | undefined, invoice: InvoiceDto) => void;
    actionLoading: boolean | string;
    claimArchived: boolean;
    claim: ClaimDto;
}

export const List: React.FC<Props> = ({
    list,
    onActionClick,
    actionLoading: actionsLoading,
    claimArchived,
    claim,
}) => {
    const columns: ColumnsType<InvoiceDto> = [
        {
            title: 'Invoice #',
            render: ({ invoice_no }: InvoiceDto) => <b>{invoice_no}</b>,
        },
        {
            title: 'Receipt Dates',
            render: (invoice: InvoiceDto) => (
                <div
                    className="flex-between cursor"
                    onClick={() => {
                        onActionClick(
                            { label: InvoiceActions.VIEW, key: InvoiceActions.VIEW },
                            invoice,
                        );
                    }}
                >
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(invoice.startDate, claim.timezone),
                                'MM/dd/yy',
                            )}
                        </b>
                    </span>
                    <span>
                        <b> - </b>
                    </span>
                    <span>
                        <b>
                            {format(
                                convertUtcToSpecifiedTimeZone(invoice.endDate, claim.timezone),
                                'MM/dd/yy',
                            )}
                        </b>
                    </span>
                </div>
            ),
        },
        {
            title: '# of Months Billed',
            render: ({ no_of_months_billed }: InvoiceDto) => <b>{no_of_months_billed}</b>,
        },
        {
            title: (
                <div style={{ whiteSpace: 'pre-line', textAlign: 'center' }}>
                    <span style={{ fontSize: 10 }}>(Limited)</span>
                    {'\n'}# of Months Billed
                </div>
            ),
            render: ({ no_of_limited_months_billed }: InvoiceDto) => (
                <b>{no_of_limited_months_billed}</b>
            ),
        },
        {
            title: (
                <div style={{ whiteSpace: 'pre-line', textAlign: 'center' }}>
                    <span style={{ fontSize: 10 }}>(Unlimited)</span>
                    {'\n'}# of Months Billed
                </div>
            ),
            render: ({ no_of_unlimited_months_billed }: InvoiceDto) => (
                <b>{no_of_unlimited_months_billed}</b>
            ),
        },
        {
            title: 'Type',
            render: ({ type }: InvoiceDto) => <b>{type}</b>,
        },
        {
            title: 'Date Created',
            render: ({ ready_for_download_at }: InvoiceDto) => (
                <b>
                    {format(
                        convertUtcToSpecifiedTimeZone(ready_for_download_at, claim.timezone),
                        'MM/dd/yy',
                    )}
                </b>
            ),
        },
        {
            title: 'Status',
            align: 'center',
            width: 120,
            render: ({ status }: InvoiceDto) => {
                return (
                    <div
                        className={classNames(
                            'pill',
                            { warning: status === 'open' },
                            { info: status === 'held' },
                            { primary: status === 'uploadedtoqb' },
                        )}
                    >
                        {invoiceStatusMap[status]}
                    </div>
                );
            },
        },
        {
            title: 'Invoice Amount',
            render: ({ invoice_amount }: InvoiceDto) => <b>${invoice_amount}</b>,
        },
        {
            title: 'Actions',
            width: 90,
            render: (invoice: InvoiceDto) =>
                actionsLoading === invoice.id ? (
                    <div className="flex-center">
                        <Spinner />
                    </div>
                ) : (
                    <div className="flex-center">
                        {!claimArchived && (
                            <ContextMenu
                                // className={classNames({ disabled: invoice.status === 'preparing' })}
                                items={
                                    canAccess([UserRole.ADMIN, UserRole.SUPER_ADMIN])
                                        ? adminInvoiceActionMenuItems
                                        : adjusterActionMenuItems
                                }
                                onItemClick={(item) => onActionClick(item, invoice)}
                            />
                        )}
                    </div>
                ),
        },
    ];

    return <DataGrid<InvoiceDto> list={list} columns={columns} />;
};
export default List;
