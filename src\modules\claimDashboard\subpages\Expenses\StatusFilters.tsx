import { Button } from 'antd';
import { ExpenseStatusFilter, ExpenseStatusFilterType } from './utils';
import classNames from 'classnames';

interface Props {
    status?: ExpenseStatusFilterType;
    onFilterChange: (status: ExpenseStatusFilterType) => void;
}

const StatusFilter: React.FC<Props> = ({ status, onFilterChange }) => {
    const isActive = (buttonStatus: ExpenseStatusFilterType) => buttonStatus === status;

    return (
        <div className="claim-expenses-statuses">
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.ALL),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.ALL),
                })}
                type={!isActive(ExpenseStatusFilter.ALL) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.ALL)}
            >
                <b>All</b>
            </Button>
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.RECENT),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.RECENT),
                })}
                type={!isActive(ExpenseStatusFilter.RECENT) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.RECENT)}
            >
                <b>Recent</b>
            </Button>
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.SUBMITTED),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.SUBMITTED),
                })}
                type={!isActive(ExpenseStatusFilter.SUBMITTED) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.SUBMITTED)}
            >
                <b>Submitted</b>
            </Button>
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.IN_REVIEW),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.IN_REVIEW),
                })}
                type={!isActive(ExpenseStatusFilter.IN_REVIEW) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.IN_REVIEW)}
            >
                <b>In Review</b>
            </Button>
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.AUDITED),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.AUDITED),
                })}
                type={!isActive(ExpenseStatusFilter.AUDITED) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.AUDITED)}
            >
                <b>Audited</b>
            </Button>
            <Button
                className={classNames({
                    'btn-text': !isActive(ExpenseStatusFilter.ARCHIVED),
                    'btn-secondary btn-tab': isActive(ExpenseStatusFilter.ARCHIVED),
                })}
                type={!isActive(ExpenseStatusFilter.ARCHIVED) ? 'link' : undefined}
                onClick={() => onFilterChange(ExpenseStatusFilter.ARCHIVED)}
            >
                <b>Archived</b>
            </Button>
        </div>
    );
};
export default StatusFilter;
