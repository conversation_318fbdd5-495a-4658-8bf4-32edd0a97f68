#root,
body,
html {
    height: 100%;
}

body {
    background-color: $color-background;
    color: $color-text;
    font-family: $font-family-primary, sans-serif;
    font-size: $font-size;
    font-weight: $font-weight-regular;
    line-height: $line-height;
    text-align: left;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

button {
    align-items: center !important;
    color: $color-text !important;
    display: flex !important;
    font-size: 14px !important;
    font-weight: $font-weight-bold !important;
    justify-content: center !important;
    height: fit-content !important;
    letter-spacing: 1.25px;
    line-height: 130% !important;
    padding: 14px 16px !important;
    transition: all 0.3s ease !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: $color-heading;
    font-family: $font-family-heading, sans-serif;
    font-weight: $font-weight-semi-bold;
    line-height: 1.1;
}

h1 {
    font-size: 44px;
    line-height: 52px;

    @include breakpoint(sm) {
        font-size: 36px;
        line-height: 44px;
    }
}

h2 {
    font-size: 36px;
    line-height: 44px;

    @include breakpoint(sm) {
        font-size: 32px;
        line-height: 40px;
    }
}

h3 {
    font-size: 32px;
    line-height: 40px;

    @include breakpoint(sm) {
        font-size: 28px;
        line-height: 36px;
    }
}

h4 {
    font-size: 28px;
    line-height: 36px;

    @include breakpoint(sm) {
        font-size: 24px;
        line-height: 32px;
    }
}

h5 {
    font-size: 24px;
    line-height: 32px;

    @include breakpoint(sm) {
        font-size: 20px;
        line-height: 28px;
    }
}

h6 {
    font-size: 20px;
    line-height: 28px;

    @include breakpoint(sm) {
        font-size: 16px;
        line-height: 24px;
    }
}

input {
    font-size: 16px !important;
}

input,
textarea,
button {
    font-family: $font-family-primary;
}

a {
    color: $color-link-default !important;
    text-decoration: underline;

    &:visited {
        color: $color-link-visited !important;
    }
    &:hover,
    &:active,
    &:focus {
        text-decoration: underline;
    }

    &.no-line {
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }
}

button {
    border-radius: 0 !important;
}

p {
    margin: $margin 0;
}

b,
bold,
strong {
    font-weight: $font-weight-semi-bold;
}

hr {
    background-color: $color-gray-5;
    border: 0 !important;
    display: block;
    height: 1px;
    margin: 24px auto;
    padding: 0;
    width: 100%;
}

input {
    color: $color-text !important;
}

svg {
    display: block;
}

#Layer_1 {
    width: 16px !important;
    height: 16px !important;
}
