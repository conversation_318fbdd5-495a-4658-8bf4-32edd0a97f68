import { useEffect } from 'react';
import { useStoreActions } from '../../../store';
import { NOTIFICATION_INTERVAL_PERIOD } from '../../notifications/utills';

const useUnreadChatListener = (id?: string) => {
    const { checkHasUnread } = useStoreActions((actions) => actions.chat);

    useEffect(() => {
        const interval = id
            ? setInterval(() => id && checkHasUnread(id), NOTIFICATION_INTERVAL_PERIOD)
            : undefined;

        if (!id) {
            clearInterval(interval);
            return;
        }

        id && checkHasUnread(id);

        return () => {
            clearInterval(interval);
        };
    }, [checkHasUnread, id]);
};
export default useUnreadChatListener;
